#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار المميزات المحسنة
Enhanced Features Test Suite

هذا الملف يختبر جميع المميزات الجديدة في الإصدار 2.0
"""

import sys
import os
import unittest
import tempfile
import json
import time
from datetime import datetime

# إضافة مسار المجلدات للاستيراد
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

class TestEnhancedFeatures(unittest.TestCase):
    """اختبار المميزات المحسنة"""
    
    def setUp(self):
        """إعداد الاختبارات"""
        self.temp_dir = tempfile.mkdtemp()
        print(f"\n🧪 بدء اختبار المميزات المحسنة...")
    
    def tearDown(self):
        """تنظيف بعد الاختبارات"""
        import shutil
        if os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir)
    
    def test_database_manager(self):
        """اختبار مدير قاعدة البيانات الأساسي"""
        print("📊 اختبار مدير قاعدة البيانات الأساسي...")
        
        try:
            from src.database.database_manager import DatabaseManager
            
            db_manager = DatabaseManager()
            db_manager.initialize_database()
            
            # اختبار الاتصال
            self.assertTrue(db_manager.connection is not None)
            print("✅ مدير قاعدة البيانات الأساسي يعمل بنجاح")
            
        except Exception as e:
            print(f"❌ فشل اختبار مدير قاعدة البيانات: {e}")
            self.fail(f"Database manager test failed: {e}")
    
    def test_advanced_database_manager(self):
        """اختبار مدير قاعدة البيانات المتقدم"""
        print("🔧 اختبار مدير قاعدة البيانات المتقدم...")
        
        try:
            from src.database.advanced_database_manager import create_advanced_database_manager
            
            # إنشاء ملف إعدادات مؤقت
            config_file = os.path.join(self.temp_dir, "test_db_config.json")
            config = {
                "database_type": "sqlite",
                "sqlite": {
                    "database_path": os.path.join(self.temp_dir, "test.db")
                }
            }
            
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f)
            
            # اختبار المدير المتقدم
            advanced_db = create_advanced_database_manager(config_file)
            
            # اختبار الاتصال
            self.assertTrue(advanced_db.test_connection())
            
            # اختبار إنشاء الجداول
            advanced_db.create_tables()
            
            print("✅ مدير قاعدة البيانات المتقدم يعمل بنجاح")
            
        except ImportError:
            print("⚠️ مدير قاعدة البيانات المتقدم غير متاح")
        except Exception as e:
            print(f"❌ فشل اختبار مدير قاعدة البيانات المتقدم: {e}")
    
    def test_web_server(self):
        """اختبار الخادم الويب"""
        print("🌐 اختبار الخادم الويب...")
        
        try:
            from src.web_server.web_app import create_web_app
            from src.database.database_manager import DatabaseManager
            
            # إعداد قاعدة بيانات للاختبار
            db_manager = DatabaseManager()
            db_manager.initialize_database()
            
            # إنشاء تطبيق الويب
            web_app = create_web_app(db_manager, port=5001)  # منفذ مختلف للاختبار
            
            self.assertIsNotNone(web_app)
            self.assertIsNotNone(web_app.app)
            
            print("✅ الخادم الويب يعمل بنجاح")
            
        except ImportError:
            print("⚠️ الخادم الويب غير متاح")
        except Exception as e:
            print(f"❌ فشل اختبار الخادم الويب: {e}")
    
    def test_cloud_sync_manager(self):
        """اختبار مدير المزامنة السحابية"""
        print("☁️ اختبار مدير المزامنة السحابية...")
        
        try:
            from src.cloud_sync.cloud_manager import create_cloud_sync_manager
            
            # إنشاء ملف إعدادات مؤقت
            config_file = os.path.join(self.temp_dir, "test_cloud_config.json")
            config = {
                "enabled": False,  # معطل للاختبار
                "providers": {
                    "google_drive": {"enabled": False},
                    "dropbox": {"enabled": False}
                }
            }
            
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f)
            
            # اختبار المدير
            cloud_manager = create_cloud_sync_manager(config_file)
            
            self.assertIsNotNone(cloud_manager)
            
            # اختبار حالة المزامنة
            status = cloud_manager.get_sync_status()
            self.assertIsInstance(status, dict)
            
            print("✅ مدير المزامنة السحابية يعمل بنجاح")
            
        except ImportError:
            print("⚠️ مدير المزامنة السحابية غير متاح")
        except Exception as e:
            print(f"❌ فشل اختبار مدير المزامنة السحابية: {e}")
    
    def test_telegram_manager(self):
        """اختبار مدير بوت Telegram"""
        print("🤖 اختبار مدير بوت Telegram...")
        
        try:
            from src.telegram_bot.telegram_manager import create_telegram_manager
            
            # إنشاء ملف إعدادات مؤقت
            config_file = os.path.join(self.temp_dir, "test_telegram_config.json")
            config = {
                "bot_token": "",  # فارغ للاختبار
                "enabled": False,
                "admin_chat_ids": [],
                "authorized_chat_ids": []
            }
            
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f)
            
            # اختبار المدير
            telegram_manager = create_telegram_manager(config_file)
            
            self.assertIsNotNone(telegram_manager)
            
            # اختبار حالة البوت
            status = telegram_manager.get_bot_status()
            self.assertIsInstance(status, dict)
            
            print("✅ مدير بوت Telegram يعمل بنجاح")
            
        except ImportError:
            print("⚠️ مدير بوت Telegram غير متاح")
        except Exception as e:
            print(f"❌ فشل اختبار مدير بوت Telegram: {e}")
    
    def test_update_manager(self):
        """اختبار مدير التحديث التلقائي"""
        print("🔄 اختبار مدير التحديث التلقائي...")
        
        try:
            from src.updater.update_manager import create_update_manager
            
            # إنشاء ملف إعدادات مؤقت
            config_file = os.path.join(self.temp_dir, "test_update_config.json")
            config = {
                "enabled": False,  # معطل للاختبار
                "auto_check": False,
                "update_server_url": ""
            }
            
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f)
            
            # اختبار المدير
            update_manager = create_update_manager(config_file)
            
            self.assertIsNotNone(update_manager)
            
            # اختبار حالة التحديث
            status = update_manager.get_update_status()
            self.assertIsInstance(status, dict)
            
            # اختبار الحصول على الإصدار الحالي
            version = update_manager.get_current_version()
            self.assertIsInstance(version, str)
            
            print("✅ مدير التحديث التلقائي يعمل بنجاح")
            
        except ImportError:
            print("⚠️ مدير التحديث التلقائي غير متاح")
        except Exception as e:
            print(f"❌ فشل اختبار مدير التحديث التلقائي: {e}")
    
    def test_enhanced_main_imports(self):
        """اختبار استيراد الملف الرئيسي المحسن"""
        print("🚀 اختبار استيراد الملف الرئيسي المحسن...")
        
        try:
            # محاولة استيراد الفئات من الملف الرئيسي المحسن
            import enhanced_main
            
            # فحص وجود الفئات المطلوبة
            self.assertTrue(hasattr(enhanced_main, 'EnhancedSystemManager'))
            self.assertTrue(hasattr(enhanced_main, 'EnhancedSplashScreen'))
            self.assertTrue(hasattr(enhanced_main, 'SystemTrayManager'))
            
            print("✅ الملف الرئيسي المحسن يعمل بنجاح")
            
        except Exception as e:
            print(f"❌ فشل اختبار الملف الرئيسي المحسن: {e}")
    
    def test_system_integration(self):
        """اختبار تكامل النظام"""
        print("🔗 اختبار تكامل النظام...")
        
        try:
            # اختبار إنشاء مدير النظام المحسن
            from enhanced_main import EnhancedSystemManager
            
            system_manager = EnhancedSystemManager()
            
            # اختبار حالة النظام
            status = system_manager.get_system_status()
            self.assertIsInstance(status, dict)
            
            # فحص المكونات الأساسية
            self.assertIn('database', status)
            
            print("✅ تكامل النظام يعمل بنجاح")
            
        except Exception as e:
            print(f"❌ فشل اختبار تكامل النظام: {e}")

def run_feature_tests():
    """تشغيل جميع اختبارات المميزات"""
    print("=" * 60)
    print("🧪 اختبار المميزات المحسنة - نظام إدارة الطلاب")
    print("=" * 60)
    print()
    print("📚 قناة نظام طلاب مستر أحمد عادل – دروس، امتحانات، ومتابعة مستمرة.")
    print()
    
    # تشغيل الاختبارات
    unittest.main(argv=[''], exit=False, verbosity=2)

def check_requirements():
    """فحص المتطلبات"""
    print("🔍 فحص المتطلبات...")
    
    required_packages = [
        'PyQt5',
        'reportlab', 
        'openpyxl',
        'Pillow',
        'requests'
    ]
    
    optional_packages = [
        'Flask',
        'Flask-SocketIO',
        'SQLAlchemy',
        'python-telegram-bot',
        'packaging'
    ]
    
    missing_required = []
    missing_optional = []
    
    for package in required_packages:
        try:
            __import__(package.lower().replace('-', '_'))
            print(f"✅ {package}")
        except ImportError:
            missing_required.append(package)
            print(f"❌ {package}")
    
    for package in optional_packages:
        try:
            __import__(package.lower().replace('-', '_'))
            print(f"✅ {package} (اختياري)")
        except ImportError:
            missing_optional.append(package)
            print(f"⚠️ {package} (اختياري)")
    
    print()
    
    if missing_required:
        print("❌ المتطلبات المفقودة (ضرورية):")
        for package in missing_required:
            print(f"   pip install {package}")
        print()
    
    if missing_optional:
        print("⚠️ المتطلبات المفقودة (اختيارية):")
        for package in missing_optional:
            print(f"   pip install {package}")
        print()
    
    if not missing_required:
        print("✅ جميع المتطلبات الأساسية متوفرة")
    
    return len(missing_required) == 0

def main():
    """الدالة الرئيسية"""
    print("🎓 نظام إدارة الطلاب المحسن - اختبار المميزات")
    print("الإصدار 2.0.0 - 2025")
    print()
    
    # فحص المتطلبات
    if not check_requirements():
        print("❌ يرجى تثبيت المتطلبات المفقودة أولاً")
        return 1
    
    print()
    
    # تشغيل الاختبارات
    try:
        run_feature_tests()
        print()
        print("🎉 تم الانتهاء من جميع الاختبارات!")
        return 0
    except Exception as e:
        print(f"❌ خطأ في تشغيل الاختبارات: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
