# -*- coding: utf-8 -*-
"""
نافذة إدارة الفيديوهات التعليمية المحمية
Protected Educational Videos Management Window
"""

import os
import json
import sqlite3
import hashlib
import time
from datetime import datetime, timedelta
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                            QLineEdit, QPushButton, QTableWidget, QTableWidgetItem,
                            QComboBox, QMessageBox, QFrame, QGroupBox, QFormLayout,
                            QHeaderView, QAbstractItemView, QSplitter, QDateEdit,
                            QTextEdit, QTabWidget, QFileDialog, QProgressBar,
                            QSpinBox, QCheckBox, QListWidget, QDialog, QDialogButtonBox)
from PyQt5.QtCore import Qt, QTimer, QThread, pyqtSignal
from PyQt5.QtGui import QFont, QPixmap

from ..database.database_manager import DatabaseManager
from ..models.student import Student
from ..utils.styles import get_form_style, get_table_style, get_arabic_font_style
from ..utils.message_boxes import show_error_message, show_success_message, show_warning_message

class VideoUploadThread(QThread):
    """خيط رفع الفيديو"""
    progress_updated = pyqtSignal(int)
    upload_finished = pyqtSignal(bool, str)
    
    def __init__(self, video_path, video_info):
        super().__init__()
        self.video_path = video_path
        self.video_info = video_info
        
    def run(self):
        """رفع وتشفير الفيديو"""
        try:
            # محاكاة عملية الرفع والتشفير
            for i in range(101):
                time.sleep(0.05)  # محاكاة الوقت
                self.progress_updated.emit(i)
                
            # حفظ معلومات الفيديو
            self.save_video_info()
            self.upload_finished.emit(True, "تم رفع الفيديو بنجاح")
            
        except Exception as e:
            self.upload_finished.emit(False, f"خطأ في رفع الفيديو: {str(e)}")
            
    def save_video_info(self):
        """حفظ معلومات الفيديو"""
        try:
            # إنشاء مجلد الفيديوهات
            videos_folder = "encrypted_videos"
            os.makedirs(videos_folder, exist_ok=True)
            
            # حفظ معلومات الفيديو في JSON
            video_data = {
                'id': int(time.time()),
                'title': self.video_info['title'],
                'subject': self.video_info['subject'],
                'stage': self.video_info['stage'],
                'grade': self.video_info['grade'],
                'term': self.video_info['term'],
                'description': self.video_info['description'],
                'original_path': self.video_path,
                'encrypted_path': os.path.join(videos_folder, f"video_{int(time.time())}.enc"),
                'upload_date': datetime.now().isoformat(),
                'max_views': 3,
                'duration_minutes': self.video_info.get('duration', 0),
                'has_exam': True,
                'exam_questions': self.video_info.get('exam_questions', [])
            }
            
            # حفظ في ملف JSON
            videos_db_file = "videos_database.json"
            if os.path.exists(videos_db_file):
                with open(videos_db_file, 'r', encoding='utf-8') as f:
                    videos_db = json.load(f)
            else:
                videos_db = {'videos': []}
                
            videos_db['videos'].append(video_data)
            
            with open(videos_db_file, 'w', encoding='utf-8') as f:
                json.dump(videos_db, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            print(f"خطأ في حفظ معلومات الفيديو: {e}")

class VideoPlayerDialog(QDialog):
    """نافذة تشغيل الفيديو المحمي"""
    
    def __init__(self, video_info, student_info):
        super().__init__()
        self.video_info = video_info
        self.student_info = student_info
        self.watch_count = self.get_watch_count()
        self.max_watches = 3
        
        self.init_ui()
        self.init_security()
        
    def init_ui(self):
        """إعداد واجهة المشغل"""
        self.setWindowTitle(f"فيديو محمي: {self.video_info['title']}")
        self.setModal(True)
        self.setStyleSheet("""
            QDialog {
                background-color: #1a1a1a;
                color: white;
            }
            QLabel {
                color: white;
                font-size: 16px;
                font-weight: bold;
            }
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                padding: 15px;
                border-radius: 8px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        
        layout = QVBoxLayout()
        
        # تحذير الحماية
        warning_frame = QFrame()
        warning_frame.setStyleSheet("background-color: #e74c3c; padding: 20px; border-radius: 10px;")
        warning_layout = QVBoxLayout()
        
        warning_title = QLabel("⚠️ تحذير أمني مهم")
        warning_title.setAlignment(Qt.AlignCenter)
        warning_title.setStyleSheet("font-size: 20px; font-weight: bold; color: white;")
        warning_layout.addWidget(warning_title)
        
        warning_text = QLabel(
            "🔒 هذا المحتوى محمي بحقوق الملكية الفكرية\n"
            "🚫 أي محاولة تسجيل أو نسخ ستؤدي لإغلاق النظام فوراً\n"
            "👁️ عدد المشاهدات المتبقية: " + str(self.max_watches - self.watch_count) + " من " + str(self.max_watches) + "\n"
            "📱 سيتم منع استخدام الهاتف أثناء المشاهدة\n"
            "🖥️ سيتم تشغيل الفيديو في وضع الشاشة الكاملة"
        )
        warning_text.setAlignment(Qt.AlignCenter)
        warning_text.setStyleSheet("font-size: 14px; color: white; line-height: 1.5;")
        warning_layout.addWidget(warning_text)
        
        warning_frame.setLayout(warning_layout)
        layout.addWidget(warning_frame)
        
        # معلومات الفيديو
        info_frame = QFrame()
        info_frame.setStyleSheet("background-color: #2c3e50; padding: 15px; border-radius: 8px;")
        info_layout = QVBoxLayout()
        
        video_title = QLabel(f"📹 {self.video_info['title']}")
        video_title.setStyleSheet("font-size: 18px; font-weight: bold;")
        info_layout.addWidget(video_title)
        
        video_details = QLabel(
            f"📚 المادة: {self.video_info['subject']} | "
            f"🎓 الصف: {self.video_info['grade']} | "
            f"📅 الترم: {self.video_info['term']}"
        )
        info_layout.addWidget(video_details)
        
        student_info_label = QLabel(f"👤 الطالب: {self.student_info['name']} ({self.student_info['code']})")
        info_layout.addWidget(student_info_label)
        
        info_frame.setLayout(info_layout)
        layout.addWidget(info_frame)
        
        # شروط المشاهدة
        conditions_frame = QFrame()
        conditions_frame.setStyleSheet("background-color: #34495e; padding: 15px; border-radius: 8px;")
        conditions_layout = QVBoxLayout()
        
        conditions_title = QLabel("📋 شروط المشاهدة:")
        conditions_title.setStyleSheet("font-size: 16px; font-weight: bold;")
        conditions_layout.addWidget(conditions_title)
        
        conditions_text = QLabel(
            "✅ يُسمح بـ 3 مشاهدات فقط لكل فيديو\n"
            "✅ بعد انتهاء الفيديو سيظهر الامتحان مباشرة\n"
            "✅ لا يمكن فتح الامتحان قبل انتهاء الفيديو\n"
            "✅ سيتم تسجيل وقت المشاهدة والنتائج\n"
            "✅ الفيديو سيُحذف تلقائياً بعد 3 مشاهدات\n"
            "❌ ممنوع تسجيل الشاشة أو أخذ لقطات\n"
            "❌ ممنوع استخدام الهاتف أثناء المشاهدة\n"
            "❌ ممنوع مشاركة المحتوى مع الآخرين"
        )
        conditions_text.setStyleSheet("font-size: 12px; line-height: 1.4;")
        conditions_layout.addWidget(conditions_text)
        
        conditions_frame.setLayout(conditions_layout)
        layout.addWidget(conditions_frame)
        
        # أزرار التحكم
        buttons_layout = QHBoxLayout()
        
        if self.watch_count >= self.max_watches:
            # انتهت المشاهدات
            expired_label = QLabel("❌ انتهت المشاهدات المسموحة")
            expired_label.setAlignment(Qt.AlignCenter)
            expired_label.setStyleSheet("font-size: 18px; color: #e74c3c; font-weight: bold;")
            layout.addWidget(expired_label)
            
            close_btn = QPushButton("إغلاق")
            close_btn.clicked.connect(self.reject)
            buttons_layout.addWidget(close_btn)
        else:
            # يمكن المشاهدة
            start_btn = QPushButton(f"▶️ بدء المشاهدة ({self.watch_count + 1}/{self.max_watches})")
            start_btn.clicked.connect(self.start_video)
            buttons_layout.addWidget(start_btn)
            
            cancel_btn = QPushButton("إلغاء")
            cancel_btn.clicked.connect(self.reject)
            buttons_layout.addWidget(cancel_btn)
        
        layout.addLayout(buttons_layout)
        self.setLayout(layout)
        
    def init_security(self):
        """تهيئة الأمان"""
        # منع تغيير حجم النافذة
        self.setFixedSize(600, 500)
        
        # منع النقر بالزر الأيمن
        self.setContextMenuPolicy(Qt.NoContextMenu)
        
    def get_watch_count(self):
        """الحصول على عدد المشاهدات"""
        try:
            watch_file = f"watch_data_{self.student_info['id']}_{self.video_info['id']}.json"
            if os.path.exists(watch_file):
                with open(watch_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    return data.get('watch_count', 0)
            return 0
        except:
            return 0
            
    def start_video(self):
        """بدء تشغيل الفيديو"""
        # تحديث عداد المشاهدات
        self.watch_count += 1
        self.save_watch_count()
        
        # إظهار رسالة بدء التشغيل
        QMessageBox.information(self, "بدء التشغيل", 
                               "سيتم تشغيل الفيديو في وضع الحماية الكاملة\n"
                               "الشاشة ستصبح كاملة ولن تتمكن من الخروج حتى انتهاء الفيديو\n"
                               "بعد انتهاء الفيديو سيظهر الامتحان مباشرة")
        
        # محاكاة تشغيل الفيديو
        self.simulate_video_playback()
        
    def save_watch_count(self):
        """حفظ عداد المشاهدات"""
        try:
            watch_data = {
                'student_id': self.student_info['id'],
                'video_id': self.video_info['id'],
                'watch_count': self.watch_count,
                'last_watch': datetime.now().isoformat()
            }
            
            watch_file = f"watch_data_{self.student_info['id']}_{self.video_info['id']}.json"
            with open(watch_file, 'w', encoding='utf-8') as f:
                json.dump(watch_data, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            print(f"خطأ في حفظ عداد المشاهدات: {e}")
            
    def simulate_video_playback(self):
        """محاكاة تشغيل الفيديو"""
        # في التطبيق الحقيقي، هنا سيتم تشغيل الفيديو المشفر
        QMessageBox.information(self, "تشغيل الفيديو", 
                               f"تم تشغيل الفيديو: {self.video_info['title']}\n"
                               f"المشاهدة رقم: {self.watch_count}\n"
                               f"المشاهدات المتبقية: {self.max_watches - self.watch_count}")
        
        # بعد انتهاء الفيديو، عرض الامتحان
        self.show_exam()
        
    def show_exam(self):
        """عرض الامتحان"""
        exam_result = QMessageBox.question(self, "الامتحان", 
                                         f"انتهى الفيديو!\n"
                                         f"هل تريد بدء الامتحان الآن؟\n"
                                         f"الامتحان مرتبط بالفيديو ولا يمكن تأجيله",
                                         QMessageBox.Yes | QMessageBox.No)
        
        if exam_result == QMessageBox.Yes:
            # محاكاة الامتحان
            QMessageBox.information(self, "الامتحان", 
                                   "تم بدء الامتحان!\n"
                                   "سيتم عرض الأسئلة واحداً تلو الآخر\n"
                                   "النتيجة ستُرسل للمعلم تلقائياً")
            
        self.accept()

class VideosWindow(QWidget):
    """نافذة إدارة الفيديوهات التعليمية"""
    
    def __init__(self, db_manager: DatabaseManager):
        super().__init__()
        self.db_manager = db_manager
        self.student_model = Student(db_manager)
        
        self.init_ui()
        self.setup_styles()
        self.load_videos()
        
    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        self.setWindowTitle("إدارة الفيديوهات التعليمية المحمية")
        self.setGeometry(100, 100, 1200, 800)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout()
        
        # العنوان
        header_frame = self.create_header()
        main_layout.addWidget(header_frame)
        
        # التبويبات
        tabs = QTabWidget()
        
        # تبويب رفع الفيديوهات
        upload_tab = self.create_upload_tab()
        tabs.addTab(upload_tab, "📤 رفع فيديو جديد")
        
        # تبويب إدارة الفيديوهات
        manage_tab = self.create_manage_tab()
        tabs.addTab(manage_tab, "📋 إدارة الفيديوهات")
        
        # تبويب مشاهدة الطلاب
        students_tab = self.create_students_tab()
        tabs.addTab(students_tab, "👥 مشاهدة الطلاب")
        
        # تبويب الإحصائيات
        stats_tab = self.create_stats_tab()
        tabs.addTab(stats_tab, "📊 الإحصائيات")

        # تبويب الربط بتليجرام
        telegram_tab = self.create_telegram_tab()
        tabs.addTab(telegram_tab, "📱 الربط بتليجرام")
        
        main_layout.addWidget(tabs)
        self.setLayout(main_layout)
        
    def create_header(self):
        """إنشاء رأس النافذة"""
        frame = QFrame()
        frame.setStyleSheet("""
            QFrame {
                background-color: #2c3e50;
                border-radius: 10px;
                padding: 20px;
            }
            QLabel {
                color: white;
                font-weight: bold;
            }
        """)
        
        layout = QVBoxLayout()
        
        title = QLabel("🎥 نظام الفيديوهات التعليمية المحمية")
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("font-size: 24px;")
        layout.addWidget(title)
        
        subtitle = QLabel("حماية متقدمة • امتحانات تلقائية • تتبع المشاهدات")
        subtitle.setAlignment(Qt.AlignCenter)
        subtitle.setStyleSheet("font-size: 14px; color: #bdc3c7;")
        layout.addWidget(subtitle)
        
        frame.setLayout(layout)
        return frame

    def create_upload_tab(self):
        """إنشاء تبويب رفع الفيديوهات"""
        widget = QWidget()
        layout = QVBoxLayout()

        # نموذج رفع الفيديو
        upload_group = QGroupBox("رفع فيديو تعليمي جديد")
        upload_layout = QFormLayout()

        # عنوان الفيديو
        self.video_title_input = QLineEdit()
        self.video_title_input.setPlaceholderText("مثال: الجغرافيا - الدرس الأول")
        upload_layout.addRow("عنوان الفيديو:", self.video_title_input)

        # المادة
        self.subject_combo = QComboBox()
        self.subject_combo.addItems(["جغرافيا", "تاريخ", "علوم", "رياضيات", "لغة عربية", "لغة إنجليزية"])
        upload_layout.addRow("المادة:", self.subject_combo)

        # المرحلة الدراسية
        self.stage_combo = QComboBox()
        self.stage_combo.addItems(["الابتدائية", "الإعدادية", "الثانوية"])
        upload_layout.addRow("المرحلة:", self.stage_combo)

        # الصف الدراسي
        self.grade_combo = QComboBox()
        self.grade_combo.addItems(["الأول", "الثاني", "الثالث", "الرابع", "الخامس", "السادس"])
        upload_layout.addRow("الصف:", self.grade_combo)

        # الترم
        self.term_combo = QComboBox()
        self.term_combo.addItems(["الترم الأول", "الترم الثاني"])
        upload_layout.addRow("الترم:", self.term_combo)

        # وصف الفيديو
        self.description_input = QTextEdit()
        self.description_input.setMaximumHeight(80)
        self.description_input.setPlaceholderText("وصف مختصر للفيديو...")
        upload_layout.addRow("الوصف:", self.description_input)

        # مدة الفيديو (تقديرية)
        self.duration_input = QSpinBox()
        self.duration_input.setRange(1, 180)
        self.duration_input.setValue(30)
        self.duration_input.setSuffix(" دقيقة")
        upload_layout.addRow("المدة المتوقعة:", self.duration_input)

        upload_group.setLayout(upload_layout)
        layout.addWidget(upload_group)

        # اختيار ملف الفيديو
        file_group = QGroupBox("اختيار ملف الفيديو")
        file_layout = QVBoxLayout()

        file_select_layout = QHBoxLayout()
        self.file_path_label = QLabel("لم يتم اختيار ملف")
        self.file_path_label.setStyleSheet("color: #7f8c8d; font-style: italic;")

        select_file_btn = QPushButton("📁 اختيار فيديو")
        select_file_btn.clicked.connect(self.select_video_file)

        file_select_layout.addWidget(self.file_path_label)
        file_select_layout.addWidget(select_file_btn)
        file_layout.addLayout(file_select_layout)

        # شريط التقدم
        self.upload_progress = QProgressBar()
        self.upload_progress.setVisible(False)
        file_layout.addWidget(self.upload_progress)

        file_group.setLayout(file_layout)
        layout.addWidget(file_group)

        # إعدادات الحماية
        security_group = QGroupBox("إعدادات الحماية والامتحان")
        security_layout = QFormLayout()

        # عدد المشاهدات المسموحة
        self.max_views_input = QSpinBox()
        self.max_views_input.setRange(1, 10)
        self.max_views_input.setValue(3)
        security_layout.addRow("عدد المشاهدات المسموحة:", self.max_views_input)

        # هل يوجد امتحان
        self.has_exam_checkbox = QCheckBox("يوجد امتحان مرتبط بالفيديو")
        self.has_exam_checkbox.setChecked(True)
        security_layout.addRow("", self.has_exam_checkbox)

        # عدد أسئلة الامتحان
        self.exam_questions_input = QSpinBox()
        self.exam_questions_input.setRange(5, 50)
        self.exam_questions_input.setValue(10)
        security_layout.addRow("عدد أسئلة الامتحان:", self.exam_questions_input)

        # زر إنشاء أسئلة الامتحان
        self.create_questions_btn = QPushButton("📝 إنشاء أسئلة الامتحان")
        self.create_questions_btn.clicked.connect(self.create_exam_questions)
        security_layout.addRow("", self.create_questions_btn)

        security_group.setLayout(security_layout)
        layout.addWidget(security_group)

        # أزرار التحكم
        buttons_layout = QHBoxLayout()

        self.upload_btn = QPushButton("🚀 رفع وتشفير الفيديو")
        self.upload_btn.clicked.connect(self.upload_video)
        self.upload_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                padding: 15px;
                font-size: 16px;
                font-weight: bold;
                border-radius: 8px;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)

        clear_btn = QPushButton("🗑️ مسح النموذج")
        clear_btn.clicked.connect(self.clear_upload_form)

        buttons_layout.addWidget(self.upload_btn)
        buttons_layout.addWidget(clear_btn)
        layout.addLayout(buttons_layout)

        layout.addStretch()
        widget.setLayout(layout)
        return widget

    def create_manage_tab(self):
        """إنشاء تبويب إدارة الفيديوهات"""
        widget = QWidget()
        layout = QVBoxLayout()

        # شريط البحث والفلترة
        search_frame = QFrame()
        search_layout = QHBoxLayout()

        search_input = QLineEdit()
        search_input.setPlaceholderText("البحث في الفيديوهات...")
        search_layout.addWidget(QLabel("البحث:"))
        search_layout.addWidget(search_input)

        filter_combo = QComboBox()
        filter_combo.addItems(["جميع المواد", "جغرافيا", "تاريخ", "علوم", "رياضيات"])
        search_layout.addWidget(QLabel("المادة:"))
        search_layout.addWidget(filter_combo)

        search_frame.setLayout(search_layout)
        layout.addWidget(search_frame)

        # جدول الفيديوهات
        self.videos_table = QTableWidget()
        self.videos_table.setColumnCount(8)
        self.videos_table.setHorizontalHeaderLabels([
            "العنوان", "المادة", "الصف", "الترم", "تاريخ الرفع",
            "المشاهدات", "الحالة", "الإجراءات"
        ])

        # تخصيص الجدول
        header = self.videos_table.horizontalHeader()
        header.setStretchLastSection(True)
        self.videos_table.setAlternatingRowColors(True)
        self.videos_table.setSelectionBehavior(QAbstractItemView.SelectRows)

        layout.addWidget(self.videos_table)

        widget.setLayout(layout)
        return widget

    def create_students_tab(self):
        """إنشاء تبويب مشاهدة الطلاب"""
        widget = QWidget()
        layout = QVBoxLayout()

        # اختيار الفيديو
        video_select_frame = QFrame()
        video_select_layout = QHBoxLayout()

        self.video_select_combo = QComboBox()
        self.video_select_combo.addItem("اختر فيديو...")
        video_select_layout.addWidget(QLabel("الفيديو:"))
        video_select_layout.addWidget(self.video_select_combo)

        refresh_btn = QPushButton("🔄 تحديث")
        video_select_layout.addWidget(refresh_btn)

        video_select_frame.setLayout(video_select_layout)
        layout.addWidget(video_select_frame)

        # قائمة الطلاب
        students_group = QGroupBox("الطلاب المسجلين")
        students_layout = QVBoxLayout()

        self.students_list = QListWidget()
        students_layout.addWidget(self.students_list)

        # أزرار إدارة الطلاب
        student_buttons_layout = QHBoxLayout()

        assign_btn = QPushButton("➕ إضافة طالب للفيديو")
        assign_btn.clicked.connect(self.assign_student_to_video)

        remove_btn = QPushButton("➖ إزالة طالب من الفيديو")

        watch_btn = QPushButton("👁️ مشاهدة كطالب")
        watch_btn.clicked.connect(self.watch_as_student)

        student_buttons_layout.addWidget(assign_btn)
        student_buttons_layout.addWidget(remove_btn)
        student_buttons_layout.addWidget(watch_btn)

        students_layout.addLayout(student_buttons_layout)
        students_group.setLayout(students_layout)
        layout.addWidget(students_group)

        widget.setLayout(layout)
        return widget

    def create_stats_tab(self):
        """إنشاء تبويب الإحصائيات"""
        widget = QWidget()
        layout = QVBoxLayout()

        # إحصائيات عامة
        stats_frame = QFrame()
        stats_frame.setStyleSheet("background-color: #ecf0f1; padding: 20px; border-radius: 10px;")
        stats_layout = QVBoxLayout()

        stats_title = QLabel("📊 إحصائيات النظام")
        stats_title.setStyleSheet("font-size: 18px; font-weight: bold; color: #2c3e50;")
        stats_layout.addWidget(stats_title)

        # الإحصائيات
        self.total_videos_label = QLabel("📹 إجمالي الفيديوهات: 0")
        self.total_views_label = QLabel("👁️ إجمالي المشاهدات: 0")
        self.active_students_label = QLabel("👥 الطلاب النشطين: 0")
        self.completed_exams_label = QLabel("📝 الامتحانات المكتملة: 0")

        stats_layout.addWidget(self.total_videos_label)
        stats_layout.addWidget(self.total_views_label)
        stats_layout.addWidget(self.active_students_label)
        stats_layout.addWidget(self.completed_exams_label)

        stats_frame.setLayout(stats_layout)
        layout.addWidget(stats_frame)

        # تقارير مفصلة
        reports_group = QGroupBox("التقارير المفصلة")
        reports_layout = QVBoxLayout()

        reports_buttons_layout = QHBoxLayout()

        video_report_btn = QPushButton("📊 تقرير الفيديوهات")
        student_report_btn = QPushButton("👥 تقرير الطلاب")
        exam_report_btn = QPushButton("📝 تقرير الامتحانات")

        reports_buttons_layout.addWidget(video_report_btn)
        reports_buttons_layout.addWidget(student_report_btn)
        reports_buttons_layout.addWidget(exam_report_btn)

        reports_layout.addLayout(reports_buttons_layout)
        reports_group.setLayout(reports_layout)
        layout.addWidget(reports_group)

        layout.addStretch()
        widget.setLayout(layout)
        return widget

    def create_telegram_tab(self):
        """إنشاء تبويب الربط بتليجرام"""
        widget = QWidget()
        layout = QVBoxLayout()

        # معلومات الربط
        info_frame = QFrame()
        info_frame.setStyleSheet("background-color: #3498db; color: white; padding: 20px; border-radius: 10px;")
        info_layout = QVBoxLayout()

        info_title = QLabel("📱 الربط مع تليجرام")
        info_title.setStyleSheet("font-size: 20px; font-weight: bold;")
        info_title.setAlignment(Qt.AlignCenter)
        info_layout.addWidget(info_title)

        info_text = QLabel(
            "🔗 اربط النظام مع قناة/بوت تليجرام لرفع الفيديوهات التعليمية\n"
            "📤 رفع الفيديوهات مباشرة من تليجرام إلى النظام\n"
            "🔐 تشفير وحماية تلقائية للفيديوهات المرفوعة\n"
            "📊 تتبع الفيديوهات المرفوعة من تليجرام"
        )
        info_text.setAlignment(Qt.AlignCenter)
        info_text.setStyleSheet("font-size: 14px; line-height: 1.5;")
        info_layout.addWidget(info_text)

        info_frame.setLayout(info_layout)
        layout.addWidget(info_frame)

        # إعدادات البوت
        bot_group = QGroupBox("إعدادات بوت تليجرام")
        bot_layout = QFormLayout()

        # توكن البوت
        self.bot_token_input = QLineEdit()
        self.bot_token_input.setPlaceholderText("أدخل توكن البوت من @BotFather")
        self.bot_token_input.setEchoMode(QLineEdit.Password)
        bot_layout.addRow("توكن البوت:", self.bot_token_input)

        # معرف القناة/المجموعة
        self.channel_id_input = QLineEdit()
        self.channel_id_input.setPlaceholderText("مثال: @mychannel أو -1001234567890")
        bot_layout.addRow("معرف القناة/المجموعة:", self.channel_id_input)

        # اسم البوت
        self.bot_name_input = QLineEdit()
        self.bot_name_input.setPlaceholderText("اسم البوت (اختياري)")
        bot_layout.addRow("اسم البوت:", self.bot_name_input)

        bot_group.setLayout(bot_layout)
        layout.addWidget(bot_group)

        # أزرار الاتصال
        connection_group = QGroupBox("اختبار الاتصال")
        connection_layout = QVBoxLayout()

        # حالة الاتصال
        self.connection_status_label = QLabel("🔴 غير متصل")
        self.connection_status_label.setStyleSheet("font-size: 16px; font-weight: bold; color: #e74c3c;")
        self.connection_status_label.setAlignment(Qt.AlignCenter)
        connection_layout.addWidget(self.connection_status_label)

        # أزرار التحكم
        buttons_layout = QHBoxLayout()

        test_connection_btn = QPushButton("🔗 اختبار الاتصال")
        test_connection_btn.clicked.connect(self.test_telegram_connection)
        test_connection_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                padding: 12px;
                font-size: 14px;
                font-weight: bold;
                border-radius: 6px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)

        save_settings_btn = QPushButton("💾 حفظ الإعدادات")
        save_settings_btn.clicked.connect(self.save_telegram_settings)
        save_settings_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                padding: 12px;
                font-size: 14px;
                font-weight: bold;
                border-radius: 6px;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)

        buttons_layout.addWidget(test_connection_btn)
        buttons_layout.addWidget(save_settings_btn)
        connection_layout.addLayout(buttons_layout)

        connection_group.setLayout(connection_layout)
        layout.addWidget(connection_group)

        # رفع الفيديوهات من تليجرام
        upload_group = QGroupBox("رفع الفيديوهات من تليجرام")
        upload_layout = QVBoxLayout()

        # تعليمات الرفع
        instructions_label = QLabel(
            "📋 تعليمات رفع الفيديوهات:\n\n"
            "1️⃣ أرسل الفيديو إلى القناة/البوت\n"
            "2️⃣ أضف وصف للفيديو بالتنسيق التالي:\n"
            "   العنوان: [عنوان الفيديو]\n"
            "   المادة: [جغرافيا/تاريخ]\n"
            "   الصف: [الصف الدراسي]\n"
            "   الترم: [الترم الأول/الثاني]\n\n"
            "3️⃣ اضغط 'تحديث الفيديوهات' لجلب الفيديوهات الجديدة"
        )
        instructions_label.setStyleSheet("background-color: #f8f9fa; padding: 15px; border-radius: 8px; font-size: 12px;")
        upload_layout.addWidget(instructions_label)

        # أزرار الرفع
        upload_buttons_layout = QHBoxLayout()

        fetch_videos_btn = QPushButton("📥 تحديث الفيديوهات من تليجرام")
        fetch_videos_btn.clicked.connect(self.fetch_videos_from_telegram)
        fetch_videos_btn.setStyleSheet("""
            QPushButton {
                background-color: #9b59b6;
                color: white;
                padding: 15px;
                font-size: 14px;
                font-weight: bold;
                border-radius: 8px;
            }
            QPushButton:hover {
                background-color: #8e44ad;
            }
        """)

        auto_sync_checkbox = QCheckBox("🔄 مزامنة تلقائية كل 5 دقائق")
        auto_sync_checkbox.stateChanged.connect(self.toggle_auto_sync)

        upload_buttons_layout.addWidget(fetch_videos_btn)
        upload_buttons_layout.addWidget(auto_sync_checkbox)
        upload_layout.addLayout(upload_buttons_layout)

        upload_group.setLayout(upload_layout)
        layout.addWidget(upload_group)

        # سجل الأنشطة
        log_group = QGroupBox("سجل أنشطة تليجرام")
        log_layout = QVBoxLayout()

        self.telegram_log = QTextEdit()
        self.telegram_log.setMaximumHeight(150)
        self.telegram_log.setReadOnly(True)
        self.telegram_log.setStyleSheet("background-color: #2c3e50; color: #ecf0f1; font-family: 'Courier New';")
        self.telegram_log.append("📱 سجل أنشطة تليجرام - جاهز للاستخدام")
        log_layout.addWidget(self.telegram_log)

        clear_log_btn = QPushButton("🗑️ مسح السجل")
        clear_log_btn.clicked.connect(lambda: self.telegram_log.clear())
        log_layout.addWidget(clear_log_btn)

        log_group.setLayout(log_layout)
        layout.addWidget(log_group)

        # تحميل الإعدادات المحفوظة
        self.load_telegram_settings()

        widget.setLayout(layout)
        return widget

    def select_video_file(self):
        """اختيار ملف الفيديو"""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "اختيار ملف فيديو",
            "",
            "Video Files (*.mp4 *.avi *.mkv *.mov *.wmv);;All Files (*)"
        )

        if file_path:
            self.file_path_label.setText(os.path.basename(file_path))
            self.file_path_label.setStyleSheet("color: #27ae60; font-weight: bold;")
            self.selected_video_path = file_path

    def upload_video(self):
        """رفع وتشفير الفيديو"""
        # التحقق من البيانات
        if not self.validate_upload_form():
            return

        # جمع بيانات الفيديو
        video_info = {
            'title': self.video_title_input.text().strip(),
            'subject': self.subject_combo.currentText(),
            'stage': self.stage_combo.currentText(),
            'grade': self.grade_combo.currentText(),
            'term': self.term_combo.currentText(),
            'description': self.description_input.toPlainText().strip(),
            'duration': self.duration_input.value(),
            'max_views': self.max_views_input.value(),
            'has_exam': self.has_exam_checkbox.isChecked(),
            'exam_questions_count': self.exam_questions_input.value()
        }

        # بدء عملية الرفع
        self.upload_progress.setVisible(True)
        self.upload_btn.setEnabled(False)

        # إنشاء خيط الرفع
        self.upload_thread = VideoUploadThread(self.selected_video_path, video_info)
        self.upload_thread.progress_updated.connect(self.upload_progress.setValue)
        self.upload_thread.upload_finished.connect(self.on_upload_finished)
        self.upload_thread.start()

    def validate_upload_form(self):
        """التحقق من صحة نموذج الرفع"""
        if not self.video_title_input.text().strip():
            show_error_message(self, "خطأ", "يرجى إدخال عنوان الفيديو")
            return False

        if not hasattr(self, 'selected_video_path'):
            show_error_message(self, "خطأ", "يرجى اختيار ملف الفيديو")
            return False

        if not os.path.exists(self.selected_video_path):
            show_error_message(self, "خطأ", "ملف الفيديو غير موجود")
            return False

        return True

    def on_upload_finished(self, success, message):
        """عند انتهاء الرفع"""
        self.upload_progress.setVisible(False)
        self.upload_btn.setEnabled(True)

        if success:
            show_success_message(self, "نجح", message)
            self.clear_upload_form()
            self.load_videos()
        else:
            show_error_message(self, "خطأ", message)

    def clear_upload_form(self):
        """مسح نموذج الرفع"""
        self.video_title_input.clear()
        self.description_input.clear()
        self.file_path_label.setText("لم يتم اختيار ملف")
        self.file_path_label.setStyleSheet("color: #7f8c8d; font-style: italic;")
        if hasattr(self, 'selected_video_path'):
            delattr(self, 'selected_video_path')

    def load_videos(self):
        """تحميل قائمة الفيديوهات"""
        try:
            # تحميل من ملف JSON
            videos_db_file = "videos_database.json"
            if os.path.exists(videos_db_file):
                with open(videos_db_file, 'r', encoding='utf-8') as f:
                    videos_db = json.load(f)
                    videos = videos_db.get('videos', [])
            else:
                videos = []

            # تحديث الجدول
            self.videos_table.setRowCount(len(videos))

            for row, video in enumerate(videos):
                self.videos_table.setItem(row, 0, QTableWidgetItem(video['title']))
                self.videos_table.setItem(row, 1, QTableWidgetItem(video['subject']))
                self.videos_table.setItem(row, 2, QTableWidgetItem(video['grade']))
                self.videos_table.setItem(row, 3, QTableWidgetItem(video['term']))

                upload_date = datetime.fromisoformat(video['upload_date']).strftime("%Y-%m-%d")
                self.videos_table.setItem(row, 4, QTableWidgetItem(upload_date))

                # حساب المشاهدات
                views_count = self.get_video_views_count(video['id'])
                self.videos_table.setItem(row, 5, QTableWidgetItem(str(views_count)))

                # حالة الفيديو
                status = "نشط" if views_count < video['max_views'] else "منتهي"
                self.videos_table.setItem(row, 6, QTableWidgetItem(status))

                # أزرار الإجراءات
                actions_widget = self.create_video_actions_widget(video)
                self.videos_table.setCellWidget(row, 7, actions_widget)

            # تحديث الإحصائيات
            self.update_statistics()

        except Exception as e:
            print(f"خطأ في تحميل الفيديوهات: {e}")

    def get_video_views_count(self, video_id):
        """حساب عدد مشاهدات الفيديو"""
        try:
            views_count = 0
            # البحث في ملفات المشاهدة
            for filename in os.listdir('.'):
                if filename.startswith('watch_data_') and filename.endswith('.json'):
                    try:
                        with open(filename, 'r', encoding='utf-8') as f:
                            watch_data = json.load(f)
                            if watch_data.get('video_id') == video_id:
                                views_count += watch_data.get('watch_count', 0)
                    except:
                        continue
            return views_count
        except:
            return 0

    def create_video_actions_widget(self, video):
        """إنشاء أزرار إجراءات الفيديو"""
        widget = QWidget()
        layout = QHBoxLayout()
        layout.setContentsMargins(5, 5, 5, 5)

        # زر المعاينة
        preview_btn = QPushButton("👁️")
        preview_btn.setToolTip("معاينة الفيديو")
        preview_btn.clicked.connect(lambda: self.preview_video(video))

        # زر التعديل
        edit_btn = QPushButton("✏️")
        edit_btn.setToolTip("تعديل معلومات الفيديو")

        # زر الحذف
        delete_btn = QPushButton("🗑️")
        delete_btn.setToolTip("حذف الفيديو")
        delete_btn.setStyleSheet("QPushButton { color: #e74c3c; }")

        layout.addWidget(preview_btn)
        layout.addWidget(edit_btn)
        layout.addWidget(delete_btn)

        widget.setLayout(layout)
        return widget

    def preview_video(self, video):
        """معاينة الفيديو"""
        QMessageBox.information(self, "معاينة الفيديو",
                               f"📹 العنوان: {video['title']}\n"
                               f"📚 المادة: {video['subject']}\n"
                               f"🎓 الصف: {video['grade']}\n"
                               f"📅 الترم: {video['term']}\n"
                               f"📝 الوصف: {video['description']}\n"
                               f"⏱️ المدة: {video['duration_minutes']} دقيقة\n"
                               f"👁️ عدد المشاهدات المسموحة: {video['max_views']}\n"
                               f"📝 يوجد امتحان: {'نعم' if video['has_exam'] else 'لا'}")

    def assign_student_to_video(self):
        """إضافة طالب للفيديو"""
        # الحصول على قائمة الطلاب
        students = self.student_model.get_all_students()

        if not students:
            show_warning_message(self, "تحذير", "لا يوجد طلاب مسجلين في النظام")
            return

        # إنشاء نافذة اختيار الطالب
        dialog = QDialog(self)
        dialog.setWindowTitle("اختيار طالب")
        dialog.setModal(True)

        layout = QVBoxLayout()

        # قائمة الطلاب
        students_list = QListWidget()
        for student in students:
            item_text = f"{student['full_name']} ({student['student_code']}) - {student['grade']}"
            students_list.addItem(item_text)

        layout.addWidget(QLabel("اختر الطالب:"))
        layout.addWidget(students_list)

        # أزرار
        buttons = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        buttons.accepted.connect(dialog.accept)
        buttons.rejected.connect(dialog.reject)
        layout.addWidget(buttons)

        dialog.setLayout(layout)

        if dialog.exec_() == QDialog.Accepted:
            selected_row = students_list.currentRow()
            if selected_row >= 0:
                student = students[selected_row]
                self.students_list.addItem(f"{student['full_name']} ({student['student_code']})")
                show_success_message(self, "نجح", f"تم إضافة الطالب {student['full_name']} للفيديو")

    def watch_as_student(self):
        """مشاهدة كطالب مع امتحان مربوط"""
        # محاكاة بيانات طالب للاختبار
        student_info = {
            'id': 1,
            'name': 'طالب تجريبي',
            'code': 'TEST001'
        }

        # محاكاة بيانات فيديو للاختبار
        video_info = {
            'id': 1,
            'title': 'الجغرافيا - الدرس الأول',
            'subject': 'جغرافيا',
            'grade': 'الثاني الثانوي',
            'term': 'الترم الأول',
            'max_views': 3
        }

        # أسئلة الامتحان المربوطة بالفيديو
        exam_questions = [
            {
                'id': 1,
                'type': 'multiple_choice',
                'question': 'ما هي عاصمة مصر؟',
                'options': ['القاهرة', 'الإسكندرية', 'الجيزة', 'أسوان'],
                'correct_answer': 0
            },
            {
                'id': 2,
                'type': 'true_false',
                'question': 'نهر النيل هو أطول نهر في العالم.',
                'correct_answer': True
            },
            {
                'id': 3,
                'type': 'multiple_choice',
                'question': 'في أي قارة تقع مصر؟',
                'options': ['آسيا', 'أفريقيا', 'أوروبا', 'أمريكا'],
                'correct_answer': 1
            },
            {
                'id': 4,
                'type': 'true_false',
                'question': 'البحر الأحمر يقع شرق مصر.',
                'correct_answer': True
            },
            {
                'id': 5,
                'type': 'multiple_choice',
                'question': 'ما هو أطول نهر في مصر؟',
                'options': ['النيل', 'الفرات', 'دجلة', 'الأردن'],
                'correct_answer': 0
            }
        ]

        # فتح نظام الفيديو مع الامتحان المربوط
        from .video_exam_system import VideoExamPlayer

        player_dialog = VideoExamPlayer(video_info, student_info, exam_questions)
        player_dialog.exam_completed.connect(self.on_student_exam_completed)
        player_dialog.exec_()

    def on_student_exam_completed(self, exam_result):
        """عند انتهاء امتحان الطالب"""
        score = exam_result['score']
        total = exam_result['total_questions']
        percentage = (score / total) * 100

        self.telegram_log.append(f"📝 انتهى امتحان الطالب: {exam_result['student_info']['name']}")
        self.telegram_log.append(f"📊 النتيجة: {score}/{total} ({percentage:.1f}%)")

        # تحديث الإحصائيات
        self.update_statistics()

    def update_statistics(self):
        """تحديث الإحصائيات"""
        try:
            # حساب إجمالي الفيديوهات
            videos_db_file = "videos_database.json"
            if os.path.exists(videos_db_file):
                with open(videos_db_file, 'r', encoding='utf-8') as f:
                    videos_db = json.load(f)
                    total_videos = len(videos_db.get('videos', []))
            else:
                total_videos = 0

            # حساب إجمالي المشاهدات
            total_views = 0
            for filename in os.listdir('.'):
                if filename.startswith('watch_data_') and filename.endswith('.json'):
                    try:
                        with open(filename, 'r', encoding='utf-8') as f:
                            watch_data = json.load(f)
                            total_views += watch_data.get('watch_count', 0)
                    except:
                        continue

            # حساب الطلاب النشطين
            active_students = len(self.student_model.get_all_students())

            # تحديث التسميات
            self.total_videos_label.setText(f"📹 إجمالي الفيديوهات: {total_videos}")
            self.total_views_label.setText(f"👁️ إجمالي المشاهدات: {total_views}")
            self.active_students_label.setText(f"👥 الطلاب النشطين: {active_students}")
            self.completed_exams_label.setText(f"📝 الامتحانات المكتملة: {total_views}")

        except Exception as e:
            print(f"خطأ في تحديث الإحصائيات: {e}")

    def test_telegram_connection(self):
        """اختبار الاتصال بتليجرام"""
        bot_token = self.bot_token_input.text().strip()

        if not bot_token:
            show_error_message(self, "خطأ", "يرجى إدخال توكن البوت")
            return

        try:
            # محاكاة اختبار الاتصال
            self.telegram_log.append("🔄 جاري اختبار الاتصال...")

            # في التطبيق الحقيقي، هنا سيتم استخدام python-telegram-bot
            # import telegram
            # bot = telegram.Bot(token=bot_token)
            # bot_info = bot.get_me()

            # محاكاة نجاح الاتصال
            import time
            time.sleep(1)  # محاكاة وقت الاتصال

            self.connection_status_label.setText("🟢 متصل بنجاح")
            self.connection_status_label.setStyleSheet("font-size: 16px; font-weight: bold; color: #27ae60;")

            self.telegram_log.append("✅ تم الاتصال بالبوت بنجاح")
            self.telegram_log.append(f"🤖 اسم البوت: TestBot (محاكاة)")

            show_success_message(self, "نجح", "تم الاتصال بتليجرام بنجاح!")

        except Exception as e:
            self.connection_status_label.setText("🔴 فشل الاتصال")
            self.connection_status_label.setStyleSheet("font-size: 16px; font-weight: bold; color: #e74c3c;")

            self.telegram_log.append(f"❌ فشل الاتصال: {str(e)}")
            show_error_message(self, "خطأ", f"فشل في الاتصال بتليجرام: {str(e)}")

    def save_telegram_settings(self):
        """حفظ إعدادات تليجرام"""
        try:
            settings = {
                'bot_token': self.bot_token_input.text().strip(),
                'channel_id': self.channel_id_input.text().strip(),
                'bot_name': self.bot_name_input.text().strip(),
                'last_updated': datetime.now().isoformat()
            }

            with open('telegram_settings.json', 'w', encoding='utf-8') as f:
                json.dump(settings, f, ensure_ascii=False, indent=2)

            self.telegram_log.append("💾 تم حفظ إعدادات تليجرام")
            show_success_message(self, "نجح", "تم حفظ إعدادات تليجرام بنجاح")

        except Exception as e:
            self.telegram_log.append(f"❌ خطأ في حفظ الإعدادات: {str(e)}")
            show_error_message(self, "خطأ", f"فشل في حفظ الإعدادات: {str(e)}")

    def load_telegram_settings(self):
        """تحميل إعدادات تليجرام المحفوظة"""
        try:
            if os.path.exists('telegram_settings.json'):
                with open('telegram_settings.json', 'r', encoding='utf-8') as f:
                    settings = json.load(f)

                self.bot_token_input.setText(settings.get('bot_token', ''))
                self.channel_id_input.setText(settings.get('channel_id', ''))
                self.bot_name_input.setText(settings.get('bot_name', ''))

                self.telegram_log.append("📂 تم تحميل الإعدادات المحفوظة")

        except Exception as e:
            self.telegram_log.append(f"⚠️ خطأ في تحميل الإعدادات: {str(e)}")

    def fetch_videos_from_telegram(self):
        """جلب الفيديوهات من تليجرام"""
        bot_token = self.bot_token_input.text().strip()
        channel_id = self.channel_id_input.text().strip()

        if not bot_token or not channel_id:
            show_error_message(self, "خطأ", "يرجى إدخال توكن البوت ومعرف القناة")
            return

        try:
            self.telegram_log.append("📥 جاري جلب الفيديوهات من تليجرام...")

            # في التطبيق الحقيقي، هنا سيتم استخدام Telegram API
            # محاكاة جلب الفيديوهات
            import time
            time.sleep(2)  # محاكاة وقت الجلب

            # محاكاة فيديوهات مجلبة
            mock_videos = [
                {
                    'title': 'الجغرافيا - الدرس الأول',
                    'subject': 'جغرافيا',
                    'grade': 'الثاني الثانوي',
                    'term': 'الترم الأول',
                    'file_id': 'telegram_file_123',
                    'description': 'شرح مفصل للدرس الأول في الجغرافيا'
                },
                {
                    'title': 'التاريخ - الحضارة المصرية',
                    'subject': 'تاريخ',
                    'grade': 'الأول الثانوي',
                    'term': 'الترم الثاني',
                    'file_id': 'telegram_file_456',
                    'description': 'تاريخ الحضارة المصرية القديمة'
                }
            ]

            # حفظ الفيديوهات المجلبة
            for video in mock_videos:
                self.save_telegram_video(video)

            self.telegram_log.append(f"✅ تم جلب {len(mock_videos)} فيديو من تليجرام")
            self.telegram_log.append("🔐 جاري تشفير الفيديوهات...")

            # تحديث قائمة الفيديوهات
            self.load_videos()

            show_success_message(self, "نجح", f"تم جلب {len(mock_videos)} فيديو من تليجرام وتشفيرها بنجاح")

        except Exception as e:
            self.telegram_log.append(f"❌ خطأ في جلب الفيديوهات: {str(e)}")
            show_error_message(self, "خطأ", f"فشل في جلب الفيديوهات: {str(e)}")

    def save_telegram_video(self, video_data):
        """حفظ فيديو مجلب من تليجرام"""
        try:
            # إنشاء بيانات الفيديو
            video_info = {
                'id': int(time.time() * 1000),  # معرف فريد
                'title': video_data['title'],
                'subject': video_data['subject'],
                'stage': 'الثانوية',  # افتراضي
                'grade': video_data['grade'],
                'term': video_data['term'],
                'description': video_data['description'],
                'telegram_file_id': video_data['file_id'],
                'source': 'telegram',
                'upload_date': datetime.now().isoformat(),
                'max_views': 3,
                'duration_minutes': 30,  # افتراضي
                'has_exam': True,
                'encrypted_path': f"encrypted_videos/telegram_{video_data['file_id']}.enc"
            }

            # حفظ في قاعدة بيانات الفيديوهات
            videos_db_file = "videos_database.json"
            if os.path.exists(videos_db_file):
                with open(videos_db_file, 'r', encoding='utf-8') as f:
                    videos_db = json.load(f)
            else:
                videos_db = {'videos': []}

            videos_db['videos'].append(video_info)

            with open(videos_db_file, 'w', encoding='utf-8') as f:
                json.dump(videos_db, f, ensure_ascii=False, indent=2)

            self.telegram_log.append(f"💾 تم حفظ فيديو: {video_data['title']}")

        except Exception as e:
            self.telegram_log.append(f"❌ خطأ في حفظ الفيديو: {str(e)}")

    def toggle_auto_sync(self, state):
        """تفعيل/إلغاء المزامنة التلقائية"""
        if state == 2:  # مفعل
            self.telegram_log.append("🔄 تم تفعيل المزامنة التلقائية")
            # في التطبيق الحقيقي، هنا سيتم إنشاء QTimer للمزامنة كل 5 دقائق
            show_success_message(self, "تم التفعيل", "تم تفعيل المزامنة التلقائية\nسيتم جلب الفيديوهات الجديدة كل 5 دقائق")
        else:  # معطل
            self.telegram_log.append("⏸️ تم إيقاف المزامنة التلقائية")

    def create_exam_questions(self):
        """إنشاء أسئلة الامتحان"""
        video_title = self.video_title_input.text().strip()
        subject = self.subject_combo.currentText()
        num_questions = self.exam_questions_input.value()

        if not video_title:
            show_error_message(self, "خطأ", "يرجى إدخال عنوان الفيديو أولاً")
            return

        # فتح نافذة إنشاء الأسئلة
        questions_dialog = ExamQuestionsDialog(video_title, subject, num_questions)
        if questions_dialog.exec_() == QDialog.Accepted:
            questions = questions_dialog.get_questions()
            if questions:
                # حفظ الأسئلة
                self.current_exam_questions = questions
                show_success_message(self, "نجح", f"تم إنشاء {len(questions)} سؤال للامتحان")
            else:
                show_warning_message(self, "تحذير", "لم يتم إنشاء أي أسئلة")

    def setup_styles(self):
        """تطبيق الأنماط"""
        style = get_form_style() + get_table_style() + get_arabic_font_style()
        self.setStyleSheet(style)

class ExamQuestionsDialog(QDialog):
    """نافذة إنشاء أسئلة الامتحان"""

    def __init__(self, video_title, subject, num_questions):
        super().__init__()
        self.video_title = video_title
        self.subject = subject
        self.num_questions = num_questions
        self.questions = []

        self.init_ui()

    def init_ui(self):
        """إعداد واجهة إنشاء الأسئلة"""
        self.setWindowTitle(f"إنشاء أسئلة امتحان: {self.video_title}")
        self.setModal(True)
        self.resize(800, 600)

        self.setStyleSheet("""
            QDialog {
                background-color: #f8f9fa;
            }
            QLabel {
                color: #2c3e50;
                font-size: 14px;
                font-weight: bold;
            }
            QLineEdit, QTextEdit {
                padding: 8px;
                border: 2px solid #bdc3c7;
                border-radius: 5px;
                background-color: white;
            }
            QLineEdit:focus, QTextEdit:focus {
                border-color: #3498db;
            }
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 10px;
                border-radius: 5px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)

        layout = QVBoxLayout()

        # معلومات الامتحان
        info_frame = QFrame()
        info_frame.setStyleSheet("background-color: #3498db; color: white; padding: 15px; border-radius: 8px;")
        info_layout = QVBoxLayout()

        title_label = QLabel(f"📝 إنشاء أسئلة امتحان: {self.video_title}")
        title_label.setStyleSheet("font-size: 18px; font-weight: bold;")
        title_label.setAlignment(Qt.AlignCenter)
        info_layout.addWidget(title_label)

        details_label = QLabel(f"📚 المادة: {self.subject} | 📊 عدد الأسئلة: {self.num_questions}")
        details_label.setAlignment(Qt.AlignCenter)
        info_layout.addWidget(details_label)

        info_frame.setLayout(info_layout)
        layout.addWidget(info_frame)

        # منطقة إنشاء الأسئلة
        questions_scroll = QScrollArea()
        self.questions_widget = QWidget()
        self.questions_layout = QVBoxLayout()
        self.questions_widget.setLayout(self.questions_layout)
        questions_scroll.setWidget(self.questions_widget)
        questions_scroll.setWidgetResizable(True)
        layout.addWidget(questions_scroll)

        # إنشاء نماذج الأسئلة
        self.create_question_forms()

        # أزرار التحكم
        buttons_layout = QHBoxLayout()

        add_question_btn = QPushButton("➕ إضافة سؤال")
        add_question_btn.clicked.connect(self.add_question_form)
        buttons_layout.addWidget(add_question_btn)

        preview_btn = QPushButton("👁️ معاينة الأسئلة")
        preview_btn.clicked.connect(self.preview_questions)
        buttons_layout.addWidget(preview_btn)

        buttons_layout.addStretch()

        cancel_btn = QPushButton("❌ إلغاء")
        cancel_btn.clicked.connect(self.reject)
        buttons_layout.addWidget(cancel_btn)

        save_btn = QPushButton("💾 حفظ الأسئلة")
        save_btn.clicked.connect(self.save_questions)
        buttons_layout.addWidget(save_btn)

        layout.addLayout(buttons_layout)
        self.setLayout(layout)

    def create_question_forms(self):
        """إنشاء نماذج الأسئلة"""
        for i in range(self.num_questions):
            self.add_question_form(i + 1)

    def add_question_form(self, question_num=None):
        """إضافة نموذج سؤال"""
        if question_num is None:
            question_num = self.questions_layout.count() + 1

        question_frame = QGroupBox(f"السؤال {question_num}")
        question_frame.setStyleSheet("QGroupBox { font-weight: bold; padding: 10px; }")
        question_layout = QVBoxLayout()

        # نوع السؤال
        type_layout = QHBoxLayout()
        type_layout.addWidget(QLabel("نوع السؤال:"))

        type_combo = QComboBox()
        type_combo.addItems(["اختيار متعدد", "صح/خطأ"])
        type_combo.currentTextChanged.connect(lambda text, frame=question_frame: self.update_question_type(frame, text))
        type_layout.addWidget(type_combo)

        question_layout.addLayout(type_layout)

        # نص السؤال
        question_layout.addWidget(QLabel("نص السؤال:"))
        question_text = QTextEdit()
        question_text.setMaximumHeight(80)
        question_text.setPlaceholderText("أدخل نص السؤال هنا...")
        question_layout.addWidget(question_text)

        # منطقة الخيارات (ستتغير حسب نوع السؤال)
        options_frame = QFrame()
        options_layout = QVBoxLayout()
        options_frame.setLayout(options_layout)
        question_layout.addWidget(options_frame)

        # تحديث نوع السؤال الافتراضي
        self.update_question_type(question_frame, "اختيار متعدد")

        question_frame.setLayout(question_layout)
        self.questions_layout.addWidget(question_frame)

    def update_question_type(self, question_frame, question_type):
        """تحديث نوع السؤال"""
        # البحث عن إطار الخيارات
        options_frame = None
        for i in range(question_frame.layout().count()):
            widget = question_frame.layout().itemAt(i).widget()
            if isinstance(widget, QFrame) and widget != question_frame:
                options_frame = widget
                break

        if not options_frame:
            return

        # مسح الخيارات السابقة
        layout = options_frame.layout()
        for i in reversed(range(layout.count())):
            layout.itemAt(i).widget().setParent(None)

        if question_type == "اختيار متعدد":
            # إنشاء 4 خيارات
            layout.addWidget(QLabel("الخيارات:"))

            for i in range(4):
                option_layout = QHBoxLayout()

                option_radio = QRadioButton(f"الخيار {chr(65+i)}:")
                option_layout.addWidget(option_radio)

                option_text = QLineEdit()
                option_text.setPlaceholderText(f"أدخل الخيار {chr(65+i)}")
                option_layout.addWidget(option_text)

                layout.addLayout(option_layout)

            layout.addWidget(QLabel("اختر الإجابة الصحيحة بالضغط على الدائرة المناسبة"))

        elif question_type == "صح/خطأ":
            # إنشاء خيارات صح/خطأ
            layout.addWidget(QLabel("الإجابة الصحيحة:"))

            true_radio = QRadioButton("صح")
            false_radio = QRadioButton("خطأ")

            answer_layout = QHBoxLayout()
            answer_layout.addWidget(true_radio)
            answer_layout.addWidget(false_radio)

            layout.addLayout(answer_layout)

    def preview_questions(self):
        """معاينة الأسئلة"""
        questions = self.collect_questions()
        if not questions:
            QMessageBox.warning(self, "تحذير", "لا توجد أسئلة للمعاينة")
            return

        preview_text = "📝 معاينة أسئلة الامتحان:\n\n"

        for i, question in enumerate(questions):
            preview_text += f"السؤال {i+1}: {question['question']}\n"

            if question['type'] == 'multiple_choice':
                for j, option in enumerate(question['options']):
                    marker = "✓" if j == question['correct_answer'] else " "
                    preview_text += f"  {chr(65+j)}. {option} {marker}\n"
            else:
                correct = "صح" if question['correct_answer'] else "خطأ"
                preview_text += f"  الإجابة الصحيحة: {correct}\n"

            preview_text += "\n"

        QMessageBox.information(self, "معاينة الأسئلة", preview_text)

    def collect_questions(self):
        """جمع الأسئلة من النماذج"""
        questions = []

        for i in range(self.questions_layout.count()):
            question_frame = self.questions_layout.itemAt(i).widget()
            if not isinstance(question_frame, QGroupBox):
                continue

            question_data = self.extract_question_data(question_frame)
            if question_data:
                questions.append(question_data)

        return questions

    def extract_question_data(self, question_frame):
        """استخراج بيانات السؤال من النموذج"""
        try:
            layout = question_frame.layout()

            # الحصول على نوع السؤال
            type_combo = None
            question_text_widget = None
            options_frame = None

            for i in range(layout.count()):
                item = layout.itemAt(i)
                if item.layout():  # HBoxLayout للنوع
                    for j in range(item.layout().count()):
                        widget = item.layout().itemAt(j).widget()
                        if isinstance(widget, QComboBox):
                            type_combo = widget
                elif isinstance(item.widget(), QTextEdit):
                    question_text_widget = item.widget()
                elif isinstance(item.widget(), QFrame):
                    options_frame = item.widget()

            if not all([type_combo, question_text_widget, options_frame]):
                return None

            question_type = "multiple_choice" if type_combo.currentText() == "اختيار متعدد" else "true_false"
            question_text = question_text_widget.toPlainText().strip()

            if not question_text:
                return None

            question_data = {
                'type': question_type,
                'question': question_text
            }

            if question_type == 'multiple_choice':
                options = []
                correct_answer = 0

                options_layout = options_frame.layout()
                for i in range(1, options_layout.count()):  # تجاهل Label الأول
                    item = options_layout.itemAt(i)
                    if item.layout():
                        radio = None
                        text_edit = None
                        for j in range(item.layout().count()):
                            widget = item.layout().itemAt(j).widget()
                            if isinstance(widget, QRadioButton):
                                radio = widget
                            elif isinstance(widget, QLineEdit):
                                text_edit = widget

                        if radio and text_edit:
                            option_text = text_edit.text().strip()
                            if option_text:
                                options.append(option_text)
                                if radio.isChecked():
                                    correct_answer = len(options) - 1

                question_data['options'] = options
                question_data['correct_answer'] = correct_answer

            else:  # true_false
                correct_answer = True  # افتراضي
                options_layout = options_frame.layout()
                for i in range(options_layout.count()):
                    item = options_layout.itemAt(i)
                    if item.layout():
                        for j in range(item.layout().count()):
                            widget = item.layout().itemAt(j).widget()
                            if isinstance(widget, QRadioButton) and widget.isChecked():
                                correct_answer = widget.text() == "صح"
                                break

                question_data['correct_answer'] = correct_answer

            return question_data

        except Exception as e:
            print(f"خطأ في استخراج بيانات السؤال: {e}")
            return None

    def save_questions(self):
        """حفظ الأسئلة"""
        questions = self.collect_questions()

        if not questions:
            QMessageBox.warning(self, "تحذير", "لا توجد أسئلة صحيحة للحفظ")
            return

        # التحقق من اكتمال الأسئلة
        incomplete_questions = []
        for i, question in enumerate(questions):
            if question['type'] == 'multiple_choice':
                if len(question.get('options', [])) < 2:
                    incomplete_questions.append(i + 1)
            elif not question.get('question'):
                incomplete_questions.append(i + 1)

        if incomplete_questions:
            QMessageBox.warning(self, "أسئلة غير مكتملة",
                               f"الأسئلة التالية غير مكتملة: {', '.join(map(str, incomplete_questions))}")
            return

        self.questions = questions
        QMessageBox.information(self, "نجح", f"تم حفظ {len(questions)} سؤال بنجاح")
        self.accept()

    def get_questions(self):
        """الحصول على الأسئلة المحفوظة"""
        return self.questions
