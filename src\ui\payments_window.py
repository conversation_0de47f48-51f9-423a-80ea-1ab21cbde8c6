# -*- coding: utf-8 -*-
"""
نافذة إدارة المدفوعات الشهرية
Monthly Payments Management Window
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                            QLineEdit, QPushButton, QTableWidget, QTableWidgetItem,
                            QComboBox, QMessageBox, QFrame, QGroupBox, QFormLayout,
                            QHeaderView, QAbstractItemView, QSplitter, QDateEdit,
                            QDoubleSpinBox, QTextEdit, QSpinBox)
from PyQt5.QtCore import Qt, pyqtSignal, QDate, QTime
from PyQt5.QtGui import QFont
from datetime import datetime, date

from ..models.student import Student
from ..database.database_manager import DatabaseManager
from ..utils.styles import get_form_style, get_table_style, get_arabic_font_style
from ..utils.message_boxes import show_error_message, show_success_message, show_warning_message, show_confirmation_dialog

class PaymentsWindow(QWidget):
    """نافذة إدارة المدفوعات الشهرية"""
    
    def __init__(self, db_manager):
        super().__init__()
        self.db_manager = db_manager
        self.student_model = Student(db_manager)
        
        # إعدادات الأسعار الافتراضية
        self.default_geography_fee = 100.0
        self.default_history_fee = 100.0
        
        self.init_ui()
        self.load_students()
        self.load_payments()
        self.load_settings()
        
    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        self.setWindowTitle("إدارة المدفوعات الشهرية - نظام إدارة الطلاب")
        self.setGeometry(100, 100, 1200, 800)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout()
        
        # شريط الإحصائيات العلوي
        stats_frame = self.create_stats_frame()
        main_layout.addWidget(stats_frame)
        
        # التخطيط الأفقي للمحتوى
        content_layout = QHBoxLayout()
        
        # الجانب الأيسر - نموذج الدفع
        left_panel = self.create_payment_form()
        content_layout.addWidget(left_panel, 1)
        
        # الجانب الأيمن - جدول المدفوعات
        right_panel = self.create_payments_table()
        content_layout.addWidget(right_panel, 2)
        
        main_layout.addLayout(content_layout)
        
        self.setLayout(main_layout)
        
        # تطبيق الأنماط
        self.setStyleSheet(get_form_style() + get_arabic_font_style())
    
    def create_stats_frame(self):
        """إنشاء إطار الإحصائيات"""
        frame = QFrame()
        frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                            stop:0 #3498db, stop:1 #2980b9);
                border-radius: 10px;
                margin: 5px;
                padding: 10px;
            }
            QLabel {
                color: white;
                font-weight: bold;
                background: transparent;
            }
        """)
        
        layout = QHBoxLayout()
        
        # إجمالي المدفوعات اليوم
        self.today_total_label = QLabel("إجمالي اليوم: 0 جنيه")
        self.today_total_label.setStyleSheet("font-size: 16px;")
        layout.addWidget(self.today_total_label)
        
        # إجمالي المدفوعات الشهر
        self.month_total_label = QLabel("إجمالي الشهر: 0 جنيه")
        self.month_total_label.setStyleSheet("font-size: 16px;")
        layout.addWidget(self.month_total_label)
        
        # عدد الطلاب الذين دفعوا اليوم
        self.today_students_label = QLabel("طلاب اليوم: 0")
        self.today_students_label.setStyleSheet("font-size: 16px;")
        layout.addWidget(self.today_students_label)
        
        # عدد الطلاب الذين دفعوا هذا الشهر
        self.month_students_label = QLabel("طلاب الشهر: 0")
        self.month_students_label.setStyleSheet("font-size: 16px;")
        layout.addWidget(self.month_students_label)
        
        frame.setLayout(layout)
        return frame
    
    def create_payment_form(self):
        """إنشاء نموذج تسجيل الدفع"""
        group_box = QGroupBox("تسجيل دفعة جديدة")
        layout = QVBoxLayout()
        
        # نموذج البيانات
        form_layout = QFormLayout()
        
        # اختيار الطالب
        self.student_combo = QComboBox()
        self.student_combo.setEditable(True)
        self.student_combo.currentTextChanged.connect(self.on_student_selected)
        form_layout.addRow("الطالب:", self.student_combo)
        
        # كود الطالب (للعرض فقط)
        self.student_code_label = QLabel("-")
        self.student_code_label.setStyleSheet("font-weight: bold; color: #2c3e50;")
        form_layout.addRow("كود الطالب:", self.student_code_label)
        
        # المجموعة (للعرض فقط)
        self.group_label = QLabel("-")
        self.group_label.setStyleSheet("font-weight: bold; color: #27ae60;")
        form_layout.addRow("المجموعة:", self.group_label)
        
        # تاريخ الدفع (تلقائي)
        self.payment_date = QDateEdit()
        self.payment_date.setDate(QDate.currentDate())
        self.payment_date.setCalendarPopup(True)
        form_layout.addRow("تاريخ الدفع:", self.payment_date)
        
        # الشهر والسنة
        self.month_year_combo = QComboBox()
        self.populate_month_year_combo()
        form_layout.addRow("الشهر/السنة:", self.month_year_combo)
        
        # رسوم الجغرافيا
        self.geography_fee_input = QDoubleSpinBox()
        self.geography_fee_input.setRange(0, 10000)
        self.geography_fee_input.setValue(self.default_geography_fee)
        self.geography_fee_input.setSuffix(" جنيه")
        self.geography_fee_input.valueChanged.connect(self.calculate_total)
        form_layout.addRow("رسوم الجغرافيا:", self.geography_fee_input)
        
        # رسوم التاريخ
        self.history_fee_input = QDoubleSpinBox()
        self.history_fee_input.setRange(0, 10000)
        self.history_fee_input.setValue(self.default_history_fee)
        self.history_fee_input.setSuffix(" جنيه")
        self.history_fee_input.valueChanged.connect(self.calculate_total)
        form_layout.addRow("رسوم التاريخ:", self.history_fee_input)
        
        # الإجمالي (تلقائي)
        self.total_label = QLabel("200.0 جنيه")
        self.total_label.setStyleSheet("""
            font-size: 18px; 
            font-weight: bold; 
            color: #e74c3c; 
            background-color: #f8f9fa; 
            padding: 8px; 
            border-radius: 5px;
            border: 2px solid #e74c3c;
        """)
        form_layout.addRow("الإجمالي:", self.total_label)
        
        # ملاحظات
        self.notes_input = QTextEdit()
        self.notes_input.setMaximumHeight(80)
        self.notes_input.setPlaceholderText("ملاحظات إضافية (اختياري)")
        form_layout.addRow("ملاحظات:", self.notes_input)
        
        layout.addLayout(form_layout)
        
        # أزرار العمليات
        buttons_layout = QHBoxLayout()
        
        # زر تسجيل الدفع
        save_btn = QPushButton("💰 تسجيل الدفع")
        save_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                padding: 12px 20px;
                border-radius: 6px;
                font-size: 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        save_btn.clicked.connect(self.save_payment)
        
        # زر مسح النموذج
        clear_btn = QPushButton("🗑️ مسح")
        clear_btn.setStyleSheet("""
            QPushButton {
                background-color: #95a5a6;
                color: white;
                border: none;
                padding: 12px 20px;
                border-radius: 6px;
                font-size: 16px;
            }
            QPushButton:hover {
                background-color: #7f8c8d;
            }
        """)
        clear_btn.clicked.connect(self.clear_form)
        
        buttons_layout.addWidget(save_btn)
        buttons_layout.addWidget(clear_btn)
        layout.addLayout(buttons_layout)
        
        # إعدادات الأسعار
        settings_group = QGroupBox("إعدادات الأسعار")
        settings_layout = QFormLayout()
        
        # سعر الجغرافيا الافتراضي
        self.default_geo_input = QDoubleSpinBox()
        self.default_geo_input.setRange(0, 10000)
        self.default_geo_input.setValue(self.default_geography_fee)
        self.default_geo_input.setSuffix(" جنيه")
        settings_layout.addRow("سعر الجغرافيا:", self.default_geo_input)
        
        # سعر التاريخ الافتراضي
        self.default_hist_input = QDoubleSpinBox()
        self.default_hist_input.setRange(0, 10000)
        self.default_hist_input.setValue(self.default_history_fee)
        self.default_hist_input.setSuffix(" جنيه")
        settings_layout.addRow("سعر التاريخ:", self.default_hist_input)
        
        # زر حفظ الإعدادات
        save_settings_btn = QPushButton("💾 حفظ الإعدادات")
        save_settings_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 8px 15px;
                border-radius: 5px;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        save_settings_btn.clicked.connect(self.save_settings)
        
        settings_layout.addRow(save_settings_btn)
        settings_group.setLayout(settings_layout)
        layout.addWidget(settings_group)
        
        group_box.setLayout(layout)
        return group_box

    def create_payments_table(self):
        """إنشاء جدول المدفوعات"""
        group_box = QGroupBox("سجل المدفوعات")
        layout = QVBoxLayout()

        # شريط البحث والفلترة
        search_layout = QHBoxLayout()

        # البحث
        search_label = QLabel("البحث:")
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("ابحث بالاسم أو الكود أو المجموعة...")
        self.search_input.textChanged.connect(self.search_payments)

        # فلتر الشهر
        month_filter_label = QLabel("الشهر:")
        self.month_filter_combo = QComboBox()
        self.month_filter_combo.addItem("جميع الشهور")
        self.populate_month_filter()
        self.month_filter_combo.currentTextChanged.connect(self.filter_payments)

        search_layout.addWidget(search_label)
        search_layout.addWidget(self.search_input)
        search_layout.addWidget(month_filter_label)
        search_layout.addWidget(self.month_filter_combo)
        search_layout.addStretch()

        layout.addLayout(search_layout)

        # جدول المدفوعات
        self.payments_table = QTableWidget()
        self.payments_table.setColumnCount(10)
        self.payments_table.setHorizontalHeaderLabels([
            "الكود", "الاسم", "المجموعة", "جغرافيا", "تاريخ",
            "الإجمالي", "التاريخ", "الوقت", "الشهر/السنة", "إجراءات"
        ])

        # تنسيق الجدول
        header = self.payments_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.Stretch)
        self.payments_table.setAlternatingRowColors(True)
        self.payments_table.setSelectionBehavior(QAbstractItemView.SelectRows)

        layout.addWidget(self.payments_table)

        group_box.setLayout(layout)
        return group_box

    def populate_month_year_combo(self):
        """ملء قائمة الشهر/السنة"""
        current_date = datetime.now()
        current_month = current_date.month
        current_year = current_date.year

        months = [
            "يناير", "فبراير", "مارس", "أبريل", "مايو", "يونيو",
            "يوليو", "أغسطس", "سبتمبر", "أكتوبر", "نوفمبر", "ديسمبر"
        ]

        # إضافة الشهر الحالي والشهور القادمة
        for i in range(12):
            month_num = ((current_month - 1 + i) % 12) + 1
            year = current_year + ((current_month - 1 + i) // 12)
            month_name = months[month_num - 1]
            self.month_year_combo.addItem(f"{month_name} {year}")

        # تحديد الشهر الحالي كافتراضي
        current_month_name = months[current_month - 1]
        self.month_year_combo.setCurrentText(f"{current_month_name} {current_year}")

    def populate_month_filter(self):
        """ملء فلتر الشهور"""
        try:
            query = "SELECT DISTINCT month_year FROM monthly_payments ORDER BY created_at DESC"
            result = self.db_manager.execute_query_with_fetch(query, fetch=True)

            for row in result:
                if row[0]:
                    self.month_filter_combo.addItem(row[0])
        except Exception as e:
            print(f"خطأ في تحميل فلتر الشهور: {e}")

    def load_students(self):
        """تحميل قائمة الطلاب"""
        try:
            students = self.student_model.get_all_students()
            self.student_combo.clear()
            self.student_combo.addItem("اختر الطالب...")

            for student in students:
                display_text = f"{student['full_name']} ({student['student_code']})"
                self.student_combo.addItem(display_text, student)

        except Exception as e:
            show_error_message(self, "خطأ", f"فشل في تحميل الطلاب: {str(e)}")

    def on_student_selected(self):
        """عند اختيار طالب"""
        current_data = self.student_combo.currentData()
        if current_data:
            self.student_code_label.setText(current_data['student_code'])
            group_name = current_data.get('group_name', 'لا توجد مجموعة')
            self.group_label.setText(group_name)
        else:
            self.student_code_label.setText("-")
            self.group_label.setText("-")

    def calculate_total(self):
        """حساب الإجمالي تلقائياً"""
        geography_fee = self.geography_fee_input.value()
        history_fee = self.history_fee_input.value()
        total = geography_fee + history_fee
        self.total_label.setText(f"{total:.1f} جنيه")

    def save_payment(self):
        """حفظ الدفعة"""
        # التحقق من البيانات
        if self.student_combo.currentIndex() == 0:
            show_warning_message(self, "تحذير", "يرجى اختيار الطالب")
            return

        student_data = self.student_combo.currentData()
        if not student_data:
            show_warning_message(self, "تحذير", "بيانات الطالب غير صحيحة")
            return

        # جمع البيانات
        geography_fee = self.geography_fee_input.value()
        history_fee = self.history_fee_input.value()
        total_amount = geography_fee + history_fee

        if total_amount <= 0:
            show_warning_message(self, "تحذير", "يجب أن يكون المبلغ أكبر من صفر")
            return

        payment_date = self.payment_date.date().toString("yyyy-MM-dd")
        payment_time = datetime.now().strftime("%H:%M:%S")
        month_year = self.month_year_combo.currentText()
        notes = self.notes_input.toPlainText().strip()

        try:
            # إدراج الدفعة في قاعدة البيانات
            query = """
            INSERT INTO monthly_payments
            (student_id, student_code, student_name, group_name, geography_fee,
             history_fee, total_amount, payment_date, payment_time, month_year, notes)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """

            params = (
                student_data['id'],
                student_data['student_code'],
                student_data['full_name'],
                student_data.get('group_name', 'لا توجد مجموعة'),
                geography_fee,
                history_fee,
                total_amount,
                payment_date,
                payment_time,
                month_year,
                notes
            )

            self.db_manager.execute_query(query, params)

            show_success_message(self, "نجح", f"تم تسجيل دفعة {total_amount:.1f} جنيه بنجاح")

            # إعادة تحميل البيانات
            self.load_payments()
            self.update_statistics()
            self.clear_form()

        except Exception as e:
            show_error_message(self, "خطأ", f"فشل في حفظ الدفعة: {str(e)}")

    def clear_form(self):
        """مسح النموذج"""
        self.student_combo.setCurrentIndex(0)
        self.student_code_label.setText("-")
        self.group_label.setText("-")
        self.payment_date.setDate(QDate.currentDate())
        self.geography_fee_input.setValue(self.default_geography_fee)
        self.history_fee_input.setValue(self.default_history_fee)
        self.notes_input.clear()
        self.calculate_total()

    def load_payments(self):
        """تحميل المدفوعات"""
        try:
            query = """
            SELECT * FROM monthly_payments
            ORDER BY created_at DESC
            """
            result = self.db_manager.execute_query_with_fetch(query, fetch=True)

            self.payments_table.setRowCount(len(result))

            for row_idx, payment in enumerate(result):
                self.payments_table.setItem(row_idx, 0, QTableWidgetItem(str(payment[2])))  # student_code
                self.payments_table.setItem(row_idx, 1, QTableWidgetItem(str(payment[3])))  # student_name
                self.payments_table.setItem(row_idx, 2, QTableWidgetItem(str(payment[4] or "")))  # group_name
                self.payments_table.setItem(row_idx, 3, QTableWidgetItem(f"{payment[5]:.1f}"))  # geography_fee
                self.payments_table.setItem(row_idx, 4, QTableWidgetItem(f"{payment[6]:.1f}"))  # history_fee
                self.payments_table.setItem(row_idx, 5, QTableWidgetItem(f"{payment[7]:.1f}"))  # total_amount
                self.payments_table.setItem(row_idx, 6, QTableWidgetItem(str(payment[8])))  # payment_date
                self.payments_table.setItem(row_idx, 7, QTableWidgetItem(str(payment[9])))  # payment_time
                self.payments_table.setItem(row_idx, 8, QTableWidgetItem(str(payment[10])))  # month_year

                # زر حذف
                delete_btn = QPushButton("🗑️")
                delete_btn.setToolTip("حذف الدفعة")
                delete_btn.setStyleSheet("""
                    QPushButton {
                        background-color: #e74c3c;
                        color: white;
                        border: none;
                        border-radius: 15px;
                        font-size: 14px;
                        max-width: 30px;
                        max-height: 30px;
                    }
                    QPushButton:hover {
                        background-color: #c0392b;
                    }
                """)
                delete_btn.clicked.connect(lambda checked, payment_id=payment[0]: self.delete_payment(payment_id))
                self.payments_table.setCellWidget(row_idx, 9, delete_btn)

            self.update_statistics()

        except Exception as e:
            show_error_message(self, "خطأ", f"فشل في تحميل المدفوعات: {str(e)}")

    def delete_payment(self, payment_id):
        """حذف دفعة"""
        if show_confirmation_dialog(self, "تأكيد الحذف", "هل تريد حذف هذه الدفعة؟"):
            try:
                query = "DELETE FROM monthly_payments WHERE id = ?"
                self.db_manager.execute_query(query, (payment_id,))

                show_success_message(self, "نجح", "تم حذف الدفعة بنجاح")
                self.load_payments()

            except Exception as e:
                show_error_message(self, "خطأ", f"فشل في حذف الدفعة: {str(e)}")

    def search_payments(self):
        """البحث في المدفوعات"""
        search_term = self.search_input.text().strip().lower()

        for row in range(self.payments_table.rowCount()):
            show_row = False

            # البحث في الاسم والكود والمجموعة
            for col in [0, 1, 2]:  # كود، اسم، مجموعة
                item = self.payments_table.item(row, col)
                if item and search_term in item.text().lower():
                    show_row = True
                    break

            self.payments_table.setRowHidden(row, not show_row)

    def filter_payments(self):
        """فلترة المدفوعات حسب الشهر"""
        selected_month = self.month_filter_combo.currentText()

        if selected_month == "جميع الشهور":
            # إظهار جميع الصفوف
            for row in range(self.payments_table.rowCount()):
                self.payments_table.setRowHidden(row, False)
        else:
            # فلترة حسب الشهر
            for row in range(self.payments_table.rowCount()):
                month_item = self.payments_table.item(row, 8)  # عمود الشهر/السنة
                if month_item:
                    show_row = month_item.text() == selected_month
                    self.payments_table.setRowHidden(row, not show_row)

    def update_statistics(self):
        """تحديث الإحصائيات"""
        try:
            today = date.today().strftime("%Y-%m-%d")
            current_month = datetime.now().strftime("%B %Y")

            # إجمالي اليوم
            query_today = "SELECT SUM(total_amount), COUNT(*) FROM monthly_payments WHERE payment_date = ?"
            result_today = self.db_manager.execute_query_with_fetch(query_today, (today,), fetch=True)

            today_total = result_today[0][0] if result_today[0][0] else 0
            today_count = result_today[0][1] if result_today[0][1] else 0

            # إجمالي الشهر
            query_month = "SELECT SUM(total_amount), COUNT(*) FROM monthly_payments WHERE month_year LIKE ?"
            month_pattern = f"%{datetime.now().strftime('%Y')}%"
            result_month = self.db_manager.execute_query_with_fetch(query_month, (month_pattern,), fetch=True)

            month_total = result_month[0][0] if result_month[0][0] else 0
            month_count = result_month[0][1] if result_month[0][1] else 0

            # تحديث التسميات
            self.today_total_label.setText(f"إجمالي اليوم: {today_total:.1f} جنيه")
            self.today_students_label.setText(f"طلاب اليوم: {today_count}")
            self.month_total_label.setText(f"إجمالي الشهر: {month_total:.1f} جنيه")
            self.month_students_label.setText(f"طلاب الشهر: {month_count}")

        except Exception as e:
            print(f"خطأ في تحديث الإحصائيات: {e}")

    def save_settings(self):
        """حفظ إعدادات الأسعار"""
        try:
            self.default_geography_fee = self.default_geo_input.value()
            self.default_history_fee = self.default_hist_input.value()

            # حفظ في قاعدة البيانات
            queries = [
                ("INSERT OR REPLACE INTO settings (setting_key, setting_value) VALUES (?, ?)",
                 ("default_geography_fee", str(self.default_geography_fee))),
                ("INSERT OR REPLACE INTO settings (setting_key, setting_value) VALUES (?, ?)",
                 ("default_history_fee", str(self.default_history_fee)))
            ]

            for query, params in queries:
                self.db_manager.execute_query(query, params)

            # تحديث النموذج
            self.geography_fee_input.setValue(self.default_geography_fee)
            self.history_fee_input.setValue(self.default_history_fee)
            self.calculate_total()

            show_success_message(self, "نجح", "تم حفظ إعدادات الأسعار بنجاح")

        except Exception as e:
            show_error_message(self, "خطأ", f"فشل في حفظ الإعدادات: {str(e)}")

    def load_settings(self):
        """تحميل إعدادات الأسعار"""
        try:
            # تحميل سعر الجغرافيا
            query = "SELECT setting_value FROM settings WHERE setting_key = ?"
            result = self.db_manager.execute_query_with_fetch(query, ("default_geography_fee",), fetch=True)
            if result:
                self.default_geography_fee = float(result[0][0])
                self.default_geo_input.setValue(self.default_geography_fee)
                self.geography_fee_input.setValue(self.default_geography_fee)

            # تحميل سعر التاريخ
            result = self.db_manager.execute_query_with_fetch(query, ("default_history_fee",), fetch=True)
            if result:
                self.default_history_fee = float(result[0][0])
                self.default_hist_input.setValue(self.default_history_fee)
                self.history_fee_input.setValue(self.default_history_fee)

            self.calculate_total()

        except Exception as e:
            print(f"خطأ في تحميل الإعدادات: {e}")

    def showEvent(self, event):
        """عند إظهار النافذة"""
        super().showEvent(event)
        self.load_students()
        self.load_payments()
