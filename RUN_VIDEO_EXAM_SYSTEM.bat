@echo off
chcp 65001 >nul
title نظام الفيديوهات المربوطة بالامتحانات - مستر أحمد عادل

cls
color 0E
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║           نظام الفيديوهات المربوطة بالامتحانات             ║
echo ║                   مستر أحمد عادل                           ║
echo ║              معلم الجغرافيا والتاريخ                        ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 🎥📝 النظام المتكامل للفيديوهات والامتحانات:
echo.
echo ✅ فيديوهات تعليمية محمية
echo ✅ 📝 امتحانات مربوطة تلقائياً بالفيديوهات
echo ✅ 🔒 حماية متقدمة من التسجيل والنسخ
echo ✅ 👁️ حد أقصى 3 مشاهدات لكل فيديو
echo ✅ ⏱️ امتحان إجباري بعد انتهاء الفيديو
echo ✅ 🚫 لا يمكن الخروج قبل إنهاء الامتحان
echo ✅ 📊 تصحيح تلقائي ونتائج فورية
echo ✅ 📱 ربط مع تليجرام لرفع الفيديوهات
echo.

echo 🚀 تشغيل النظام المتكامل...
echo.

REM التحقق من Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير موجود
    echo 🏪 فتح Microsoft Store لتثبيت Python...
    start ms-windows-store://pdp/?ProductId=9NRWMJP3717K
    goto end
)

echo ✅ Python موجود
python --version

echo.
echo 📦 تثبيت المكتبات المطلوبة...

REM تثبيت المكتبات الأساسية
python -m pip install --quiet PyQt5

REM تثبيت مكتبات QR Code
python -m pip install --quiet qrcode[pil] Pillow

REM تثبيت مكتبة تليجرام
python -m pip install --quiet python-telegram-bot

REM تثبيت مكتبات الوسائط
python -m pip install --quiet

echo ✅ تم تثبيت جميع المكتبات

echo.
echo 🎯 تشغيل النظام المتكامل...
echo.

python main.py

:end
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║              نظام الفيديوهات والامتحانات المتكامل           ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.
echo 🔐 بيانات تسجيل الدخول:
echo    👤 اسم المستخدم: admin
echo    🔑 كلمة المرور: admin123
echo.
echo 🎥 مميزات نظام الفيديوهات:
echo.
echo 1️⃣ رفع الفيديوهات:
echo    📤 رفع من الكمبيوتر أو تليجرام
echo    🔐 تشفير تلقائي بـ AES-256
echo    📝 ربط تلقائي بالامتحان
echo    ⚙️ إعدادات حماية متقدمة
echo.
echo 2️⃣ إنشاء الامتحانات:
echo    📝 إنشاء أسئلة اختيارية وصح/خطأ
echo    🔗 ربط تلقائي بالفيديو
echo    ⏱️ مدة زمنية محددة (30 دقيقة)
echo    ✅ تصحيح تلقائي
echo.
echo 3️⃣ مشاهدة الفيديو:
echo    🖥️ شاشة كاملة إجبارية
echo    🚫 منع تسجيل الشاشة
echo    🚫 منع لقطات الشاشة
echo    🚫 منع النسخ والمشاركة
echo    👁️ حد أقصى 3 مشاهدات
echo.
echo 4️⃣ الامتحان التلقائي:
echo    📝 يظهر تلقائياً بعد انتهاء الفيديو
echo    🚫 لا يمكن تجاهله أو تأجيله
echo    ⏱️ مؤقت 30 دقيقة
echo    🚫 لا يمكن الخروج قبل الانتهاء
echo    📊 نتيجة فورية
echo.
echo 🧭 دليل الاستخدام:
echo.
echo 📤 رفع فيديو جديد:
echo    1. إدارة الفيديوهات ← رفع فيديو جديد
echo    2. املأ معلومات الفيديو
echo    3. اختر ملف الفيديو
echo    4. اضغط "إنشاء أسئلة الامتحان"
echo    5. أنشئ الأسئلة المطلوبة
echo    6. اضغط "رفع وتشفير الفيديو"
echo.
echo 📝 إنشاء أسئلة الامتحان:
echo    1. اختر نوع السؤال (اختيارية/صح وخطأ)
echo    2. أدخل نص السؤال
echo    3. أدخل الخيارات (للاختيارية)
echo    4. حدد الإجابة الصحيحة
echo    5. كرر للأسئلة الأخرى
echo    6. اضغط "حفظ الأسئلة"
echo.
echo 👁️ مشاهدة كطالب:
echo    1. إدارة الفيديوهات ← مشاهدة الطلاب
echo    2. اضغط "مشاهدة كطالب"
echo    3. ستظهر نافذة التحذيرات
echo    4. اضغط "بدء المشاهدة"
echo    5. شاهد الفيديو حتى النهاية
echo    6. سيظهر الامتحان تلقائياً
echo    7. أجب على جميع الأسئلة
echo    8. اضغط "إنهاء الامتحان"
echo    9. ستظهر النتيجة فوراً
echo.
echo 📱 الربط مع تليجرام:
echo    1. إدارة الفيديوهات ← الربط بتليجرام
echo    2. أدخل توكن البوت ومعرف القناة
echo    3. اضغط "اختبار الاتصال"
echo    4. اضغط "حفظ الإعدادات"
echo    5. أرسل فيديو للقناة مع الوصف
echo    6. اضغط "تحديث الفيديوهات من تليجرام"
echo.
echo 🛡️ الحماية المتقدمة:
echo    🚫 منع OBS, Bandicam, Camtasia
echo    🚫 منع Print Screen و Alt+Tab
echo    🚫 منع Escape و F11
echo    🚫 شاشة كاملة إجبارية
echo    🚫 منع النقر بالزر الأيمن
echo    🔐 تشفير الفيديوهات
echo    👁️ حد أقصى 3 مشاهدات
echo    📝 امتحان إجباري
echo.
echo ⚠️ تحذيرات للطلاب:
echo    • أي محاولة تسجيل = إغلاق فوري
echo    • أي محاولة نسخ = إغلاق فوري
echo    • استخدام مفاتيح محظورة = إغلاق فوري
echo    • تشغيل برامج تسجيل = إغلاق فوري
echo    • لا يمكن الخروج قبل إنهاء الامتحان
echo    • الامتحان إجباري ولا يمكن تجاهله
echo.
echo 📊 النتائج والتقارير:
echo    📈 نتائج فورية للطلاب
echo    📊 تقارير مفصلة للمعلم
echo    📋 إحصائيات المشاهدات
echo    📝 نتائج الامتحانات
echo    📱 إشعارات تلقائية
echo.
echo 🎓 تم التطوير بواسطة: م/ حسام أسامة
echo    مهندس برمجيات - مطور تطبيقات
echo    مصمم خصيصاً لمستر أحمد عادل
echo.
echo 🏆 أول نظام فيديوهات تعليمية مربوطة بامتحانات!
echo    مع حماية متقدمة وربط تليجرام
echo.
pause
