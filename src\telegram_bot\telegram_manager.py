# -*- coding: utf-8 -*-
"""
مدير بوت Telegram
Telegram Bot Manager
"""

import os
import json
import logging
import threading
import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Callable
import telegram
from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import Application, CommandHandler, MessageHandler, CallbackQueryHandler, filters

# إعداد السجلات
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class TelegramBotManager:
    """مدير بوت Telegram"""
    
    def __init__(self, config_file="telegram_config.json"):
        self.config_file = config_file
        self.config = self.load_config()
        self.application = None
        self.bot = None
        self.is_running = False
        
        # قوائم المستخدمين المصرح لهم
        self.authorized_users = set()
        self.admin_users = set()
        
        # معالجات الأحداث
        self.event_handlers = {}
        
        # إحصائيات
        self.stats = {
            'messages_sent': 0,
            'commands_processed': 0,
            'users_count': 0,
            'start_time': None
        }
        
        # تهيئة البوت
        self._initialize_bot()
    
    def load_config(self) -> Dict[str, Any]:
        """تحميل إعدادات البوت"""
        default_config = {
            "bot_token": "",
            "enabled": False,
            "admin_chat_ids": [],
            "authorized_chat_ids": [],
            "notifications": {
                "new_student": True,
                "attendance_marked": True,
                "grade_added": True,
                "daily_report": True,
                "system_alerts": True
            },
            "commands": {
                "stats": True,
                "attendance": True,
                "students": True,
                "help": True
            },
            "daily_report_time": "18:00",
            "language": "ar"
        }
        
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    # دمج مع الإعدادات الافتراضية
                    for key, value in default_config.items():
                        if key not in config:
                            config[key] = value
                    return config
            except Exception as e:
                logger.warning(f"فشل في تحميل إعدادات البوت: {e}")
        
        # حفظ الإعدادات الافتراضية
        self.save_config(default_config)
        return default_config
    
    def save_config(self, config: Dict[str, Any]):
        """حفظ إعدادات البوت"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=2, ensure_ascii=False)
        except Exception as e:
            logger.error(f"فشل في حفظ إعدادات البوت: {e}")
    
    def _initialize_bot(self):
        """تهيئة البوت"""
        try:
            bot_token = self.config.get("bot_token", "")
            if not bot_token:
                logger.warning("لم يتم تعيين رمز البوت")
                return
            
            # إنشاء التطبيق
            self.application = Application.builder().token(bot_token).build()
            self.bot = self.application.bot
            
            # تحميل المستخدمين المصرح لهم
            self.authorized_users = set(self.config.get("authorized_chat_ids", []))
            self.admin_users = set(self.config.get("admin_chat_ids", []))
            
            # إعداد المعالجات
            self._setup_handlers()
            
            logger.info("تم تهيئة بوت Telegram بنجاح")
            
        except Exception as e:
            logger.error(f"فشل في تهيئة البوت: {e}")
            self.application = None
            self.bot = None
    
    def _setup_handlers(self):
        """إعداد معالجات الأوامر والرسائل"""
        if not self.application:
            return
        
        # معالجات الأوامر
        self.application.add_handler(CommandHandler("start", self._handle_start))
        self.application.add_handler(CommandHandler("help", self._handle_help))
        self.application.add_handler(CommandHandler("stats", self._handle_stats))
        self.application.add_handler(CommandHandler("attendance", self._handle_attendance))
        self.application.add_handler(CommandHandler("students", self._handle_students))
        self.application.add_handler(CommandHandler("authorize", self._handle_authorize))
        
        # معالج الرسائل النصية
        self.application.add_handler(MessageHandler(filters.TEXT & ~filters.COMMAND, self._handle_message))
        
        # معالج الأزرار التفاعلية
        self.application.add_handler(CallbackQueryHandler(self._handle_callback))
    
    async def _handle_start(self, update: Update, context):
        """معالج أمر /start"""
        try:
            chat_id = update.effective_chat.id
            user = update.effective_user
            
            welcome_message = f"""
🎓 مرحباً {user.first_name}!

أهلاً بك في بوت نظام إدارة الطلاب لمستر أحمد عادل

📚 قناة نظام طلاب مستر أحمد عادل – دروس، امتحانات، ومتابعة مستمرة.

🔹 الأوامر المتاحة:
/help - عرض المساعدة
/stats - الإحصائيات
/attendance - حالة الحضور
/students - معلومات الطلاب

للحصول على الصلاحيات، تواصل مع المدير.
            """
            
            await update.message.reply_text(welcome_message)
            
            # إضافة المستخدم للقائمة إذا لم يكن موجوداً
            if chat_id not in self.authorized_users and chat_id not in self.admin_users:
                # إشعار المدير بمستخدم جديد
                await self._notify_admins(f"مستخدم جديد: {user.first_name} (@{user.username})\nChat ID: {chat_id}")
            
            self.stats['commands_processed'] += 1
            
        except Exception as e:
            logger.error(f"خطأ في معالج /start: {e}")
    
    async def _handle_help(self, update: Update, context):
        """معالج أمر /help"""
        try:
            help_message = """
📖 دليل استخدام البوت

🔹 الأوامر العامة:
/start - بدء المحادثة
/help - عرض هذه المساعدة
/stats - عرض الإحصائيات العامة

🔹 أوامر المعلم:
/attendance - حالة الحضور اليوم
/students - إحصائيات الطلاب
/authorize <chat_id> - إضافة مستخدم مصرح (للمدير فقط)

🔹 الإشعارات التلقائية:
• إضافة طالب جديد
• تسجيل حضور
• إضافة درجة جديدة
• التقرير اليومي
• تنبيهات النظام

📞 للدعم الفني، تواصل مع المطور.
            """
            
            await update.message.reply_text(help_message)
            self.stats['commands_processed'] += 1
            
        except Exception as e:
            logger.error(f"خطأ في معالج /help: {e}")
    
    async def _handle_stats(self, update: Update, context):
        """معالج أمر /stats"""
        try:
            chat_id = update.effective_chat.id
            
            if not self._is_authorized(chat_id):
                await update.message.reply_text("❌ غير مصرح لك باستخدام هذا الأمر")
                return
            
            # الحصول على الإحصائيات من قاعدة البيانات
            stats_message = f"""
📊 إحصائيات النظام

👥 الطلاب: {self._get_students_count()}
✅ الحاضرين اليوم: {self._get_attendance_count('حاضر')}
❌ الغائبين اليوم: {self._get_attendance_count('غائب')}
⏰ المتأخرين اليوم: {self._get_attendance_count('متأخر')}

🤖 إحصائيات البوت:
📨 الرسائل المرسلة: {self.stats['messages_sent']}
⚡ الأوامر المنفذة: {self.stats['commands_processed']}
👤 المستخدمين: {len(self.authorized_users) + len(self.admin_users)}

🕐 آخر تحديث: {datetime.now().strftime('%Y-%m-%d %H:%M')}
            """
            
            await update.message.reply_text(stats_message)
            self.stats['commands_processed'] += 1
            
        except Exception as e:
            logger.error(f"خطأ في معالج /stats: {e}")
    
    async def _handle_attendance(self, update: Update, context):
        """معالج أمر /attendance"""
        try:
            chat_id = update.effective_chat.id
            
            if not self._is_authorized(chat_id):
                await update.message.reply_text("❌ غير مصرح لك باستخدام هذا الأمر")
                return
            
            # إنشاء أزرار تفاعلية
            keyboard = [
                [InlineKeyboardButton("📊 إحصائيات اليوم", callback_data="attendance_today")],
                [InlineKeyboardButton("📅 إحصائيات الأسبوع", callback_data="attendance_week")],
                [InlineKeyboardButton("📈 تقرير مفصل", callback_data="attendance_detailed")]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)
            
            await update.message.reply_text(
                "📋 اختر نوع تقرير الحضور:",
                reply_markup=reply_markup
            )
            
            self.stats['commands_processed'] += 1
            
        except Exception as e:
            logger.error(f"خطأ في معالج /attendance: {e}")
    
    async def _handle_students(self, update: Update, context):
        """معالج أمر /students"""
        try:
            chat_id = update.effective_chat.id
            
            if not self._is_authorized(chat_id):
                await update.message.reply_text("❌ غير مصرح لك باستخدام هذا الأمر")
                return
            
            students_message = f"""
👥 إحصائيات الطلاب

📊 العدد الإجمالي: {self._get_students_count()}

📚 حسب المرحلة:
• الإعدادية: {self._get_students_by_stage('إعدادي')}
• الثانوية: {self._get_students_by_stage('ثانوي')}

👦👧 حسب النوع:
• ذكور: {self._get_students_by_gender('ذكر')}
• إناث: {self._get_students_by_gender('أنثى')}

📈 معدل الحضور العام: {self._get_overall_attendance_rate()}%
            """
            
            await update.message.reply_text(students_message)
            self.stats['commands_processed'] += 1
            
        except Exception as e:
            logger.error(f"خطأ في معالج /students: {e}")
    
    async def _handle_authorize(self, update: Update, context):
        """معالج أمر /authorize"""
        try:
            chat_id = update.effective_chat.id
            
            if not self._is_admin(chat_id):
                await update.message.reply_text("❌ هذا الأمر للمدير فقط")
                return
            
            if not context.args:
                await update.message.reply_text("❌ يرجى تحديد Chat ID للمستخدم")
                return
            
            try:
                new_chat_id = int(context.args[0])
                self.authorized_users.add(new_chat_id)
                
                # تحديث الإعدادات
                self.config["authorized_chat_ids"] = list(self.authorized_users)
                self.save_config(self.config)
                
                await update.message.reply_text(f"✅ تم إضافة المستخدم {new_chat_id} للمصرح لهم")
                
            except ValueError:
                await update.message.reply_text("❌ Chat ID غير صحيح")
            
            self.stats['commands_processed'] += 1
            
        except Exception as e:
            logger.error(f"خطأ في معالج /authorize: {e}")
    
    async def _handle_message(self, update: Update, context):
        """معالج الرسائل النصية"""
        try:
            chat_id = update.effective_chat.id
            text = update.message.text
            
            if not self._is_authorized(chat_id):
                await update.message.reply_text("❌ غير مصرح لك بالتفاعل مع البوت")
                return
            
            # يمكن إضافة منطق معالجة الرسائل النصية هنا
            # مثل البحث عن طالب بالاسم أو الكود
            
            if text.startswith("طالب:") or text.startswith("كود:"):
                # البحث عن طالب
                search_term = text.split(":", 1)[1].strip()
                student_info = self._search_student(search_term)
                
                if student_info:
                    await update.message.reply_text(student_info)
                else:
                    await update.message.reply_text("❌ لم يتم العثور على الطالب")
            else:
                await update.message.reply_text("💡 استخدم /help لمعرفة الأوامر المتاحة")
            
        except Exception as e:
            logger.error(f"خطأ في معالج الرسائل: {e}")
    
    async def _handle_callback(self, update: Update, context):
        """معالج الأزرار التفاعلية"""
        try:
            query = update.callback_query
            await query.answer()
            
            chat_id = query.message.chat_id
            
            if not self._is_authorized(chat_id):
                await query.edit_message_text("❌ غير مصرح لك بهذا الإجراء")
                return
            
            data = query.data
            
            if data == "attendance_today":
                message = self._get_attendance_report("today")
                await query.edit_message_text(message)
            
            elif data == "attendance_week":
                message = self._get_attendance_report("week")
                await query.edit_message_text(message)
            
            elif data == "attendance_detailed":
                message = self._get_attendance_report("detailed")
                await query.edit_message_text(message)
            
        except Exception as e:
            logger.error(f"خطأ في معالج الأزرار: {e}")
    
    def _is_authorized(self, chat_id: int) -> bool:
        """فحص إذا كان المستخدم مصرح له"""
        return chat_id in self.authorized_users or chat_id in self.admin_users
    
    def _is_admin(self, chat_id: int) -> bool:
        """فحص إذا كان المستخدم مدير"""
        return chat_id in self.admin_users
    
    def _get_students_count(self) -> int:
        """الحصول على عدد الطلاب"""
        # هنا يجب الاتصال بقاعدة البيانات
        return 0  # مؤقت
    
    def _get_attendance_count(self, status: str) -> int:
        """الحصول على عدد الحضور حسب الحالة"""
        # هنا يجب الاتصال بقاعدة البيانات
        return 0  # مؤقت
    
    def _get_students_by_stage(self, stage: str) -> int:
        """الحصول على عدد الطلاب حسب المرحلة"""
        return 0  # مؤقت
    
    def _get_students_by_gender(self, gender: str) -> int:
        """الحصول على عدد الطلاب حسب النوع"""
        return 0  # مؤقت
    
    def _get_overall_attendance_rate(self) -> float:
        """الحصول على معدل الحضور العام"""
        return 0.0  # مؤقت
    
    def _search_student(self, search_term: str) -> Optional[str]:
        """البحث عن طالب"""
        # هنا يجب البحث في قاعدة البيانات
        return None  # مؤقت
    
    def _get_attendance_report(self, report_type: str) -> str:
        """الحصول على تقرير الحضور"""
        if report_type == "today":
            return f"""
📊 تقرير حضور اليوم

✅ الحاضرين: {self._get_attendance_count('حاضر')}
❌ الغائبين: {self._get_attendance_count('غائب')}
⏰ المتأخرين: {self._get_attendance_count('متأخر')}

📅 التاريخ: {datetime.now().strftime('%Y-%m-%d')}
            """
        elif report_type == "week":
            return "📈 تقرير الأسبوع قيد التطوير..."
        else:
            return "📋 التقرير المفصل قيد التطوير..."
    
    async def _notify_admins(self, message: str):
        """إرسال إشعار للمدراء"""
        try:
            for admin_id in self.admin_users:
                await self.bot.send_message(chat_id=admin_id, text=f"🔔 {message}")
            self.stats['messages_sent'] += len(self.admin_users)
        except Exception as e:
            logger.error(f"فشل في إرسال إشعار للمدراء: {e}")
    
    async def send_notification(self, message: str, notification_type: str = "general"):
        """إرسال إشعار لجميع المستخدمين المصرح لهم"""
        try:
            if not self.config.get("notifications", {}).get(notification_type, True):
                return
            
            all_users = self.authorized_users.union(self.admin_users)
            
            for user_id in all_users:
                try:
                    await self.bot.send_message(chat_id=user_id, text=f"📢 {message}")
                    self.stats['messages_sent'] += 1
                except Exception as e:
                    logger.warning(f"فشل في إرسال إشعار للمستخدم {user_id}: {e}")
            
        except Exception as e:
            logger.error(f"فشل في إرسال الإشعارات: {e}")
    
    def start_bot(self):
        """بدء تشغيل البوت"""
        try:
            if not self.application or not self.config.get("enabled", False):
                logger.warning("البوت معطل أو غير مهيأ")
                return False
            
            self.is_running = True
            self.stats['start_time'] = datetime.now()
            
            # تشغيل البوت في خيط منفصل
            def run_bot():
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                self.application.run_polling()
            
            bot_thread = threading.Thread(target=run_bot, daemon=True)
            bot_thread.start()
            
            logger.info("تم بدء تشغيل بوت Telegram")
            return True
            
        except Exception as e:
            logger.error(f"فشل في تشغيل البوت: {e}")
            return False
    
    def stop_bot(self):
        """إيقاف البوت"""
        try:
            if self.application and self.is_running:
                self.application.stop()
                self.is_running = False
                logger.info("تم إيقاف بوت Telegram")
        except Exception as e:
            logger.error(f"خطأ في إيقاف البوت: {e}")
    
    def get_bot_status(self) -> Dict[str, Any]:
        """الحصول على حالة البوت"""
        return {
            "enabled": self.config.get("enabled", False),
            "is_running": self.is_running,
            "authorized_users": len(self.authorized_users),
            "admin_users": len(self.admin_users),
            "stats": self.stats.copy()
        }

# دالة مساعدة لإنشاء مدير البوت
def create_telegram_manager(config_file="telegram_config.json"):
    """إنشاء مدير بوت Telegram"""
    return TelegramBotManager(config_file)
