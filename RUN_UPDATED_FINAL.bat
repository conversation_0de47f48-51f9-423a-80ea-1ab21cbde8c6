@echo off
chcp 65001 >nul
title نظام إدارة الطلاب - الإصدار المحدث النهائي

cls
color 0F
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    نظام إدارة الطلاب                        ║
echo ║              الإصدار المحدث النهائي v6.2                    ║
echo ║                   مستر أحمد عادل                           ║
echo ║              معلم الجغرافيا والتاريخ                        ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 🎉 التحديثات الجديدة في هذا الإصدار:
echo.
echo ✅ رسالة ترحيب محدثة: "أهلاً وسهلاً أ/ أحمد عادل"
echo ✅ إحصائيات محدثة في لوحة التحكم:
echo    📊 الحضور - عدد الطلاب الحاضرين اليوم
echo    ❌ الغياب - عدد الطلاب الغائبين اليوم  
echo    ⏰ التأخير - عدد الطلاب المتأخرين اليوم
echo    📈 المعدل - متوسط درجات جميع الطلاب
echo ✅ تحديث معلومات المطور: "م/ حسام أسامة"
echo ✅ تصميم محسن للإحصائيات مع أيقونات وألوان
echo ✅ تحديث تلقائي للإحصائيات عند فتح لوحة التحكم
echo.

echo 🚀 تشغيل النظام المحدث...
echo.

REM محاولة تشغيل البرنامج
py --version >nul 2>&1
if not errorlevel 1 (
    echo ✅ Python موجود!
    py main.py
    goto end
)

python --version >nul 2>&1
if not errorlevel 1 (
    echo ✅ Python موجود!
    python main.py
    goto end
)

python3 --version >nul 2>&1
if not errorlevel 1 (
    echo ✅ Python موجود!
    python3 main.py
    goto end
)

echo ❌ Python غير موجود!
echo يرجى تثبيت Python من: https://python.org/downloads
start https://python.org/downloads

:end
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    دليل الاستخدام المحدث                    ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.
echo 🔐 بيانات تسجيل الدخول:
echo    👤 اسم المستخدم: admin
echo    🔑 كلمة المرور: admin123
echo.
echo 🏠 لوحة التحكم المحدثة:
echo    🎯 رسالة ترحيب: "أهلاً وسهلاً أ/ أحمد عادل"
echo    📊 إحصائيات تفاعلية مع أيقونات ملونة:
echo       👥 الحضور - الطلاب الحاضرين اليوم
echo       ❌ الغياب - الطلاب الغائبين اليوم
echo       ⏰ التأخير - الطلاب المتأخرين اليوم
echo       📈 المعدل - متوسط الدرجات العام
echo.
echo 🧭 شريط التنقل الكامل:
echo    🏠 الرئيسية - لوحة التحكم المحدثة
echo    👥 إدارة الطلاب - إضافة وتعديل الطلاب
echo    👨‍👩‍👧‍👦 إدارة المجموعات - تنظيم الطلاب
echo    💰 إدارة المدفوعات - نظام المدفوعات الشهرية
echo    📋 تسجيل الحضور - مع رسائل تلقائية
echo    📊 إدارة الدرجات - مع رسائل ذكية
echo    📈 التقارير - تقارير شاملة
echo    ⚙️ الإعدادات - مع معلومات المطور المحدثة
echo    🚪 تسجيل الخروج - خروج آمن
echo.
echo 💰 نظام المدفوعات المتكامل:
echo    ✅ تسجيل مدفوعات الجغرافيا والتاريخ
echo    ✅ حساب تلقائي للمبالغ والإجماليات
echo    ✅ إحصائيات يومية وشهرية
echo    ✅ فلترة وبحث متقدم
echo    ✅ إعدادات أسعار مرنة
echo.
echo 📱 نظام الرسائل التلقائية:
echo    ✅ رسائل غياب فورية لأولياء الأمور
echo    ✅ تهنئة بالدرجات الممتازة (85%+)
echo    ✅ تنبيهات للدرجات المنخفضة (أقل من 50%)
echo    ✅ إرسال عبر WhatsApp و Telegram
echo.
echo 🎓 معلومات المطور المحدثة:
echo    👨‍💻 م/ حسام أسامة
echo    🏢 مهندس برمجيات - مطور تطبيقات
echo    🎯 مصمم خصيصاً لمستر أحمد عادل
echo.
echo 📊 مميزات الإحصائيات الجديدة:
echo    🔄 تحديث تلقائي عند فتح لوحة التحكم
echo    🎨 تصميم جذاب مع تدرجات لونية
echo    📱 أيقونات تعبيرية واضحة
echo    📈 بيانات حقيقية من قاعدة البيانات
echo.
echo 🏆 النظام الآن محدث ومتكامل بالكامل!
echo    أقوى نظام إدارة طلاب عربي مع إحصائيات تفاعلية
echo.
echo 📞 للدعم الفني: راجع تبويب "حول التطبيق" في الإعدادات
echo.
pause
