# 📦 تعليمات تحميل نظام إدارة الطلاب

## 🎯 الملفات المطلوب تحميلها

لتحميل نظام إدارة الطلاب كاملاً، تحتاج إلى تحميل الملفات والمجلدات التالية:

### 📄 الملفات الرئيسية:
```
✅ main.py                 - الملف الرئيسي للتطبيق
✅ run.py                  - ملف التشغيل المحسن  
✅ launch.py               - ملف التشغيل الشامل
✅ config.py               - ملف التكوين العام
✅ requirements.txt        - قائمة المتطلبات
```

### 🚀 ملفات التشغيل:
```
✅ run_app.bat            - ملف التشغيل الرئيسي (الأهم!)
✅ start.bat              - ملف تشغيل بديل
✅ build.bat              - ملف بناء EXE
```

### 🔧 ملفات الأدوات:
```
✅ fix_imports.py         - إصلاح مشاكل الاستيراد
✅ test_app.py            - اختبار التطبيق
✅ build_exe.py           - بناء ملف EXE
✅ compress_project.py    - ضغط المشروع
✅ create_final_zip.py    - إنشاء ZIP نهائي
```

### 📖 ملفات التوثيق:
```
✅ README.md              - ملف التوثيق الأساسي
✅ README_FINAL.md        - دليل المشروع الكامل
✅ USER_GUIDE.md          - دليل المستخدم (مهم جداً!)
✅ INSTALLATION.md        - دليل التثبيت
✅ PROJECT_SUMMARY.md     - ملخص المشروع
✅ DOWNLOAD_INSTRUCTIONS.md - هذا الملف
```

### 📁 المجلدات المطلوبة:

#### مجلد src/ (الكود المصدري):
```
src/
├── __init__.py
├── database/
│   ├── __init__.py
│   └── database_manager.py
├── models/
│   ├── __init__.py
│   ├── student.py
│   ├── attendance.py
│   └── grades.py
├── ui/
│   ├── __init__.py
│   ├── login_window.py
│   ├── main_window.py
│   ├── students_window.py
│   ├── attendance_window.py
│   ├── grades_window.py
│   ├── reports_window.py
│   └── settings_window.py
├── utils/
│   ├── __init__.py
│   ├── auth.py
│   └── styles.py
└── reports/
    ├── __init__.py
    ├── pdf_generator.py
    └── excel_generator.py
```

#### مجلد data/ (قاعدة البيانات):
```
data/
└── students.db (سيتم إنشاؤه تلقائياً)
```

#### مجلد assets/ (الموارد):
```
assets/
└── (أيقونات وصور - اختياري)
```

### 📂 المجلدات التي ستُنشأ تلقائياً:
```
logs/      - ملفات السجلات
backups/   - النسخ الاحتياطية  
exports/   - الملفات المصدرة
temp/      - الملفات المؤقتة
```

## 🚀 طريقة التحميل والتشغيل

### الخطوة 1: تحميل الملفات
1. حمل جميع الملفات المذكورة أعلاه
2. ضعها في مجلد واحد (مثل: `StudentManagementSystem`)
3. تأكد من الحفاظ على هيكل المجلدات

### الخطوة 2: التشغيل
1. **انقر مرتين على `run_app.bat`** (الطريقة الأسهل)
2. اختر الخيار المناسب من القائمة
3. استخدم `admin` و `admin123` لتسجيل الدخول

### الخطوة 3: في حالة وجود مشاكل
1. شغل `run_app.bat` واختر الخيار 4 لإصلاح المشاكل
2. أو شغل `python fix_imports.py` مباشرة
3. راجع `USER_GUIDE.md` للحصول على مساعدة مفصلة

## 📋 قائمة تحقق سريعة

قبل التشغيل، تأكد من وجود:
- [ ] ملف `run_app.bat`
- [ ] ملف `main.py`
- [ ] ملف `requirements.txt`
- [ ] مجلد `src/` مع جميع الملفات
- [ ] ملف `USER_GUIDE.md`

## 🎓 معلومات إضافية

### بيانات تسجيل الدخول:
- **اسم المستخدم:** `admin`
- **كلمة المرور:** `admin123`

### الملفات الأكثر أهمية:
1. `run_app.bat` - للتشغيل
2. `USER_GUIDE.md` - للمساعدة
3. `main.py` - الملف الرئيسي
4. مجلد `src/` - الكود المصدري

### نصائح:
- احفظ نسخة احتياطية من الملفات
- راجع `USER_GUIDE.md` للحصول على دليل شامل
- في حالة وجود مشاكل، راجع `INSTALLATION.md`

---

## 📞 الدعم

إذا واجهت أي مشكلة:
1. راجع `USER_GUIDE.md` أولاً
2. شغل `python test_app.py` للتشخيص
3. تحقق من مجلد `logs/` للأخطاء

---

**🎉 نظام إدارة الطلاب جاهز للاستخدام!**
**تم التطوير خصيصاً لمستر أحمد عادل**
