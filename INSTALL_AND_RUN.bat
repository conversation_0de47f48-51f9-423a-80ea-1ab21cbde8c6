@echo off
title Student Management System - Install and Run

echo.
echo ========================================
echo   Student Management System
echo   Install and Run
echo ========================================
echo.

echo Checking system requirements...
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo Python is not installed on this system.
    echo.
    echo Option 1: Install Python
    echo --------------------------
    echo 1. Go to: https://python.org/downloads
    echo 2. Download Python 3.8 or newer
    echo 3. During installation, CHECK "Add Python to PATH"
    echo 4. After installation, run this file again
    echo.
    echo Option 2: Use Portable Version
    echo ------------------------------
    echo We can try to download a portable Python version...
    echo.
    
    set /p choice="Do you want to try portable Python? (y/n): "
    if /i "%choice%"=="y" goto portable_python
    if /i "%choice%"=="yes" goto portable_python
    
    echo.
    echo Please install Python manually and run this file again.
    echo.
    pause
    exit /b 1
)

echo Python found! Version:
python --version
echo.

echo Installing required packages...
pip install PyQt5 --quiet
if errorlevel 1 (
    echo Failed to install PyQt5
    echo Trying alternative installation...
    pip install PyQt5 --user --quiet
)

echo.
echo Starting Student Management System...
echo.

if exist "main.py" (
    python main.py
) else (
    echo main.py not found!
    echo Please make sure all files are in the correct location.
)

goto end

:portable_python
echo.
echo Attempting to use portable Python...
echo This is experimental and may not work.
echo.

REM Try to find Python in common locations
set "PYTHON_PATHS=C:\Python39\python.exe;C:\Python38\python.exe;C:\Python37\python.exe;%LOCALAPPDATA%\Programs\Python\Python39\python.exe;%LOCALAPPDATA%\Programs\Python\Python38\python.exe"

for %%p in (%PYTHON_PATHS%) do (
    if exist "%%p" (
        echo Found Python at: %%p
        "%%p" --version
        echo.
        echo Installing PyQt5...
        "%%p" -m pip install PyQt5 --quiet --user
        echo.
        echo Starting application...
        "%%p" main.py
        goto end
    )
)

echo No Python installation found.
echo Please install Python manually from: https://python.org
echo.

:end
echo.
echo Login credentials:
echo Username: admin
echo Password: admin123
echo.
pause
