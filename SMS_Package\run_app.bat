@echo off
chcp 65001 >nul
title نظام إدارة الطلاب - مستر أحمد عادل

cls
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    نظام إدارة الطلاب                        ║
echo ║                   مستر أحمد عادل                           ║
echo ║                                                              ║
echo ║              Student Management System                       ║
echo ║                   Version 1.0                               ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

REM التحقق من وجود Python
echo 🔍 التحقق من متطلبات النظام...
python --version >nul 2>&1
if errorlevel 1 (
    echo.
    echo ❌ خطأ: Python غير مثبت على النظام
    echo.
    echo 📥 يرجى تحميل وتثبيت Python من الرابط التالي:
    echo    https://python.org/downloads
    echo.
    echo 💡 تأكد من تحديد "Add Python to PATH" أثناء التثبيت
    echo.
    pause
    exit /b 1
)

REM التحقق من وجود pip
pip --version >nul 2>&1
if errorlevel 1 (
    echo ❌ خطأ: pip غير متوفر
    echo.
    pause
    exit /b 1
)

echo ✅ Python متوفر
echo.

REM التحقق من وجود ملف المتطلبات
if not exist "requirements.txt" (
    echo ❌ خطأ: ملف requirements.txt غير موجود
    echo.
    pause
    exit /b 1
)

REM تثبيت المتطلبات بصمت
echo 📦 التحقق من المكتبات المطلوبة...
pip install -r requirements.txt --quiet --disable-pip-version-check

if errorlevel 1 (
    echo.
    echo ⚠️ تحذير: فشل في تثبيت بعض المكتبات
    echo سيتم المحاولة مع المكتبات المتوفرة...
    echo.
)

echo ✅ تم التحقق من المتطلبات
echo.

REM اختيار طريقة التشغيل
echo 🚀 اختر طريقة التشغيل:
echo.
echo [1] التشغيل العادي (main.py)
echo [2] التشغيل المحسن (run.py) 
echo [3] التشغيل الشامل (launch.py)
echo [4] إصلاح المشاكل أولاً (fix_imports.py)
echo [5] اختبار التطبيق (test_app.py)
echo.

set /p choice="اختر رقم (1-5) أو اضغط Enter للتشغيل العادي: "

if "%choice%"=="" set choice=1
if "%choice%"=="1" goto run_main
if "%choice%"=="2" goto run_enhanced
if "%choice%"=="3" goto run_comprehensive
if "%choice%"=="4" goto fix_issues
if "%choice%"=="5" goto test_app

echo ❌ اختيار غير صحيح
goto end

:run_main
echo.
echo 🎯 تشغيل التطبيق (الطريقة العادية)...
echo.
python main.py
goto end

:run_enhanced
echo.
echo 🎯 تشغيل التطبيق (الطريقة المحسنة)...
echo.
python run.py
goto end

:run_comprehensive
echo.
echo 🎯 تشغيل التطبيق (الطريقة الشاملة)...
echo.
python launch.py
goto end

:fix_issues
echo.
echo 🔧 إصلاح مشاكل التطبيق...
echo.
python fix_imports.py
if errorlevel 1 (
    echo.
    echo ❌ فشل في إصلاح المشاكل
    goto end
)
echo.
echo ✅ تم إصلاح المشاكل بنجاح
echo.
set /p run_after="هل تريد تشغيل التطبيق الآن؟ (y/n): "
if /i "%run_after%"=="y" goto run_main
if /i "%run_after%"=="yes" goto run_main
if /i "%run_after%"=="ن" goto run_main
if /i "%run_after%"=="نعم" goto run_main
goto end

:test_app
echo.
echo 🧪 اختبار التطبيق...
echo.
python test_app.py
if errorlevel 1 (
    echo.
    echo ❌ فشل في بعض الاختبارات
    echo يرجى مراجعة الأخطاء أعلاه
) else (
    echo.
    echo ✅ نجحت جميع الاختبارات
    echo.
    set /p run_after="هل تريد تشغيل التطبيق الآن؟ (y/n): "
    if /i "%run_after%"=="y" goto run_main
    if /i "%run_after%"=="yes" goto run_main
    if /i "%run_after%"=="ن" goto run_main
    if /i "%run_after%"=="نعم" goto run_main
)
goto end

:end
echo.
echo ═══════════════════════════════════════════════════════════════
echo.
echo 💡 نصائح مفيدة:
echo    • اسم المستخدم الافتراضي: admin
echo    • كلمة المرور الافتراضية: admin123
echo    • راجع ملف USER_GUIDE.md للحصول على دليل الاستخدام
echo    • في حالة وجود مشاكل، شغل الخيار رقم 4 لإصلاحها
echo.
echo 📞 للدعم الفني:
echo    • راجع ملف INSTALLATION.md
echo    • تحقق من مجلد logs للأخطاء
echo.
echo شكراً لاستخدام نظام إدارة الطلاب! 🎓
echo.
pause
