# -*- coding: utf-8 -*-
"""
رسائل الحوار المحسنة
Enhanced Message Boxes
"""

from PyQt5.QtWidgets import QMessageBox, QPushButton
from PyQt5.QtCore import Qt

def show_error_message(parent, title, message):
    """إظهار رسالة خطأ محسنة"""
    msg = QMessageBox(parent)
    msg.setIcon(QMessageBox.Critical)
    msg.setWindowTitle(title)
    msg.setText(message)
    msg.setStyleSheet("""
        QMessageBox {
            background-color: white;
            color: black;
            font-size: 16px;
            font-weight: bold;
            border: 2px solid #e74c3c;
            border-radius: 8px;
        }
        QMessageBox QLabel {
            color: black;
            font-size: 16px;
            padding: 10px;
        }
        QMessageBox QPushButton {
            background-color: #e74c3c;
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 6px;
            font-size: 14px;
            font-weight: bold;
            min-width: 80px;
            margin: 5px;
        }
        QMessageBox QPushButton:hover {
            background-color: #c0392b;
            transform: translateY(-1px);
        }
        QMessageBox QPushButton:pressed {
            background-color: #a93226;
            transform: translateY(1px);
        }
    """)
    return msg.exec_()

def show_success_message(parent, title, message):
    """إظهار رسالة نجاح محسنة"""
    msg = QMessageBox(parent)
    msg.setIcon(QMessageBox.Information)
    msg.setWindowTitle(title)
    msg.setText(message)
    msg.setStyleSheet("""
        QMessageBox {
            background-color: white;
            color: black;
            font-size: 16px;
            font-weight: bold;
            border: 2px solid #27ae60;
            border-radius: 8px;
        }
        QMessageBox QLabel {
            color: black;
            font-size: 16px;
            padding: 10px;
        }
        QMessageBox QPushButton {
            background-color: #27ae60;
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 6px;
            font-size: 14px;
            font-weight: bold;
            min-width: 80px;
            margin: 5px;
        }
        QMessageBox QPushButton:hover {
            background-color: #229954;
            transform: translateY(-1px);
        }
        QMessageBox QPushButton:pressed {
            background-color: #1e8449;
            transform: translateY(1px);
        }
    """)
    return msg.exec_()

def show_warning_message(parent, title, message):
    """إظهار رسالة تحذير محسنة"""
    msg = QMessageBox(parent)
    msg.setIcon(QMessageBox.Warning)
    msg.setWindowTitle(title)
    msg.setText(message)
    msg.setStyleSheet("""
        QMessageBox {
            background-color: white;
            color: black;
            font-size: 16px;
            font-weight: bold;
            border: 2px solid #f39c12;
            border-radius: 8px;
        }
        QMessageBox QLabel {
            color: black;
            font-size: 16px;
            padding: 10px;
        }
        QMessageBox QPushButton {
            background-color: #f39c12;
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 6px;
            font-size: 14px;
            font-weight: bold;
            min-width: 80px;
            margin: 5px;
        }
        QMessageBox QPushButton:hover {
            background-color: #e67e22;
            transform: translateY(-1px);
        }
        QMessageBox QPushButton:pressed {
            background-color: #d68910;
            transform: translateY(1px);
        }
    """)
    return msg.exec_()

def show_question_message(parent, title, message):
    """إظهار رسالة سؤال محسنة"""
    msg = QMessageBox(parent)
    msg.setIcon(QMessageBox.Question)
    msg.setWindowTitle(title)
    msg.setText(message)
    msg.setStandardButtons(QMessageBox.Yes | QMessageBox.No)
    
    # تخصيص النصوص
    yes_button = msg.button(QMessageBox.Yes)
    no_button = msg.button(QMessageBox.No)
    yes_button.setText("نعم")
    no_button.setText("لا")
    
    msg.setStyleSheet("""
        QMessageBox {
            background-color: white;
            color: black;
            font-size: 16px;
            font-weight: bold;
            border: 2px solid #3498db;
            border-radius: 8px;
        }
        QMessageBox QLabel {
            color: black;
            font-size: 16px;
            padding: 10px;
        }
        QMessageBox QPushButton {
            background-color: #3498db;
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 6px;
            font-size: 14px;
            font-weight: bold;
            min-width: 80px;
            margin: 5px;
        }
        QMessageBox QPushButton:hover {
            background-color: #2980b9;
            transform: translateY(-1px);
        }
        QMessageBox QPushButton:pressed {
            background-color: #21618c;
            transform: translateY(1px);
        }
    """)
    
    result = msg.exec_()
    return result == QMessageBox.Yes

def show_confirmation_dialog(parent, title, message, confirm_text="تأكيد", cancel_text="إلغاء"):
    """إظهار حوار تأكيد محسن"""
    msg = QMessageBox(parent)
    msg.setIcon(QMessageBox.Question)
    msg.setWindowTitle(title)
    msg.setText(message)
    
    # إضافة أزرار مخصصة
    confirm_button = msg.addButton(confirm_text, QMessageBox.AcceptRole)
    cancel_button = msg.addButton(cancel_text, QMessageBox.RejectRole)
    
    msg.setStyleSheet("""
        QMessageBox {
            background-color: white;
            color: black;
            font-size: 16px;
            font-weight: bold;
            border: 2px solid #9b59b6;
            border-radius: 8px;
        }
        QMessageBox QLabel {
            color: black;
            font-size: 16px;
            padding: 10px;
        }
        QMessageBox QPushButton {
            background-color: #9b59b6;
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 6px;
            font-size: 14px;
            font-weight: bold;
            min-width: 80px;
            margin: 5px;
        }
        QMessageBox QPushButton:hover {
            background-color: #8e44ad;
            transform: translateY(-1px);
        }
        QMessageBox QPushButton:pressed {
            background-color: #7d3c98;
            transform: translateY(1px);
        }
    """)
    
    result = msg.exec_()
    return msg.clickedButton() == confirm_button
