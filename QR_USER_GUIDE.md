# دليل المستخدم - نظام QR Code للحضور الذكي

## 📱 مقدمة النظام

نظام QR Code للحضور الذكي هو حل متطور لتسجيل حضور الطلاب باستخدام تقنية QR Code المشفرة مع الكاميرا. يوفر النظام حماية متقدمة من التلاعب، تنبيهات فورية، وإشعارات تلقائية لأولياء الأمور.

## 🎯 المميزات الرئيسية

### 🔗 إنشاء QR Code مشفر
- QR Code فريد لكل طالب
- تشفير متقدم مع توقيع رقمي
- مدة صلاحية قابلة للتخصيص
- حماية من التلاعب والنسخ

### 📱 مسح ذكي بالكاميرا
- مسح مباشر عبر كاميرا الجهاز
- تحقق فوري من صحة QR Code
- واجهة سهلة الاستخدام
- دعم كاميرات متعددة

### ⏰ إدارة التوقيتات
- جدولة الحصص بدقة
- نوافذ حضور مرنة
- تحديد حالة الحضور تلقائياً
- إعدادات تأخير قابلة للتخصيص

### 🚨 تنبيهات فورية
- تنبيه عند المسح خارج التوقيت
- تسجيل محاولات الغش
- إشعارات للمعلم
- سجل شامل للتنبيهات

### 📱 إشعارات أولياء الأمور
- رسائل تلقائية عند الحضور
- تفاصيل الحصة والتوقيت
- دعم WhatsApp و Telegram
- رسائل مخصصة

## 🚀 البدء السريع

### 1️⃣ تشغيل النظام
```bash
# تشغيل ملف البرنامج
RUN_QR_SYSTEM.bat
```

### 2️⃣ تسجيل الدخول
- **اسم المستخدم:** admin
- **كلمة المرور:** admin123

### 3️⃣ الوصول لنظام QR Code
- من شريط التنقل اختر "📱 حضور QR Code"

## 📋 دليل الاستخدام التفصيلي

### 🔗 إنشاء QR Codes للطلاب

#### الخطوات:
1. **اذهب لتبويب "🔗 إنشاء QR Codes"**
2. **اضبط مدة الصلاحية:**
   - افتراضي: 24 ساعة
   - يمكن تعديلها من 1 ساعة إلى أسبوع
3. **اختر الطلاب:**
   - ✅ اختيار الكل: لجميع الطلاب
   - ❌ إلغاء الكل: لإلغاء الاختيار
   - اختيار فردي: بالضغط على checkbox
4. **إنشاء QR Codes:**
   - 🔗 إنشاء: لطالب واحد
   - 🔗 إنشاء للمحددين: للطلاب المختارين

#### النتيجة:
- ملفات PNG محفوظة في مجلد المشروع
- اسم الملف: `qr_[كود_الطالب].png`
- QR Code يحتوي على معلومات الطالب مشفرة

### 📅 إدارة الحصص

#### إضافة حصة جديدة:
1. **املأ بيانات الحصة:**
   - اسم الحصة: مثل "حصة الجغرافيا - الصف الأول"
   - المادة: جغرافيا أو تاريخ
   - وقت البداية والنهاية
   - المجموعة (اختياري)

2. **اختر أيام الأسبوع:**
   - حدد الأيام التي تُدرس فيها الحصة
   - يمكن اختيار أيام متعددة

3. **اضبط إعدادات التوقيت:**
   - **السماح قبل:** كم دقيقة قبل بداية الحصة (افتراضي: 15 دقيقة)
   - **السماح بعد:** كم دقيقة بعد بداية الحصة (افتراضي: 30 دقيقة)
   - **حد التأخير:** متى يُعتبر الطالب متأخراً (افتراضي: 10 دقائق)

#### مثال على الإعدادات:
```
حصة الجغرافيا - الصف الثاني الثانوي
المادة: جغرافيا
التوقيت: 08:00 - 09:00
الأيام: الأحد، الثلاثاء، الخميس
السماح قبل: 15 دقيقة (07:45)
السماح بعد: 30 دقيقة (08:30)
حد التأخير: 10 دقائق (08:10)
```

### 📱 مسح QR Code

#### بدء المسح:
1. **اذهب لتبويب "📱 مسح QR Code"**
2. **اضغط "📷 بدء المسح"**
3. **وجه الكاميرا نحو QR Code**
4. **انتظر التسجيل التلقائي**

#### حالات المسح:
- **✅ نجح المسح:** تسجيل الحضور حسب التوقيت
- **❌ QR Code غير صحيح:** رسالة خطأ
- **⚠️ خارج التوقيت:** تنبيه + تسجيل في التنبيهات

#### تحديد حالة الحضور:
- **حاضر:** المسح قبل أو في وقت بداية الحصة
- **متأخر:** المسح بعد بداية الحصة ضمن حد التأخير
- **خارج التوقيت:** المسح خارج نافذة الحضور المسموحة

### 🚨 إدارة التنبيهات

#### أنواع التنبيهات:
1. **مسح قبل الحصة:** الطالب مسح QR Code قبل نافذة الحضور
2. **مسح بعد الحصة:** الطالب مسح QR Code بعد انتهاء نافذة الحضور
3. **لا توجد حصة:** الطالب مسح QR Code في وقت لا توجد فيه حصة

#### معالجة التنبيهات:
- **✅ معالج:** تمييز تنبيه واحد كمعالج
- **✅ تمييز الكل كمعالج:** معالجة جميع التنبيهات

### 📊 التقارير والإحصائيات

#### تقرير الحضور اليومي:
- عرض جميع عمليات المسح لليوم
- تفاصيل كل طالب وحالة حضوره
- معلومات الحصة والتوقيت

#### الإحصائيات المباشرة:
- عدد الطلاب الحاضرين
- عدد المتأخرين
- عدد التنبيهات
- إحصائيات الحصة الحالية

## 🔒 الأمان والحماية

### تشفير QR Code:
- **تشفير AES-256:** حماية قوية للبيانات
- **توقيع رقمي HMAC:** منع التلاعب
- **مدة صلاحية:** انتهاء تلقائي للرمز
- **مفتاح سري:** حماية إضافية

### حماية من التلاعب:
- **فحص التوقيع:** التحقق من صحة QR Code
- **فحص الصلاحية:** رفض الرموز المنتهية
- **تسجيل المحاولات:** حفظ محاولات الغش
- **تنبيهات فورية:** إشعار المعلم

## 🔧 حل المشاكل الشائعة

### مشاكل الكاميرا:
```
❌ المشكلة: "فشل في فتح الكاميرا"
✅ الحل:
1. تأكد من توصيل الكاميرا
2. أغلق التطبيقات الأخرى التي تستخدم الكاميرا
3. أعد تشغيل النظام
4. جرب كاميرا أخرى
```

### مشاكل مسح QR Code:
```
❌ المشكلة: "لا يتم اكتشاف QR Code"
✅ الحل:
1. تأكد من وضوح الصورة
2. حسن الإضاءة
3. اقترب أو ابتعد عن QR Code
4. تأكد من صلاحية الرمز
```

### مشاكل التثبيت:
```
❌ المشكلة: "فشل في تثبيت المكتبات"
✅ الحل:
1. شغل Command Prompt كمدير
2. pip install --upgrade pip
3. pip install opencv-python-headless
4. pip install -r requirements_qr.txt
```

## 📱 إشعارات أولياء الأمور

### إعداد الرسائل:
1. **تأكد من إدخال أرقام أولياء الأمور في بيانات الطلاب**
2. **الرسائل ترسل تلقائياً عند تسجيل الحضور**
3. **تحتوي على:**
   - اسم الطالب
   - حالة الحضور
   - اسم الحصة والمادة
   - التاريخ والوقت

### مثال على الرسالة:
```
🎓 إشعار حضور الطالب

الطالب: أحمد محمد علي
الحالة: حاضر
الحصة: حصة الجغرافيا - الصف الثاني
التاريخ والوقت: 2024-01-15 08:05

نظام إدارة الطلاب
مستر أحمد عادل - معلم الجغرافيا والتاريخ
```

## 🎯 نصائح للاستخدام الأمثل

### للمعلم:
1. **أنشئ QR Codes قبل الحصة بيوم**
2. **اطبع الرموز على ورق جيد**
3. **احتفظ بنسخة احتياطية رقمية**
4. **راجع التنبيهات يومياً**
5. **تأكد من إعدادات الحصص**

### للطلاب:
1. **احتفظ بـ QR Code في مكان آمن**
2. **لا تشارك الرمز مع أحد**
3. **تأكد من وضوح الرمز عند المسح**
4. **امسح الرمز في الوقت المحدد**
5. **أبلغ المعلم عن أي مشاكل**

## 📞 الدعم الفني

### معلومات المطور:
- **المطور:** م/ حسام أسامة
- **التخصص:** مهندس برمجيات - مطور تطبيقات
- **مصمم خصيصاً لـ:** مستر أحمد عادل - معلم الجغرافيا والتاريخ

### للحصول على المساعدة:
1. راجع هذا الدليل أولاً
2. تحقق من قسم "حل المشاكل الشائعة"
3. راجع تبويب "حول التطبيق" في الإعدادات
4. اتصل بالدعم الفني إذا لزم الأمر

---

**🏆 أول نظام QR Code ذكي للحضور باللغة العربية!**

*نظام متكامل مع حماية متقدمة وتنبيهات فورية*
