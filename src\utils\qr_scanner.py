# -*- coding: utf-8 -*-
"""
ماسح QR Code مع الكاميرا
QR Code Scanner with Camera
"""

import cv2
import numpy as np
from pyzbar import pyzbar
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                            QPushButton, QTextEdit, QFrame, QMessageBox)
from PyQt5.QtCore import QThread, pyqtSignal, QTimer, Qt
from PyQt5.QtGui import QImage, QPixmap, QFont
from datetime import datetime
import json

class QRScannerThread(QThread):
    """خيط مسح QR Code"""
    
    qr_detected = pyqtSignal(str)  # إشارة عند اكتشاف QR Code
    frame_ready = pyqtSignal(np.ndarray)  # إشارة عند جاهزية الإطار
    error_occurred = pyqtSignal(str)  # إشارة عند حدوث خطأ
    
    def __init__(self):
        super().__init__()
        self.camera = None
        self.running = False
        self.camera_index = 0
        
    def start_scanning(self, camera_index=0):
        """بدء مسح QR Code"""
        self.camera_index = camera_index
        self.running = True
        self.start()
    
    def stop_scanning(self):
        """إيقاف مسح QR Code"""
        self.running = False
        if self.camera:
            self.camera.release()
        self.quit()
        self.wait()
    
    def run(self):
        """تشغيل خيط المسح"""
        try:
            # فتح الكاميرا
            self.camera = cv2.VideoCapture(self.camera_index)
            
            if not self.camera.isOpened():
                self.error_occurred.emit("فشل في فتح الكاميرا")
                return
            
            # تحسين إعدادات الكاميرا
            self.camera.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
            self.camera.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)
            self.camera.set(cv2.CAP_PROP_FPS, 30)
            
            while self.running:
                ret, frame = self.camera.read()
                
                if not ret:
                    continue
                
                # البحث عن QR Codes في الإطار
                qr_codes = pyzbar.decode(frame)
                
                # رسم مربعات حول QR Codes المكتشفة
                for qr_code in qr_codes:
                    # استخراج البيانات
                    qr_data = qr_code.data.decode('utf-8')
                    
                    # رسم مربع حول QR Code
                    points = qr_code.polygon
                    if len(points) == 4:
                        pts = np.array([[point.x, point.y] for point in points], np.int32)
                        cv2.polylines(frame, [pts], True, (0, 255, 0), 3)
                        
                        # إضافة نص
                        cv2.putText(frame, "QR Code Detected", 
                                  (qr_code.rect.left, qr_code.rect.top - 10),
                                  cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
                    
                    # إرسال البيانات
                    self.qr_detected.emit(qr_data)
                
                # إرسال الإطار للعرض
                self.frame_ready.emit(frame)
                
                # تأخير قصير
                self.msleep(33)  # ~30 FPS
                
        except Exception as e:
            self.error_occurred.emit(f"خطأ في المسح: {str(e)}")
        finally:
            if self.camera:
                self.camera.release()

class QRScannerWidget(QWidget):
    """واجهة مسح QR Code"""
    
    qr_scanned = pyqtSignal(dict)  # إشارة عند مسح QR Code صحيح
    
    def __init__(self, qr_generator):
        super().__init__()
        self.qr_generator = qr_generator
        self.scanner_thread = QRScannerThread()
        self.last_scan_time = None
        self.scan_cooldown = 2  # ثواني بين المسحات
        
        self.init_ui()
        self.setup_connections()
        
    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        layout = QVBoxLayout()
        
        # عنوان
        title_label = QLabel("📱 مسح QR Code للحضور")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                font-size: 20px;
                font-weight: bold;
                color: #2c3e50;
                margin: 10px;
                padding: 10px;
                background-color: #ecf0f1;
                border-radius: 8px;
            }
        """)
        layout.addWidget(title_label)
        
        # إطار الكاميرا
        self.camera_frame = QLabel("📷 اضغط 'بدء المسح' لتشغيل الكاميرا")
        self.camera_frame.setAlignment(Qt.AlignCenter)
        self.camera_frame.setMinimumSize(640, 480)
        self.camera_frame.setStyleSheet("""
            QLabel {
                border: 2px dashed #bdc3c7;
                border-radius: 10px;
                background-color: #f8f9fa;
                font-size: 16px;
                color: #7f8c8d;
            }
        """)
        layout.addWidget(self.camera_frame)
        
        # أزرار التحكم
        buttons_layout = QHBoxLayout()
        
        self.start_button = QPushButton("📷 بدء المسح")
        self.start_button.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                padding: 12px 20px;
                border-radius: 6px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        self.start_button.clicked.connect(self.start_scanning)
        
        self.stop_button = QPushButton("⏹️ إيقاف المسح")
        self.stop_button.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                padding: 12px 20px;
                border-radius: 6px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        self.stop_button.clicked.connect(self.stop_scanning)
        self.stop_button.setEnabled(False)
        
        buttons_layout.addWidget(self.start_button)
        buttons_layout.addWidget(self.stop_button)
        buttons_layout.addStretch()
        
        layout.addLayout(buttons_layout)
        
        # منطقة النتائج
        results_frame = QFrame()
        results_frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 1px solid #ddd;
                border-radius: 8px;
                margin: 10px 0;
            }
        """)
        results_layout = QVBoxLayout()
        
        results_title = QLabel("📋 نتائج المسح:")
        results_title.setStyleSheet("font-weight: bold; font-size: 14px; margin: 5px;")
        results_layout.addWidget(results_title)
        
        self.results_text = QTextEdit()
        self.results_text.setMaximumHeight(150)
        self.results_text.setStyleSheet("""
            QTextEdit {
                border: 1px solid #ddd;
                border-radius: 4px;
                padding: 8px;
                font-family: 'Courier New', monospace;
            }
        """)
        results_layout.addWidget(self.results_text)
        
        results_frame.setLayout(results_layout)
        layout.addWidget(results_frame)
        
        # حالة المسح
        self.status_label = QLabel("⏸️ المسح متوقف")
        self.status_label.setAlignment(Qt.AlignCenter)
        self.status_label.setStyleSheet("""
            QLabel {
                background-color: #f39c12;
                color: white;
                padding: 8px;
                border-radius: 4px;
                font-weight: bold;
            }
        """)
        layout.addWidget(self.status_label)
        
        self.setLayout(layout)
    
    def setup_connections(self):
        """إعداد الاتصالات"""
        self.scanner_thread.qr_detected.connect(self.on_qr_detected)
        self.scanner_thread.frame_ready.connect(self.update_camera_frame)
        self.scanner_thread.error_occurred.connect(self.on_error)
    
    def start_scanning(self):
        """بدء مسح QR Code"""
        try:
            self.scanner_thread.start_scanning()
            self.start_button.setEnabled(False)
            self.stop_button.setEnabled(True)
            self.status_label.setText("🔍 جاري المسح...")
            self.status_label.setStyleSheet("""
                QLabel {
                    background-color: #27ae60;
                    color: white;
                    padding: 8px;
                    border-radius: 4px;
                    font-weight: bold;
                }
            """)
            self.results_text.append(f"[{datetime.now().strftime('%H:%M:%S')}] بدء مسح QR Code...")
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في بدء المسح: {str(e)}")
    
    def stop_scanning(self):
        """إيقاف مسح QR Code"""
        try:
            self.scanner_thread.stop_scanning()
            self.start_button.setEnabled(True)
            self.stop_button.setEnabled(False)
            self.status_label.setText("⏸️ المسح متوقف")
            self.status_label.setStyleSheet("""
                QLabel {
                    background-color: #f39c12;
                    color: white;
                    padding: 8px;
                    border-radius: 4px;
                    font-weight: bold;
                }
            """)
            self.camera_frame.setText("📷 اضغط 'بدء المسح' لتشغيل الكاميرا")
            self.camera_frame.setStyleSheet("""
                QLabel {
                    border: 2px dashed #bdc3c7;
                    border-radius: 10px;
                    background-color: #f8f9fa;
                    font-size: 16px;
                    color: #7f8c8d;
                }
            """)
            self.results_text.append(f"[{datetime.now().strftime('%H:%M:%S')}] تم إيقاف المسح.")
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في إيقاف المسح: {str(e)}")
    
    def update_camera_frame(self, frame):
        """تحديث إطار الكاميرا"""
        try:
            # تحويل الإطار إلى RGB
            rgb_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
            h, w, ch = rgb_frame.shape
            bytes_per_line = ch * w
            
            # تحويل إلى QImage
            qt_image = QImage(rgb_frame.data, w, h, bytes_per_line, QImage.Format_RGB888)
            
            # تحويل إلى QPixmap وعرضه
            pixmap = QPixmap.fromImage(qt_image)
            scaled_pixmap = pixmap.scaled(self.camera_frame.size(), Qt.KeepAspectRatio, Qt.SmoothTransformation)
            self.camera_frame.setPixmap(scaled_pixmap)
            
        except Exception as e:
            print(f"خطأ في تحديث إطار الكاميرا: {e}")
    
    def on_qr_detected(self, qr_data):
        """معالجة QR Code المكتشف"""
        try:
            # تجنب المسح المتكرر
            current_time = datetime.now()
            if self.last_scan_time and (current_time - self.last_scan_time).seconds < self.scan_cooldown:
                return
            
            self.last_scan_time = current_time
            
            # التحقق من صحة QR Code
            verification_result = self.qr_generator.verify_qr_code(qr_data)
            
            timestamp = current_time.strftime('%H:%M:%S')
            
            if verification_result['valid']:
                # QR Code صحيح
                student_name = verification_result['student_name']
                student_code = verification_result['student_code']
                
                self.results_text.append(f"[{timestamp}] ✅ تم مسح QR Code بنجاح!")
                self.results_text.append(f"    الطالب: {student_name}")
                self.results_text.append(f"    الكود: {student_code}")
                self.results_text.append("    " + "="*40)
                
                # إرسال إشارة النجاح
                self.qr_scanned.emit(verification_result)
                
            else:
                # QR Code غير صحيح
                error_msg = verification_result.get('error', 'خطأ غير معروف')
                self.results_text.append(f"[{timestamp}] ❌ QR Code غير صحيح: {error_msg}")
                self.results_text.append("    " + "="*40)
            
            # التمرير للأسفل
            self.results_text.verticalScrollBar().setValue(
                self.results_text.verticalScrollBar().maximum()
            )
            
        except Exception as e:
            self.results_text.append(f"[{datetime.now().strftime('%H:%M:%S')}] ❌ خطأ في معالجة QR Code: {str(e)}")
    
    def on_error(self, error_message):
        """معالجة الأخطاء"""
        self.results_text.append(f"[{datetime.now().strftime('%H:%M:%S')}] ❌ خطأ: {error_message}")
        QMessageBox.critical(self, "خطأ في المسح", error_message)
        
        # إعادة تعيين الأزرار
        self.start_button.setEnabled(True)
        self.stop_button.setEnabled(False)
        self.status_label.setText("❌ خطأ في المسح")
        self.status_label.setStyleSheet("""
            QLabel {
                background-color: #e74c3c;
                color: white;
                padding: 8px;
                border-radius: 4px;
                font-weight: bold;
            }
        """)
    
    def closeEvent(self, event):
        """إغلاق النافذة"""
        self.stop_scanning()
        event.accept()
