# -*- coding: utf-8 -*-
"""
نظام الرسائل التلقائية
Automatic Messaging System
"""

import webbrowser
import urllib.parse
from datetime import datetime
from PyQt5.QtWidgets import QMessageBox

class MessagingSystem:
    """نظام إرسال الرسائل التلقائية"""
    
    def __init__(self):
        self.whatsapp_enabled = True
        self.telegram_enabled = True
    
    def send_absence_notification(self, student_name, parent_phone, date=None):
        """إرسال إشعار غياب"""
        if not date:
            date = datetime.now().strftime("%Y-%m-%d")
        
        message = f"""
🔔 إشعار غياب - نظام إدارة الطلاب

عزيزي ولي الأمر،

نود إعلامكم بأن الطالب/ة: {student_name}
كان غائباً في تاريخ: {date}

يرجى التواصل مع إدارة المدرسة للاستفسار.

مع تحيات إدارة المدرسة
مستر أحمد عادل - معلم الجغرافيا والتاريخ
        """.strip()
        
        return self._send_message(parent_phone, message, "إشعار غياب")
    
    def send_excellent_grade_notification(self, student_name, parent_phone, subject, grade, max_grade):
        """إرسال إشعار درجة ممتازة"""
        percentage = (grade / max_grade) * 100
        
        message = f"""
🎉 تهنئة - نظام إدارة الطلاب

عزيزي ولي الأمر،

نتشرف بإعلامكم أن الطالب/ة: {student_name}
حصل على درجة ممتازة في مادة: {subject}

الدرجة: {grade} من {max_grade} ({percentage:.1f}%)

نبارك لكم هذا التفوق ونتمنى المزيد من النجاح.

مع تحيات إدارة المدرسة
مستر أحمد عادل - معلم الجغرافيا والتاريخ
        """.strip()
        
        return self._send_message(parent_phone, message, "تهنئة بالتفوق")
    
    def send_low_grade_notification(self, student_name, parent_phone, subject, grade, max_grade):
        """إرسال إشعار درجة منخفضة"""
        percentage = (grade / max_grade) * 100
        
        message = f"""
⚠️ تنبيه أكاديمي - نظام إدارة الطلاب

عزيزي ولي الأمر،

نود إعلامكم أن الطالب/ة: {student_name}
حصل على درجة تحتاج لمتابعة في مادة: {subject}

الدرجة: {grade} من {max_grade} ({percentage:.1f}%)

يرجى المتابعة مع الطالب ومراجعة المعلم للحصول على الدعم اللازم.

مع تحيات إدارة المدرسة
مستر أحمد عادل - معلم الجغرافيا والتاريخ
        """.strip()
        
        return self._send_message(parent_phone, message, "تنبيه أكاديمي")
    
    def _send_message(self, phone, message, title):
        """إرسال الرسالة عبر الطرق المتاحة"""
        if not phone:
            return False, "رقم الهاتف غير متوفر"
        
        # تنظيف رقم الهاتف
        clean_phone = self._clean_phone_number(phone)
        
        try:
            # إرسال عبر WhatsApp
            if self.whatsapp_enabled:
                whatsapp_sent = self._send_whatsapp(clean_phone, message)
                if whatsapp_sent:
                    return True, f"تم إرسال {title} عبر WhatsApp"
            
            # إرسال عبر Telegram (إذا فشل WhatsApp)
            if self.telegram_enabled:
                telegram_sent = self._send_telegram(clean_phone, message)
                if telegram_sent:
                    return True, f"تم إرسال {title} عبر Telegram"
            
            return False, "فشل في إرسال الرسالة"
            
        except Exception as e:
            return False, f"خطأ في إرسال الرسالة: {str(e)}"
    
    def _clean_phone_number(self, phone):
        """تنظيف رقم الهاتف"""
        # إزالة المسافات والرموز
        clean = ''.join(filter(str.isdigit, phone))
        
        # إضافة كود الدولة إذا لم يكن موجوداً
        if clean.startswith('01'):  # رقم مصري
            clean = '2' + clean
        elif not clean.startswith('2'):
            clean = '2' + clean
        
        return clean
    
    def _send_whatsapp(self, phone, message):
        """إرسال رسالة عبر WhatsApp"""
        try:
            # ترميز الرسالة للـ URL
            encoded_message = urllib.parse.quote(message)
            
            # إنشاء رابط WhatsApp
            whatsapp_url = f"https://wa.me/{phone}?text={encoded_message}"
            
            # فتح الرابط في المتصفح
            webbrowser.open(whatsapp_url)
            
            return True
            
        except Exception as e:
            print(f"خطأ في إرسال WhatsApp: {e}")
            return False
    
    def _send_telegram(self, phone, message):
        """إرسال رسالة عبر Telegram"""
        try:
            # ترميز الرسالة للـ URL
            encoded_message = urllib.parse.quote(message)
            
            # إنشاء رابط Telegram (يحتاج username بدلاً من رقم الهاتف)
            # هذا مثال - يحتاج تخصيص حسب الحاجة
            telegram_url = f"https://t.me/share/url?url=&text={encoded_message}"
            
            # فتح الرابط في المتصفح
            webbrowser.open(telegram_url)
            
            return True
            
        except Exception as e:
            print(f"خطأ في إرسال Telegram: {e}")
            return False
    
    def show_messaging_options(self, parent, phone, message, title):
        """إظهار خيارات الإرسال للمستخدم"""
        from PyQt5.QtWidgets import QDialog, QVBoxLayout, QHBoxLayout, QPushButton, QLabel, QTextEdit
        
        dialog = QDialog(parent)
        dialog.setWindowTitle("إرسال رسالة")
        dialog.setModal(True)
        dialog.resize(500, 400)
        
        layout = QVBoxLayout()
        
        # عنوان
        title_label = QLabel(f"إرسال {title}")
        title_label.setStyleSheet("font-size: 16px; font-weight: bold; color: #2c3e50; margin: 10px;")
        layout.addWidget(title_label)
        
        # رقم الهاتف
        phone_label = QLabel(f"رقم الهاتف: {phone}")
        phone_label.setStyleSheet("font-size: 14px; color: #34495e; margin: 5px;")
        layout.addWidget(phone_label)
        
        # نص الرسالة
        message_label = QLabel("نص الرسالة:")
        message_label.setStyleSheet("font-size: 14px; font-weight: bold; margin: 5px;")
        layout.addWidget(message_label)
        
        message_text = QTextEdit()
        message_text.setPlainText(message)
        message_text.setReadOnly(True)
        message_text.setMaximumHeight(200)
        layout.addWidget(message_text)
        
        # أزرار الإرسال
        buttons_layout = QHBoxLayout()
        
        whatsapp_btn = QPushButton("📱 إرسال عبر WhatsApp")
        whatsapp_btn.setStyleSheet("""
            QPushButton {
                background-color: #25D366;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #128C7E;
            }
        """)
        whatsapp_btn.clicked.connect(lambda: self._send_whatsapp(self._clean_phone_number(phone), message))
        
        telegram_btn = QPushButton("✈️ إرسال عبر Telegram")
        telegram_btn.setStyleSheet("""
            QPushButton {
                background-color: #0088cc;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #006699;
            }
        """)
        telegram_btn.clicked.connect(lambda: self._send_telegram(self._clean_phone_number(phone), message))
        
        close_btn = QPushButton("إغلاق")
        close_btn.setStyleSheet("""
            QPushButton {
                background-color: #95a5a6;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #7f8c8d;
            }
        """)
        close_btn.clicked.connect(dialog.close)
        
        buttons_layout.addWidget(whatsapp_btn)
        buttons_layout.addWidget(telegram_btn)
        buttons_layout.addWidget(close_btn)
        
        layout.addLayout(buttons_layout)
        dialog.setLayout(layout)
        
        # تطبيق الأنماط
        dialog.setStyleSheet("""
            QDialog {
                background-color: white;
                border-radius: 10px;
            }
            QTextEdit {
                border: 2px solid #bdc3c7;
                border-radius: 5px;
                padding: 10px;
                font-size: 12px;
                background-color: #f8f9fa;
            }
        """)
        
        dialog.exec_()
        return True

# إنشاء مثيل عام للنظام
messaging_system = MessagingSystem()
