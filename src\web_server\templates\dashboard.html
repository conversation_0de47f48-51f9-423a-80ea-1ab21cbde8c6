{% extends "base.html" %}

{% block title %}لوحة التحكم - نظام إدارة الطلاب{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="card mb-4">
            <div class="card-body text-center">
                <h2 class="card-title">
                    <i class="fas fa-home me-2"></i>
                    مرحباً {{ session.full_name }}
                </h2>
                <p class="card-text">أهلاً وسهلاً في نظام إدارة الطلاب</p>
                <p class="text-muted">معلم الجغرافيا والتاريخ</p>
            </div>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row">
    <div class="col-md-3 col-sm-6 mb-3">
        <div class="stats-card" style="background: linear-gradient(45deg, #3498db, #2980b9);">
            <div class="stats-number" id="total-students">{{ stats.total_students }}</div>
            <div class="stats-label">
                <i class="fas fa-users me-1"></i>إجمالي الطلاب
            </div>
        </div>
    </div>
    
    <div class="col-md-3 col-sm-6 mb-3">
        <div class="stats-card" style="background: linear-gradient(45deg, #27ae60, #2ecc71);">
            <div class="stats-number" id="present-today">{{ stats.present_today }}</div>
            <div class="stats-label">
                <i class="fas fa-check me-1"></i>الحاضرين اليوم
            </div>
        </div>
    </div>
    
    <div class="col-md-3 col-sm-6 mb-3">
        <div class="stats-card" style="background: linear-gradient(45deg, #e74c3c, #c0392b);">
            <div class="stats-number" id="absent-today">{{ stats.absent_today }}</div>
            <div class="stats-label">
                <i class="fas fa-times me-1"></i>الغائبين اليوم
            </div>
        </div>
    </div>
    
    <div class="col-md-3 col-sm-6 mb-3">
        <div class="stats-card" style="background: linear-gradient(45deg, #f39c12, #e67e22);">
            <div class="stats-number" id="late-today">{{ stats.late_today }}</div>
            <div class="stats-label">
                <i class="fas fa-clock me-1"></i>المتأخرين اليوم
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-bolt me-2"></i>الإجراءات السريعة
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4 mb-3">
                        <a href="{{ url_for('students') }}" class="btn btn-primary w-100 py-3">
                            <i class="fas fa-users fa-2x d-block mb-2"></i>
                            <strong>إدارة الطلاب</strong>
                            <small class="d-block">إضافة وتعديل وحذف الطلاب</small>
                        </a>
                    </div>
                    
                    <div class="col-md-4 mb-3">
                        <a href="{{ url_for('attendance') }}" class="btn btn-success w-100 py-3">
                            <i class="fas fa-calendar-check fa-2x d-block mb-2"></i>
                            <strong>تسجيل الحضور</strong>
                            <small class="d-block">تسجيل حضور وغياب الطلاب</small>
                        </a>
                    </div>
                    
                    <div class="col-md-4 mb-3">
                        <a href="{{ url_for('grades') }}" class="btn btn-warning w-100 py-3">
                            <i class="fas fa-chart-line fa-2x d-block mb-2"></i>
                            <strong>إدارة الدرجات</strong>
                            <small class="d-block">إدخال وتعديل درجات الطلاب</small>
                        </a>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <a href="{{ url_for('reports') }}" class="btn btn-info w-100 py-3">
                            <i class="fas fa-file-alt fa-2x d-block mb-2"></i>
                            <strong>التقارير</strong>
                            <small class="d-block">إنشاء وطباعة التقارير</small>
                        </a>
                    </div>
                    
                    <div class="col-md-6 mb-3">
                        <button class="btn btn-secondary w-100 py-3" onclick="showSystemInfo()">
                            <i class="fas fa-cog fa-2x d-block mb-2"></i>
                            <strong>معلومات النظام</strong>
                            <small class="d-block">حالة الخادم والاتصالات</small>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Activity -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-history me-2"></i>النشاط الأخير
                </h5>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    مرحباً بك في النسخة الويب من نظام إدارة الطلاب. يمكنك الآن الوصول للنظام من أي جهاز متصل بالشبكة!
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <h6><i class="fas fa-wifi me-2"></i>معلومات الاتصال</h6>
                        <ul class="list-unstyled">
                            <li><strong>حالة الخادم:</strong> <span class="text-success">متصل</span></li>
                            <li><strong>المستخدمين المتصلين:</strong> <span id="connected-users">1</span></li>
                            <li><strong>آخر تحديث:</strong> <span id="last-update">الآن</span></li>
                        </ul>
                    </div>
                    
                    <div class="col-md-6">
                        <h6><i class="fas fa-chart-bar me-2"></i>إحصائيات سريعة</h6>
                        <ul class="list-unstyled">
                            <li><strong>نسبة الحضور اليوم:</strong> 
                                <span id="attendance-percentage">
                                    {% if stats.total_students > 0 %}
                                        {{ "%.1f"|format((stats.present_today / stats.total_students) * 100) }}%
                                    {% else %}
                                        0%
                                    {% endif %}
                                </span>
                            </li>
                            <li><strong>آخر طالب سجل حضور:</strong> <span id="last-attendance">-</span></li>
                            <li><strong>آخر درجة أضيفت:</strong> <span id="last-grade">-</span></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- System Info Modal -->
<div class="modal fade" id="systemInfoModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">معلومات النظام</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <h6>معلومات الخادم</h6>
                <ul class="list-unstyled">
                    <li><strong>نوع الخادم:</strong> Flask + SocketIO</li>
                    <li><strong>قاعدة البيانات:</strong> SQLite (محلية)</li>
                    <li><strong>الوصول عن بُعد:</strong> مفعل</li>
                    <li><strong>التحديثات المباشرة:</strong> مفعل</li>
                </ul>
                
                <h6>المميزات الجديدة</h6>
                <ul>
                    <li>✅ الوصول عن بُعد من أي جهاز</li>
                    <li>✅ تحديثات مباشرة في الوقت الفعلي</li>
                    <li>✅ واجهة ويب متجاوبة</li>
                    <li>✅ دعم متعدد المستخدمين</li>
                </ul>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    function showSystemInfo() {
        const modal = new bootstrap.Modal(document.getElementById('systemInfoModal'));
        modal.show();
    }
    
    // تحديث الوقت
    function updateTime() {
        const now = new Date();
        document.getElementById('last-update').textContent = now.toLocaleTimeString('ar-EG');
    }
    
    setInterval(updateTime, 1000);
    
    // استقبال التحديثات المباشرة
    socket.on('attendance_marked', function(data) {
        document.getElementById('last-attendance').textContent = `الطالب ${data.student_code} - ${data.status}`;
        // طلب تحديث الإحصائيات
        socket.emit('request_stats');
    });
    
    socket.on('grade_added', function(data) {
        document.getElementById('last-grade').textContent = `${data.subject} - ${data.score} درجة`;
    });
    
    socket.on('student_added', function(data) {
        document.getElementById('last-update').textContent = `تم إضافة الطالب: ${data.name}`;
        // طلب تحديث الإحصائيات
        socket.emit('request_stats');
    });
</script>
{% endblock %}
