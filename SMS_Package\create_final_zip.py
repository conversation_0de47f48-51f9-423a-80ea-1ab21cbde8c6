#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إنشاء ملف ZIP نهائي للمشروع
Create Final ZIP for Project
"""

import os
import zipfile
import shutil
from pathlib import Path
from datetime import datetime

def create_project_zip():
    """إنشاء ملف ZIP للمشروع"""
    
    print("🗜️ إنشاء ملف ZIP لنظام إدارة الطلاب")
    print("=" * 50)
    
    # اسم الملف المضغوط
    timestamp = datetime.now().strftime("%Y%m%d_%H%M")
    zip_filename = f"StudentManagementSystem_{timestamp}.zip"
    
    # مسار المشروع
    project_root = Path(__file__).parent
    
    print(f"📦 اسم الملف: {zip_filename}")
    print(f"📁 مسار المشروع: {project_root}")
    print()
    
    # الملفات المطلوب ضغطها
    files_to_include = [
        'main.py',
        'run.py', 
        'launch.py',
        'config.py',
        'requirements.txt',
        'run_app.bat',
        'start.bat',
        'build.bat',
        'fix_imports.py',
        'test_app.py',
        'build_exe.py',
        'compress_project.py',
        'README.md',
        'README_FINAL.md',
        'USER_GUIDE.md',
        'INSTALLATION.md',
        'PROJECT_SUMMARY.md'
    ]
    
    # المجلدات المطلوب ضغطها
    dirs_to_include = ['src', 'data', 'assets']
    
    # المجلدات الفارغة
    empty_dirs = ['logs', 'backups', 'exports', 'temp']
    
    try:
        with zipfile.ZipFile(zip_filename, 'w', zipfile.ZIP_DEFLATED) as zipf:
            
            print("📄 إضافة الملفات...")
            # إضافة الملفات
            for file_name in files_to_include:
                file_path = project_root / file_name
                if file_path.exists():
                    zipf.write(file_path, file_name)
                    print(f"  ✅ {file_name}")
                else:
                    print(f"  ⚠️ {file_name} (غير موجود)")
            
            print("\n📁 إضافة المجلدات...")
            # إضافة المجلدات
            for dir_name in dirs_to_include:
                dir_path = project_root / dir_name
                if dir_path.exists():
                    for root, dirs, files in os.walk(dir_path):
                        for file in files:
                            file_path = Path(root) / file
                            arc_path = file_path.relative_to(project_root)
                            zipf.write(file_path, arc_path)
                    print(f"  ✅ {dir_name}/")
                else:
                    print(f"  ⚠️ {dir_name}/ (غير موجود)")
            
            print("\n📂 إنشاء المجلدات الفارغة...")
            # إنشاء المجلدات الفارغة
            for dir_name in empty_dirs:
                zipf.writestr(f"{dir_name}/.gitkeep", "")
                print(f"  ✅ {dir_name}/")
            
            # إضافة ملف معلومات المشروع
            project_info = f"""# نظام إدارة الطلاب - Student Management System
## لمستر أحمد عادل - For Mr. Ahmed Adel

**تاريخ الإنشاء - Created:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
**الإصدار - Version:** 1.0.0

## طريقة التشغيل - How to Run:

### الطريقة الأولى - Method 1 (Recommended):
1. استخرج جميع الملفات - Extract all files
2. انقر مرتين على run_app.bat - Double-click run_app.bat
3. اختر الخيار 1 - Choose option 1

### الطريقة الثانية - Method 2:
1. افتح سطر الأوامر - Open command prompt
2. شغل: python main.py - Run: python main.py

## بيانات تسجيل الدخول - Login Credentials:
- **اسم المستخدم - Username:** admin
- **كلمة المرور - Password:** admin123

## الملفات المهمة - Important Files:
- `run_app.bat` - ملف التشغيل الرئيسي - Main launcher
- `USER_GUIDE.md` - دليل المستخدم الكامل - Complete user guide
- `INSTALLATION.md` - دليل التثبيت - Installation guide
- `PROJECT_SUMMARY.md` - ملخص المشروع - Project summary

## المميزات - Features:
✅ إدارة الطلاب - Student Management
✅ تسجيل الحضور - Attendance Tracking  
✅ إدارة الدرجات - Grade Management
✅ التقارير - Reports
✅ واجهة عربية - Arabic Interface
✅ نسخ احتياطي - Data Backup

## في حالة وجود مشاكل - Troubleshooting:
1. شغل run_app.bat واختر الخيار 4 - Run run_app.bat and choose option 4
2. أو شغل: python fix_imports.py - Or run: python fix_imports.py
3. راجع مجلد logs/ للأخطاء - Check logs/ folder for errors

## المطور - Developer:
تم التطوير خصيصاً لمستر أحمد عادل
Created specifically for Mr. Ahmed Adel
معلم الجغرافيا والتاريخ - Geography and History Teacher

---
🎓 استمتع باستخدام نظام إدارة الطلاب!
🎓 Enjoy using the Student Management System!
"""
            
            zipf.writestr("PROJECT_INFO.txt", project_info)
            print("  ✅ PROJECT_INFO.txt")
        
        # معلومات الملف المضغوط
        if os.path.exists(zip_filename):
            zip_size = os.path.getsize(zip_filename)
            zip_size_mb = zip_size / (1024 * 1024)
            
            print("\n" + "=" * 50)
            print("🎉 تم إنشاء الملف المضغوط بنجاح!")
            print("=" * 50)
            print(f"📦 اسم الملف: {zip_filename}")
            print(f"📏 حجم الملف: {zip_size_mb:.2f} ميجابايت")
            print(f"📍 المسار: {project_root / zip_filename}")
            print()
            print("💡 تعليمات:")
            print("1. احفظ الملف في مكان آمن")
            print("2. يمكنك نسخه على أي جهاز Windows")
            print("3. استخرج الملفات وشغل run_app.bat")
            print("4. استخدم admin/admin123 لتسجيل الدخول")
            print()
            print("🎓 نظام إدارة الطلاب جاهز للتوزيع!")
            
            return zip_filename
        else:
            print("❌ فشل في إنشاء الملف المضغوط")
            return None
            
    except Exception as e:
        print(f"❌ خطأ في إنشاء الملف المضغوط: {e}")
        return None

def main():
    """الدالة الرئيسية"""
    zip_file = create_project_zip()
    
    if zip_file:
        print(f"\n✅ تم إنشاء الملف بنجاح: {zip_file}")
        
        # محاولة فتح مجلد الملف
        try:
            import subprocess
            subprocess.run(['explorer', '/select,', zip_file], check=False)
        except:
            pass
    else:
        print("\n❌ فشل في إنشاء الملف المضغوط")
    
    input("\nاضغط Enter للخروج...")

if __name__ == "__main__":
    main()
