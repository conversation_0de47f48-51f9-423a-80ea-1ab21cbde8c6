# -*- coding: utf-8 -*-
"""
نموذج إدارة الحصص والتوقيتات
Class Schedule Management Model
"""

from datetime import datetime, time, timedelta
from typing import Dict, List, Optional
import json

class ClassSchedule:
    """نموذج إدارة الحصص والتوقيتات"""
    
    def __init__(self, db_manager):
        self.db = db_manager
        self.create_tables()
    
    def create_tables(self):
        """إنشاء جداول الحصص"""
        
        # جدول الحصص
        classes_table = """
        CREATE TABLE IF NOT EXISTS class_sessions (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            class_name TEXT NOT NULL,
            subject TEXT NOT NULL,
            start_time TIME NOT NULL,
            end_time TIME NOT NULL,
            days_of_week TEXT NOT NULL,  -- JSON array of days
            group_name TEXT,
            attendance_window_before INTEGER DEFAULT 15,  -- minutes before class
            attendance_window_after INTEGER DEFAULT 30,   -- minutes after start
            late_threshold INTEGER DEFAULT 10,            -- minutes for late marking
            is_active BOOLEAN DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        """
        
        # جدول حضور QR Code
        qr_attendance_table = """
        CREATE TABLE IF NOT EXISTS qr_attendance (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            student_id INTEGER NOT NULL,
            student_code TEXT NOT NULL,
            student_name TEXT NOT NULL,
            class_session_id INTEGER NOT NULL,
            scan_time TIMESTAMP NOT NULL,
            attendance_status TEXT NOT NULL CHECK (attendance_status IN ('حاضر', 'متأخر', 'غائب', 'خارج_التوقيت')),
            scan_location TEXT,  -- للمستقبل
            qr_data TEXT,        -- البيانات المشفرة
            notes TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (student_id) REFERENCES students (id),
            FOREIGN KEY (class_session_id) REFERENCES class_sessions (id)
        )
        """
        
        # جدول تنبيهات المسح خارج التوقيت
        scan_alerts_table = """
        CREATE TABLE IF NOT EXISTS scan_alerts (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            student_id INTEGER NOT NULL,
            student_code TEXT NOT NULL,
            student_name TEXT NOT NULL,
            scan_time TIMESTAMP NOT NULL,
            alert_type TEXT NOT NULL CHECK (alert_type IN ('قبل_الحصة', 'بعد_الحصة', 'لا_توجد_حصة')),
            class_session_id INTEGER,
            time_difference INTEGER,  -- minutes difference
            handled BOOLEAN DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (student_id) REFERENCES students (id),
            FOREIGN KEY (class_session_id) REFERENCES class_sessions (id)
        )
        """
        
        tables = [classes_table, qr_attendance_table, scan_alerts_table]
        
        for table in tables:
            self.db.execute_query(table)
    
    def add_class_session(self, class_data: Dict) -> Optional[int]:
        """إضافة حصة جديدة"""
        try:
            query = """
            INSERT INTO class_sessions 
            (class_name, subject, start_time, end_time, days_of_week, group_name,
             attendance_window_before, attendance_window_after, late_threshold)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            """
            
            # تحويل أيام الأسبوع إلى JSON
            days_json = json.dumps(class_data['days_of_week'], ensure_ascii=False)
            
            params = (
                class_data['class_name'],
                class_data['subject'],
                class_data['start_time'],
                class_data['end_time'],
                days_json,
                class_data.get('group_name', ''),
                class_data.get('attendance_window_before', 15),
                class_data.get('attendance_window_after', 30),
                class_data.get('late_threshold', 10)
            )
            
            result = self.db.execute_query(query, params)
            return result.lastrowid if result else None
            
        except Exception as e:
            print(f"خطأ في إضافة الحصة: {e}")
            return None
    
    def get_current_class(self, current_time: datetime = None) -> Optional[Dict]:
        """الحصول على الحصة الحالية"""
        if current_time is None:
            current_time = datetime.now()
        
        try:
            # اليوم الحالي
            current_day = current_time.strftime('%A')  # Monday, Tuesday, etc.
            current_time_str = current_time.strftime('%H:%M:%S')
            
            query = """
            SELECT * FROM class_sessions 
            WHERE is_active = 1
            """
            
            classes = self.db.execute_query_with_fetch(query, fetch=True)
            
            for class_row in classes:
                # تحويل أيام الأسبوع من JSON
                days_of_week = json.loads(class_row[5])
                
                # التحقق من اليوم
                if current_day not in days_of_week:
                    continue
                
                # التحقق من التوقيت
                start_time = datetime.strptime(class_row[3], '%H:%M:%S').time()
                end_time = datetime.strptime(class_row[4], '%H:%M:%S').time()
                current_time_obj = datetime.strptime(current_time_str, '%H:%M:%S').time()
                
                # حساب نافذة الحضور
                attendance_before = class_row[7]  # minutes before
                attendance_after = class_row[8]   # minutes after
                
                # وقت بداية نافذة الحضور
                start_datetime = datetime.combine(current_time.date(), start_time)
                window_start = start_datetime - timedelta(minutes=attendance_before)
                window_end = start_datetime + timedelta(minutes=attendance_after)
                
                # التحقق من وجود الوقت الحالي في نافذة الحضور
                if window_start.time() <= current_time_obj <= window_end.time():
                    return {
                        'id': class_row[0],
                        'class_name': class_row[1],
                        'subject': class_row[2],
                        'start_time': class_row[3],
                        'end_time': class_row[4],
                        'days_of_week': days_of_week,
                        'group_name': class_row[6],
                        'attendance_window_before': class_row[7],
                        'attendance_window_after': class_row[8],
                        'late_threshold': class_row[9],
                        'window_start': window_start.strftime('%H:%M:%S'),
                        'window_end': window_end.strftime('%H:%M:%S')
                    }
            
            return None
            
        except Exception as e:
            print(f"خطأ في الحصول على الحصة الحالية: {e}")
            return None
    
    def process_qr_scan(self, student_data: Dict, scan_time: datetime = None) -> Dict:
        """معالجة مسح QR Code وتحديد حالة الحضور"""
        if scan_time is None:
            scan_time = datetime.now()
        
        try:
            # البحث عن الحصة الحالية
            current_class = self.get_current_class(scan_time)
            
            if not current_class:
                # لا توجد حصة حالية - إنشاء تنبيه
                self.create_scan_alert(student_data, scan_time, 'لا_توجد_حصة')
                return {
                    'success': False,
                    'status': 'خارج_التوقيت',
                    'message': 'لا توجد حصة في هذا التوقيت',
                    'alert_created': True
                }
            
            # تحديد حالة الحضور
            attendance_status = self.determine_attendance_status(current_class, scan_time)
            
            # تسجيل الحضور
            attendance_id = self.record_qr_attendance(
                student_data, current_class, scan_time, attendance_status
            )
            
            if attendance_id:
                return {
                    'success': True,
                    'status': attendance_status,
                    'class_info': current_class,
                    'attendance_id': attendance_id,
                    'message': f'تم تسجيل {attendance_status} بنجاح'
                }
            else:
                return {
                    'success': False,
                    'status': 'خطأ',
                    'message': 'فشل في تسجيل الحضور'
                }
                
        except Exception as e:
            print(f"خطأ في معالجة مسح QR Code: {e}")
            return {
                'success': False,
                'status': 'خطأ',
                'message': f'خطأ في المعالجة: {str(e)}'
            }
    
    def determine_attendance_status(self, class_info: Dict, scan_time: datetime) -> str:
        """تحديد حالة الحضور بناءً على التوقيت"""
        try:
            # وقت بداية الحصة
            start_time = datetime.strptime(class_info['start_time'], '%H:%M:%S').time()
            start_datetime = datetime.combine(scan_time.date(), start_time)
            
            # حد التأخير
            late_threshold = class_info['late_threshold']
            late_deadline = start_datetime + timedelta(minutes=late_threshold)
            
            if scan_time <= start_datetime:
                return 'حاضر'
            elif scan_time <= late_deadline:
                return 'متأخر'
            else:
                return 'حاضر'  # حاضر متأخر جداً
                
        except Exception as e:
            print(f"خطأ في تحديد حالة الحضور: {e}")
            return 'حاضر'
    
    def record_qr_attendance(self, student_data: Dict, class_info: Dict, 
                           scan_time: datetime, status: str) -> Optional[int]:
        """تسجيل حضور QR Code"""
        try:
            query = """
            INSERT INTO qr_attendance 
            (student_id, student_code, student_name, class_session_id, 
             scan_time, attendance_status, qr_data)
            VALUES (?, ?, ?, ?, ?, ?, ?)
            """
            
            params = (
                student_data['student_id'],
                student_data['student_code'],
                student_data['student_name'],
                class_info['id'],
                scan_time.isoformat(),
                status,
                json.dumps(student_data, ensure_ascii=False)
            )
            
            result = self.db.execute_query(query, params)
            return result.lastrowid if result else None
            
        except Exception as e:
            print(f"خطأ في تسجيل حضور QR Code: {e}")
            return None
    
    def create_scan_alert(self, student_data: Dict, scan_time: datetime, 
                         alert_type: str, class_id: int = None) -> Optional[int]:
        """إنشاء تنبيه مسح خارج التوقيت"""
        try:
            query = """
            INSERT INTO scan_alerts 
            (student_id, student_code, student_name, scan_time, 
             alert_type, class_session_id, time_difference)
            VALUES (?, ?, ?, ?, ?, ?, ?)
            """
            
            # حساب فرق التوقيت إذا كان هناك حصة
            time_diff = 0
            if class_id:
                # حساب الفرق بالدقائق
                pass  # يمكن تطويره لاحقاً
            
            params = (
                student_data['student_id'],
                student_data['student_code'],
                student_data['student_name'],
                scan_time.isoformat(),
                alert_type,
                class_id,
                time_diff
            )
            
            result = self.db.execute_query(query, params)
            return result.lastrowid if result else None
            
        except Exception as e:
            print(f"خطأ في إنشاء تنبيه المسح: {e}")
            return None
    
    def get_class_attendance_report(self, class_id: int, date: str) -> List[Dict]:
        """الحصول على تقرير حضور الحصة"""
        try:
            query = """
            SELECT qa.*, cs.class_name, cs.subject
            FROM qr_attendance qa
            JOIN class_sessions cs ON qa.class_session_id = cs.id
            WHERE qa.class_session_id = ? 
            AND DATE(qa.scan_time) = ?
            ORDER BY qa.scan_time
            """
            
            result = self.db.execute_query_with_fetch(query, (class_id, date), fetch=True)
            
            attendance_list = []
            for row in result:
                attendance_list.append({
                    'id': row[0],
                    'student_id': row[1],
                    'student_code': row[2],
                    'student_name': row[3],
                    'scan_time': row[5],
                    'status': row[6],
                    'class_name': row[10],
                    'subject': row[11]
                })
            
            return attendance_list
            
        except Exception as e:
            print(f"خطأ في الحصول على تقرير الحضور: {e}")
            return []
    
    def get_unhandled_alerts(self) -> List[Dict]:
        """الحصول على التنبيهات غير المعالجة"""
        try:
            query = """
            SELECT * FROM scan_alerts 
            WHERE handled = 0 
            ORDER BY scan_time DESC
            """
            
            result = self.db.execute_query_with_fetch(query, fetch=True)
            
            alerts = []
            for row in result:
                alerts.append({
                    'id': row[0],
                    'student_id': row[1],
                    'student_code': row[2],
                    'student_name': row[3],
                    'scan_time': row[4],
                    'alert_type': row[5],
                    'class_session_id': row[6],
                    'time_difference': row[7]
                })
            
            return alerts
            
        except Exception as e:
            print(f"خطأ في الحصول على التنبيهات: {e}")
            return []
    
    def mark_alert_handled(self, alert_id: int) -> bool:
        """تمييز التنبيه كمعالج"""
        try:
            query = "UPDATE scan_alerts SET handled = 1 WHERE id = ?"
            result = self.db.execute_query(query, (alert_id,))
            return result is not None
            
        except Exception as e:
            print(f"خطأ في تمييز التنبيه كمعالج: {e}")
            return False
