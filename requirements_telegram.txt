# متطلبات نظام إدارة الطلاب مع الربط بتليجرام
# Student Management System with Telegram Integration Requirements

# واجهة المستخدم الأساسية
PyQt5>=5.15.0

# مكتبات QR Code
qrcode>=7.3.1
Pillow>=8.3.0

# مكتبة تليجرام
python-telegram-bot>=20.0

# مكتبات قواعد البيانات
sqlite3

# مكتبات التشفير والأمان (للفيديوهات المحمية)
cryptography>=3.4.8

# مكتبات معالجة النظام (للحماية المتقدمة)
psutil>=5.8.0

# مكتبات Windows (للحماية من التسجيل)
pywin32>=227

# مكتبات إضافية للتطوير
requests>=2.25.1
urllib3>=1.26.0

# مكتبات معالجة التاريخ والوقت
python-dateutil>=2.8.0

# مكتبات JSON المحسنة
jsonschema>=3.2.0

# مكتبات التحقق من صحة البيانات
validators>=0.18.0

# مكتبات الشبكة
aiohttp>=3.7.0
asyncio

# مكتبات معالجة الملفات
pathlib2>=2.3.0

# مكتبات التسجيل والمراقبة
logging

# مكتبات الوقت والتوقيت
schedule>=1.1.0

# ملاحظات التثبيت:
# pip install -r requirements_telegram.txt
# 
# للحصول على توكن البوت:
# 1. ابحث عن @BotFather في تليجرام
# 2. أرسل /newbot
# 3. اتبع التعليمات
# 4. احفظ التوكن المُعطى
#
# لمعرفة معرف القناة:
# 1. أضف البوت للقناة كمشرف
# 2. أرسل رسالة في القناة
# 3. اذهب لـ https://api.telegram.org/bot[TOKEN]/getUpdates
# 4. ابحث عن chat.id
