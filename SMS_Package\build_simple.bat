@echo off
title Building Student Management System EXE

echo.
echo ========================================
echo   Building Student Management System
echo   Standalone EXE Application
echo ========================================
echo.

echo Checking Python installation...
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python is not installed
    echo.
    echo Please install Python from: https://python.org
    echo Make sure to check "Add Python to PATH" during installation
    echo.
    pause
    exit /b 1
)

echo Python found!
echo.

echo Installing PyInstaller...
pip install pyinstaller --quiet
if errorlevel 1 (
    echo Failed to install PyInstaller
    pause
    exit /b 1
)

echo PyInstaller installed!
echo.

echo Installing requirements...
if exist "requirements.txt" (
    pip install -r requirements.txt --quiet
)

echo.
echo Creating spec file...

echo # -*- mode: python ; coding: utf-8 -*- > simple.spec
echo. >> simple.spec
echo a = Analysis( >> simple.spec
echo     ['main.py'], >> simple.spec
echo     pathex=[], >> simple.spec
echo     binaries=[], >> simple.spec
echo     datas=[('src', 'src'), ('data', 'data')], >> simple.spec
echo     hiddenimports=['PyQt5.QtCore', 'PyQt5.QtGui', 'PyQt5.QtWidgets', 'sqlite3'], >> simple.spec
echo     hookspath=[], >> simple.spec
echo     runtime_hooks=[], >> simple.spec
echo     excludes=[], >> simple.spec
echo     win_no_prefer_redirects=False, >> simple.spec
echo     win_private_assemblies=False, >> simple.spec
echo     cipher=None, >> simple.spec
echo     noarchive=False, >> simple.spec
echo ) >> simple.spec
echo. >> simple.spec
echo pyz = PYZ(a.pure, a.zipped_data, cipher=None) >> simple.spec
echo. >> simple.spec
echo exe = EXE( >> simple.spec
echo     pyz, >> simple.spec
echo     a.scripts, >> simple.spec
echo     a.binaries, >> simple.spec
echo     a.zipfiles, >> simple.spec
echo     a.datas, >> simple.spec
echo     [], >> simple.spec
echo     name='StudentManagementSystem', >> simple.spec
echo     debug=False, >> simple.spec
echo     bootloader_ignore_signals=False, >> simple.spec
echo     strip=False, >> simple.spec
echo     upx=True, >> simple.spec
echo     runtime_tmpdir=None, >> simple.spec
echo     console=False, >> simple.spec
echo     disable_windowed_traceback=False, >> simple.spec
echo     target_arch=None, >> simple.spec
echo ) >> simple.spec

echo Spec file created!
echo.

echo Building EXE file...
echo This may take several minutes...
echo.

pyinstaller --clean --noconfirm simple.spec

if exist "dist\StudentManagementSystem.exe" (
    echo.
    echo ========================================
    echo           BUILD SUCCESSFUL!
    echo ========================================
    echo.
    
    for %%A in ("dist\StudentManagementSystem.exe") do set "file_size=%%~zA"
    set /a "file_size_mb=%file_size% / 1048576"
    
    echo EXE file: dist\StudentManagementSystem.exe
    echo File size: %file_size_mb% MB
    echo.
    
    echo Creating README file...
    (
    echo # Student Management System
    echo.
    echo ## How to Run:
    echo 1. Double-click StudentManagementSystem.exe
    echo 2. Login with: admin / admin123
    echo.
    echo ## Features:
    echo - Student Management
    echo - Attendance Tracking
    echo - Grade Management
    echo - Reports Generation
    echo.
    echo Created for Mr. Ahmed Adel
    echo Geography and History Teacher
    ) > "dist\README.txt"
    
    echo Creating directories...
    mkdir "dist\logs" >nul 2>&1
    mkdir "dist\backups" >nul 2>&1
    mkdir "dist\exports" >nul 2>&1
    
    echo.
    echo SUCCESS! The application is ready!
    echo.
    echo Instructions:
    echo 1. Copy the 'dist' folder to any Windows computer
    echo 2. Double-click StudentManagementSystem.exe
    echo 3. Login with admin/admin123
    echo.
    echo Opening dist folder...
    explorer "dist"
    
) else (
    echo.
    echo BUILD FAILED!
    echo Check the errors above.
)

echo.
echo Cleaning up...
if exist "build" rmdir /s /q "build" >nul 2>&1
if exist "simple.spec" del "simple.spec" >nul 2>&1

echo.
pause
