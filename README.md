# تطبيق إدارة الطلاب لمستر أحمد عادل
## Student Management System for Mr. <PERSON>el

### 📋 وصف التطبيق
تطبيق سطح مكتب لإدارة الطلاب مصمم خصيصاً لمستر أحمد عادل لإدارة طلاب مادتي الجغرافيا والتاريخ في المراحل الإعدادية والثانوية.

### ✨ المميزات الرئيسية
- 👥 إدارة شاملة للطلاب (إضافة، تعديل، حذف، بحث)
- 📅 نظام تسجيل الحضور والانصراف
- 📊 إدارة درجات مادتي الجغرافيا والتاريخ
- 📈 تقارير مفصلة قابلة للتصدير (PDF, Excel)
- 🔐 نظام مصادقة آمن للأدمين
- 🎨 واجهة عصرية باللغة العربية
- 💾 قاعدة بيانات محلية (SQLite)

### 🏗️ هيكل المشروع
```
sms/
├── main.py                 # الملف الرئيسي للتطبيق
├── requirements.txt        # متطلبات المشروع
├── README.md              # ملف التوثيق
├── src/                   # مجلد الكود المصدري
│   ├── ui/               # واجهات المستخدم
│   ├── database/         # إدارة قاعدة البيانات
│   ├── models/           # نماذج البيانات
│   ├── utils/            # أدوات مساعدة
│   └── reports/          # نظام التقارير
├── data/                 # قاعدة البيانات
└── assets/               # الصور والموارد
```

### 🚀 طريقة التشغيل
1. تثبيت المتطلبات:
```bash
pip install -r requirements.txt
```

2. تشغيل التطبيق:
```bash
python main.py
```

### 📦 إنشاء ملف EXE
```bash
pyinstaller --onefile --windowed main.py
```

### 👨‍🏫 معلومات المعلم
- **الاسم:** مستر أحمد عادل
- **المواد:** الجغرافيا والتاريخ
- **المراحل:** الإعدادية والثانوية

### 🎓 المراحل الدراسية المدعومة
**المرحلة الإعدادية:**
- أولى إعدادي
- ثانية إعدادي
- ثالثة إعدادي

**المرحلة الثانوية:**
- أولى ثانوي
- ثانية ثانوي
- ثالثة ثانوي

### 📋 بيانات الطالب
- كود فريد (ID)
- الاسم الرباعي
- النوع (ذكر/أنثى)
- المرحلة والصف الدراسي
- درجات المواد
- سجل الحضور

### 🔧 التقنيات المستخدمة
- **اللغة:** Python 3.x
- **واجهة المستخدم:** PyQt5
- **قاعدة البيانات:** SQLite
- **التقارير:** ReportLab, OpenPyXL
- **التحويل لـ EXE:** PyInstaller

### 📞 الدعم الفني
للدعم الفني أو الاستفسارات، يرجى التواصل مع المطور.

---
**تم التطوير بواسطة:** مساعد الذكي  
**التاريخ:** 2025  
**الإصدار:** 1.0
