@echo off
title Student Management System - Find and Run

echo.
echo ========================================
echo   Finding Python and Running App
echo ========================================
echo.

echo Searching for Python installations...
echo.

REM Try standard python command first
python --version >nul 2>&1
if not errorlevel 1 (
    echo Found Python in PATH!
    python --version
    echo.
    echo Starting application...
    python main.py
    goto end
)

REM Try python3 command
python3 --version >nul 2>&1
if not errorlevel 1 (
    echo Found Python3 in PATH!
    python3 --version
    echo.
    echo Starting application...
    python3 main.py
    goto end
)

REM Try py launcher
py --version >nul 2>&1
if not errorlevel 1 (
    echo Found Python launcher!
    py --version
    echo.
    echo Starting application...
    py main.py
    goto end
)

echo Python not found in PATH. Searching common locations...
echo.

REM Search common Python installation paths
set "PYTHON_PATHS=C:\Python39\python.exe;C:\Python38\python.exe;C:\Python37\python.exe;C:\Python36\python.exe;%LOCALAPPDATA%\Programs\Python\Python39\python.exe;%LOCALAPPDATA%\Programs\Python\Python38\python.exe;%LOCALAPPDATA%\Programs\Python\Python37\python.exe;%APPDATA%\Local\Programs\Python\Python39\python.exe;%PROGRAMFILES%\Python39\python.exe;%PROGRAMFILES%\Python38\python.exe"

for %%p in (%PYTHON_PATHS%) do (
    if exist "%%p" (
        echo Found Python at: %%p
        "%%p" --version 2>nul
        echo.
        echo Installing required packages...
        "%%p" -m pip install PyQt5 --quiet --user 2>nul
        echo.
        echo Starting application...
        "%%p" main.py
        goto end
    )
)

echo.
echo ========================================
echo   Python Not Found!
echo ========================================
echo.
echo Solutions:
echo.
echo 1. Install Python from Microsoft Store:
echo    - Open Microsoft Store
echo    - Search for "Python"
echo    - Install Python 3.9 or newer
echo.
echo 2. Install Python from python.org:
echo    - Go to: https://python.org/downloads
echo    - Download and install Python
echo    - Make sure to check "Add Python to PATH"
echo.
echo 3. Use Windows Subsystem for Linux (WSL):
echo    - Enable WSL in Windows Features
echo    - Install Ubuntu from Microsoft Store
echo    - Run: sudo apt install python3 python3-pip
echo.

echo Opening Python download page...
start https://python.org/downloads

echo.
echo After installing Python, run this script again.

:end
echo.
echo Login credentials:
echo Username: admin
echo Password: admin123
echo.
pause
