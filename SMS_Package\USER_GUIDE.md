# دليل المستخدم - نظام إدارة الطلاب

## 📖 مقدمة

مرحباً بك في نظام إدارة الطلاب المصمم خصيصاً لمستر أحمد عادل. هذا الدليل سيساعدك على استخدام جميع مميزات التطبيق بفعالية.

## 🚀 البدء السريع

### تشغيل التطبيق
1. **الطريقة المُوصى بها:** انقر مرتين على ملف `RUN_COMPLETE.bat`
2. **طرق بديلة:**
   - `RUN_ENHANCED.bat` - الإصدار المحسن
   - `START_PERFECT.bat` - التشغيل الأساسي
   - `python main.py` من سطر الأوامر

### تسجيل الدخول
- **اسم المستخدم:** `admin`
- **كلمة المرور:** `admin123`

⚠️ **ملاحظة مهمة:** يجب إدخال البيانات الصحيحة - لا يمكن الدخول بدون كتابة اسم المستخدم وكلمة المرور.

## 🧭 شريط التنقل الجديد

### المميزات الجديدة
- **شريط تنقل متقدم** في أعلى النافذة الرئيسية
- **انتقال سريع** بين الصفحات بدون إغلاق النوافذ
- **الزر النشط** يظهر بلون أخضر مميز
- **واجهة موحدة** ومتكاملة

### أزرار شريط التنقل
- 🏠 **الرئيسية** - العودة للوحة التحكم الرئيسية
- 👥 **إدارة الطلاب** - إضافة وتعديل بيانات الطلاب
- 📋 **تسجيل الحضور** - تسجيل حضور وغياب الطلاب
- 📊 **إدارة الدرجات** - إدخال ومتابعة درجات الطلاب
- 📈 **التقارير** - إنشاء تقارير شاملة PDF/Excel
- ⚙️ **الإعدادات** - إعدادات النظام والمعلم
- 🚪 **تسجيل الخروج** - الخروج الآمن من النظام

## 🎓 إدارة الطلاب المحسنة

### المميزات الجديدة في إدارة الطلاب
- ✅ **زر "طالب جديد"** أخضر جذاب في أعلى النافذة
- ✅ **نموذج قابل للطي** يظهر ويختفي عند الحاجة
- ✅ **حقول جديدة:** رقم الهاتف ورقم ولي الأمر
- ✅ **رسائل محسنة** بخلفية بيضاء ونص واضح
- ✅ **تحديث تلقائي** لقاعدة البيانات

### إضافة طالب جديد (الطريقة المحسنة)

1. **فتح نافذة إدارة الطلاب:**
   - من شريط التنقل، انقر على 👥 **"إدارة الطلاب"**

2. **إظهار نموذج الطالب الجديد:**
   - انقر على الزر الأخضر **"+ طالب جديد"**
   - سيظهر النموذج أسفل الزر مباشرة

3. **ملء البيانات الكاملة:**
   - **كود الطالب:** سيتم توليده تلقائياً
   - **الاسم:** أدخل الاسم الكامل للطالب
   - **المرحلة الدراسية:** اختر (إعدادي/ثانوي)
   - **الصف الدراسي:** اختر الصف المناسب
   - **رقم الهاتف:** أدخل رقم هاتف الطالب (جديد)
   - **رقم ولي الأمر:** أدخل رقم هاتف ولي الأمر (جديد)
   - **النوع:** اختر (ذكر/أنثى)
   - **درجة الجغرافيا:** أدخل الدرجة الحالية (اختياري)
   - **درجة التاريخ:** أدخل الدرجة الحالية (اختياري)

4. **حفظ البيانات:**
   - انقر على "إضافة طالب"
   - ستظهر رسالة نجاح بخلفية بيضاء
   - النموذج سيختفي تلقائياً بعد الحفظ
   - قائمة الطلاب ستتحدث فوراً

### تعديل بيانات طالب

1. **البحث عن الطالب:**
   - استخدم مربع البحث لكتابة اسم أو كود الطالب
   - أو تصفح القائمة مباشرة

2. **تحديد الطالب:**
   - انقر على الطالب في الجدول لتحديده
   - ستظهر بياناته في النموذج الجانبي

3. **تعديل البيانات:**
   - عدل المعلومات المطلوبة
   - انقر على "تحديث البيانات"

### حذف طالب

1. حدد الطالب من الجدول
2. انقر على "حذف الطالب"
3. أكد الحذف في النافذة المنبثقة

⚠️ **تحذير:** حذف الطالب سيحذف جميع بياناته بما في ذلك الحضور والدرجات

## 📅 تسجيل الحضور

### التسجيل السريع

1. **فتح نافذة الحضور:**
   - من الشاشة الرئيسية، انقر على "تسجيل الحضور"

2. **التسجيل بالكود:**
   - في تبويب "تسجيل سريع"
   - أدخل كود الطالب في المربع
   - اختر الحالة (حاضر/غائب/متأخر)
   - أضف ملاحظات إن أردت
   - اضغط Enter أو انقر "تسجيل حضور"

### إدارة الحضور اليومي

1. **عرض الحضور:**
   - انتقل لتبويب "إدارة الحضور"
   - اختر التاريخ المطلوب
   - ستظهر قائمة بجميع الطلاب وحالة حضورهم

2. **تعديل حالة الحضور:**
   - انقر مرتين على أي طالب في الجدول
   - غير الحالة في النافذة المنبثقة
   - أضف ملاحظات إن لزم الأمر

3. **العمليات المجمعة:**
   - "تسجيل الكل حاضر": لتسجيل جميع الطلاب كحاضرين
   - "تسجيل المحدد غائب": لتسجيل الطلاب المحددين كغائبين

### تقارير الحضور

1. **انتقل لتبويب "تقارير الحضور"**
2. **حدد الفترة الزمنية:**
   - تاريخ البداية والنهاية
   - المرحلة (اختياري)

3. **إنشاء التقرير:**
   - انقر على "إنشاء التقرير"
   - راجع الإحصائيات المعروضة

## 📊 إدارة الدرجات

### إضافة درجة جديدة

1. **فتح نافذة الدرجات:**
   - من الشاشة الرئيسية، انقر على "إدارة الدرجات"

2. **في تبويب "إدخال الدرجات":**
   - اختر الطالب من القائمة المنسدلة
   - اختر المادة (جغرافيا/تاريخ)
   - أدخل نوع الامتحان (مثال: امتحان شهري، واجب، مشاركة)
   - أدخل الدرجة المحصلة والدرجة الكاملة
   - اختر تاريخ الامتحان
   - أضف ملاحظات إن أردت

3. **حفظ الدرجة:**
   - انقر على "إضافة الدرجة"
   - سيتم تحديث متوسط الطالب تلقائياً

### عرض وتعديل الدرجات

1. **عرض درجات طالب معين:**
   - اختر الطالب من القائمة
   - ستظهر جميع درجاته في الجدول الجانبي

2. **تعديل درجة:**
   - انقر على الدرجة في الجدول لتحديدها
   - عدل البيانات في النموذج
   - انقر على "تحديث الدرجة"

3. **حذف درجة:**
   - حدد الدرجة من الجدول
   - انقر على "حذف الدرجة"

### عرض جميع الدرجات

1. **انتقل لتبويب "عرض الدرجات"**
2. **استخدم الفلاتر:**
   - المادة (الكل/جغرافيا/تاريخ)
   - الصف
   - نوع الامتحان

3. **تصدير البيانات:**
   - انقر على "تصدير لـ Excel" لحفظ الدرجات

## 📈 التقارير

### أنواع التقارير المتاحة

1. **تقرير الطلاب:**
   - إحصائيات شاملة عن الطلاب
   - تصنيف حسب المرحلة والصف

2. **تقرير الحضور:**
   - إحصائيات الحضور لفترة محددة
   - معدلات الحضور اليومية

3. **تقرير الدرجات:**
   - أداء الطلاب في المواد
   - قوائم أفضل الطلاب

4. **التقرير الشامل:**
   - يجمع جميع الإحصائيات في تقرير واحد

### إنشاء تقرير

1. **فتح نافذة التقارير:**
   - من الشاشة الرئيسية، انقر على "التقارير"

2. **اختيار نوع التقرير:**
   - انتقل للتبويب المناسب

3. **تحديد الإعدادات:**
   - اختر الفلاتر المطلوبة (المرحلة، التاريخ، المادة)
   - حدد الخيارات الإضافية

4. **إنشاء التقرير:**
   - انقر على زر "إنشاء التقرير"
   - انتظر حتى يكتمل التحميل

5. **تصدير أو طباعة:**
   - "تصدير PDF": لحفظ التقرير كملف PDF
   - "تصدير Excel": لحفظ البيانات في جدول بيانات
   - "طباعة": لطباعة التقرير مباشرة

## ⚙️ الإعدادات

### الإعدادات العامة

1. **فتح نافذة الإعدادات:**
   - من الشاشة الرئيسية، انقر على "الإعدادات"

2. **في تبويب "الإعدادات العامة":**
   - عدل اسم المدرسة
   - حدث العام الدراسي
   - اختر إعدادات التطبيق

### إعدادات المعلم

1. **في تبويب "إعدادات المعلم":**
   - عدل اسم المعلم
   - أضف البريد الإلكتروني والهاتف
   - حدد إعدادات المواد ودرجة النجاح

### إدارة قاعدة البيانات

1. **في تبويب "قاعدة البيانات":**
   - راجع معلومات قاعدة البيانات
   - أنشئ نسخة احتياطية
   - استعد من نسخة احتياطية سابقة
   - حسن أداء قاعدة البيانات

### الأمان

1. **في تبويب "الأمان":**
   - غير كلمة المرور
   - حدد إعدادات الأمان

## 🔧 حل المشاكل الشائعة والتحسينات الجديدة

### المشاكل المحلولة في الإصدار الجديد
✅ **مشكلة openpyxl:** تم تثبيت المكتبة تلقائياً
✅ **مشكلة SQLite threading:** تم حلها بإضافة `check_same_thread=False`
✅ **مشكلة رسائل الخطأ:** أصبحت بخلفية بيضاء ونص واضح
✅ **مشكلة التنقل:** شريط تنقل متقدم بدون إغلاق النوافذ

### التطبيق لا يبدأ
1. **استخدم ملفات التشغيل الجديدة:**
   - `RUN_COMPLETE.bat` (الأفضل)
   - `RUN_ENHANCED.bat` (بديل)
   - `START_PERFECT.bat` (أساسي)

2. **إذا ظهرت رسالة "مكتبة openpyxl غير مثبتة":**
   - شغل: `py -m pip install openpyxl`
   - أو استخدم ملف التشغيل الذي يثبتها تلقائياً

3. **إذا ظهرت مشكلة Python:**
   - ثبت Python من Microsoft Store
   - أو من الموقع الرسمي: https://python.org/downloads

### مشاكل قاعدة البيانات
1. **خطأ SQLite threading (محلول):**
   - تم حل هذه المشكلة في الإصدار الجديد
   - إذا ظهرت، أعد تشغيل التطبيق

2. **مشاكل البيانات:**
   - أنشئ نسخة احتياطية من البيانات
   - احذف ملف `data/students.db`
   - أعد تشغيل التطبيق

### مشاكل الواجهة
1. **نافذة تسجيل الدخول صغيرة:**
   - تم تكبيرها إلى 800x900 في الإصدار الجديد
   - حجم الخط أصبح 28px للوضوح

2. **رسائل الخطأ غير واضحة:**
   - تم تحسينها بخلفية بيضاء ونص أسود
   - أزرار ملونة حسب نوع الرسالة

3. **صعوبة التنقل:**
   - شريط تنقل جديد في أعلى النافذة
   - انتقال سريع بدون إغلاق النوافذ

### بطء في الأداء
1. في الإعدادات، انقر على "تحسين قاعدة البيانات"
2. انقر على "ضغط قاعدة البيانات"
3. أعد تشغيل التطبيق

## � المميزات المتقدمة الجديدة

### شريط التنقل المتقدم
- **انتقال سريع:** لا حاجة لإغلاق النوافذ
- **الزر النشط:** يظهر بلون أخضر مميز
- **تصميم احترافي:** أزرار ملونة مع تأثيرات hover
- **تنظيم ذكي:** جميع الوظائف في مكان واحد

### نموذج الطالب الجديد المحسن
- **زر قابل للطي:** يظهر ويختفي حسب الحاجة
- **حقول إضافية:** رقم الهاتف ورقم ولي الأمر
- **حفظ تلقائي:** النموذج يختفي بعد الحفظ
- **تحديث فوري:** قائمة الطلاب تتحدث مباشرة

### رسائل الحوار المحسنة
- **خلفية بيضاء:** وضوح كامل للنص
- **ألوان مميزة:** أحمر للخطأ، أخضر للنجاح، أزرق للأسئلة
- **أزرار كبيرة:** سهولة في الضغط
- **تأثيرات بصرية:** تحسين تجربة المستخدم

## �💡 نصائح للاستخدام الأمثل المحسن

### استخدام شريط التنقل
- **ابدأ من الرئيسية:** للحصول على نظرة عامة
- **استخدم الأزرار العلوية:** للانتقال السريع
- **لاحظ الزر النشط:** لمعرفة موقعك الحالي
- **استخدم تسجيل الخروج:** للخروج الآمن

### إدارة الطلاب المحسنة
- **استخدم زر "طالب جديد":** لإضافة طلاب جدد
- **املأ جميع البيانات:** خاصة أرقام الهواتف
- **استخدم النموذج القابل للطي:** لتوفير المساحة
- **راجع البيانات:** قبل الحفظ النهائي

### تسجيل الحضور
- استخدم التسجيل السريع بالكود لتوفير الوقت
- سجل الحضور في بداية كل حصة
- راجع تقارير الحضور أسبوعياً
- استفد من أرقام الهواتف للتواصل مع أولياء الأمور

### إدارة الدرجات
- أدخل الدرجات فور الانتهاء من التصحيح
- استخدم أنواع امتحانات واضحة ومتسقة
- راجع إحصائيات الدرجات لتتبع أداء الطلاب
- استخدم التقارير لمتابعة التحسن

### النسخ الاحتياطي
- أنشئ نسخة احتياطية أسبوعياً على الأقل
- احفظ النسخ الاحتياطية في مكان آمن
- اختبر استعادة النسخ الاحتياطية دورياً
- استخدم أسماء واضحة للنسخ الاحتياطية

### التقارير المحسنة
- أنشئ تقارير شهرية لمتابعة التقدم
- استخدم التقارير في اجتماعات أولياء الأمور
- احفظ التقارير المهمة كملفات PDF
- استفد من بيانات الهواتف في التقارير

## 📞 الحصول على المساعدة

إذا واجهت أي مشكلة أو كان لديك استفسار:

1. **راجع هذا الدليل أولاً** - معظم الأسئلة مجابة هنا
2. **استخدم ملفات التشغيل المحسنة** - تحل معظم المشاكل تلقائياً
3. **تحقق من ملف `logs/`** للأخطاء التقنية
4. **جرب إعادة تشغيل التطبيق** - يحل مشاكل كثيرة
5. **تواصل مع مطور التطبيق** للمساعدة المتقدمة

## 🎯 ملخص الإصدار الكامل النهائي v4.0

### ✅ **المشاكل المحلولة:**
- مشكلة openpyxl محلولة نهائياً
- مشكلة SQLite threading محلولة
- رسائل الخطأ أصبحت واضحة
- نافذة تسجيل الدخول كبيرة ووضحة
- إزالة التلميحات المزعجة

### 🚀 **المميزات الجديدة:**
- شريط تنقل متقدم في أعلى النافذة
- زر "طالب جديد" قابل للطي
- حقول جديدة: رقم الهاتف ورقم ولي الأمر
- انتقال سريع بين الصفحات
- واجهة موحدة ومتكاملة

### 🎓 **مصمم خصيصاً لمستر أحمد عادل**
معلم الجغرافيا والتاريخ

### 📁 **ملفات التشغيل:**
- `RUN_COMPLETE.bat` - **الملف الرئيسي المُوصى به**
- `RUN_ENHANCED.bat` - ملف بديل محسن
- `START_PERFECT.bat` - ملف تشغيل أساسي

### 🔐 **بيانات الدخول:**
- اسم المستخدم: `admin`
- كلمة المرور: `admin123`

---

## 🎉 **استمتع بالنظام الكامل المحسن!**

**نظام إدارة الطلاب أصبح الآن مكتملاً ومحسناً بالكامل مع جميع المميزات المطلوبة. نتمنى لك تجربة ممتعة ومفيدة! 🎓✨**

---

*تم التطوير والتحسين بعناية فائقة لضمان أفضل تجربة استخدام ممكنة.*
