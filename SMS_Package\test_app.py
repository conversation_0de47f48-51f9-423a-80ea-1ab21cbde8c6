#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار التطبيق
Application Testing Script

هذا الملف يختبر جميع مكونات التطبيق للتأكد من عملها بشكل صحيح
"""

import sys
import os
import unittest
from datetime import date, datetime

# إضافة مسار المشروع
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

class TestDatabase(unittest.TestCase):
    """اختبار قاعدة البيانات"""
    
    def setUp(self):
        """إعداد الاختبار"""
        from src.database.database_manager import DatabaseManager
        self.db = DatabaseManager(':memory:')  # قاعدة بيانات في الذاكرة للاختبار
        self.db.initialize_database()
    
    def test_database_creation(self):
        """اختبار إنشاء قاعدة البيانات"""
        # التحقق من وجود الجداول
        tables = self.db.fetch_all("""
            SELECT name FROM sqlite_master 
            WHERE type='table' AND name NOT LIKE 'sqlite_%'
        """)
        
        table_names = [table['name'] for table in tables]
        expected_tables = ['students', 'attendance', 'grades', 'settings', 'users']
        
        for table in expected_tables:
            self.assertIn(table, table_names, f"الجدول {table} غير موجود")
    
    def test_settings(self):
        """اختبار الإعدادات"""
        # اختبار جلب إعداد
        teacher_name = self.db.get_setting('teacher_name')
        self.assertIsNotNone(teacher_name, "اسم المعلم غير موجود")
        
        # اختبار تحديث إعداد
        result = self.db.update_setting('test_setting', 'test_value')
        self.assertTrue(result, "فشل في تحديث الإعداد")
        
        # التحقق من التحديث
        value = self.db.get_setting('test_setting')
        self.assertEqual(value, 'test_value', "قيمة الإعداد غير صحيحة")

class TestStudentModel(unittest.TestCase):
    """اختبار نموذج الطالب"""
    
    def setUp(self):
        """إعداد الاختبار"""
        from src.database.database_manager import DatabaseManager
        from src.models.student import Student
        
        self.db = DatabaseManager(':memory:')
        self.db.initialize_database()
        self.student_model = Student(self.db)
    
    def test_create_student(self):
        """اختبار إنشاء طالب"""
        student_data = {
            'student_code': 'STD0001',
            'full_name': 'أحمد محمد علي',
            'gender': 'ذكر',
            'stage': 'إعدادي',
            'grade': 'أولى إعدادي',
            'geography_score': 85.5,
            'history_score': 90.0
        }
        
        student_id = self.student_model.create_student(student_data)
        self.assertIsNotNone(student_id, "فشل في إنشاء الطالب")
        
        # التحقق من البيانات
        student = self.student_model.get_student_by_id(student_id)
        self.assertEqual(student['full_name'], student_data['full_name'])
        self.assertEqual(student['student_code'], student_data['student_code'])
    
    def test_duplicate_student_code(self):
        """اختبار منع تكرار كود الطالب"""
        student_data = {
            'student_code': 'STD0001',
            'full_name': 'طالب أول',
            'gender': 'ذكر',
            'stage': 'إعدادي',
            'grade': 'أولى إعدادي'
        }
        
        # إنشاء الطالب الأول
        student_id1 = self.student_model.create_student(student_data)
        self.assertIsNotNone(student_id1)
        
        # محاولة إنشاء طالب بنفس الكود
        student_data['full_name'] = 'طالب ثاني'
        student_id2 = self.student_model.create_student(student_data)
        self.assertIsNone(student_id2, "تم السماح بتكرار كود الطالب")
    
    def test_generate_student_code(self):
        """اختبار توليد كود الطالب"""
        code = self.student_model.generate_student_code()
        self.assertTrue(code.startswith('STD'), "كود الطالب لا يبدأ بـ STD")
        self.assertEqual(len(code), 7, "طول كود الطالب غير صحيح")
    
    def test_search_students(self):
        """اختبار البحث عن الطلاب"""
        # إنشاء طلاب للاختبار
        students_data = [
            {'student_code': 'STD0001', 'full_name': 'أحمد محمد', 'gender': 'ذكر', 'stage': 'إعدادي', 'grade': 'أولى إعدادي'},
            {'student_code': 'STD0002', 'full_name': 'فاطمة علي', 'gender': 'أنثى', 'stage': 'إعدادي', 'grade': 'ثانية إعدادي'},
            {'student_code': 'STD0003', 'full_name': 'محمد أحمد', 'gender': 'ذكر', 'stage': 'ثانوي', 'grade': 'أولى ثانوي'}
        ]
        
        for data in students_data:
            self.student_model.create_student(data)
        
        # البحث بالاسم
        results = self.student_model.search_students('أحمد')
        self.assertEqual(len(results), 2, "نتائج البحث بالاسم غير صحيحة")
        
        # البحث بالكود
        results = self.student_model.search_students('STD0002')
        self.assertEqual(len(results), 1, "نتائج البحث بالكود غير صحيحة")
        self.assertEqual(results[0]['full_name'], 'فاطمة علي')

class TestAttendanceModel(unittest.TestCase):
    """اختبار نموذج الحضور"""
    
    def setUp(self):
        """إعداد الاختبار"""
        from src.database.database_manager import DatabaseManager
        from src.models.student import Student
        from src.models.attendance import Attendance
        
        self.db = DatabaseManager(':memory:')
        self.db.initialize_database()
        self.student_model = Student(self.db)
        self.attendance_model = Attendance(self.db)
        
        # إنشاء طالب للاختبار
        student_data = {
            'student_code': 'STD0001',
            'full_name': 'طالب اختبار',
            'gender': 'ذكر',
            'stage': 'إعدادي',
            'grade': 'أولى إعدادي'
        }
        self.student_id = self.student_model.create_student(student_data)
    
    def test_mark_attendance(self):
        """اختبار تسجيل الحضور"""
        today = date.today()
        result = self.attendance_model.mark_attendance(self.student_id, today, 'حاضر')
        self.assertTrue(result, "فشل في تسجيل الحضور")
        
        # التحقق من التسجيل
        attendance = self.attendance_model.get_student_attendance(self.student_id)
        self.assertEqual(len(attendance), 1, "لم يتم تسجيل الحضور")
        self.assertEqual(attendance[0]['status'], 'حاضر')
    
    def test_mark_attendance_by_code(self):
        """اختبار تسجيل الحضور بالكود"""
        today = date.today()
        result = self.attendance_model.mark_attendance_by_code('STD0001', today, 'متأخر')
        self.assertTrue(result, "فشل في تسجيل الحضور بالكود")

class TestGradesModel(unittest.TestCase):
    """اختبار نموذج الدرجات"""
    
    def setUp(self):
        """إعداد الاختبار"""
        from src.database.database_manager import DatabaseManager
        from src.models.student import Student
        from src.models.grades import Grades
        
        self.db = DatabaseManager(':memory:')
        self.db.initialize_database()
        self.student_model = Student(self.db)
        self.grades_model = Grades(self.db)
        
        # إنشاء طالب للاختبار
        student_data = {
            'student_code': 'STD0001',
            'full_name': 'طالب اختبار',
            'gender': 'ذكر',
            'stage': 'إعدادي',
            'grade': 'أولى إعدادي'
        }
        self.student_id = self.student_model.create_student(student_data)
    
    def test_add_grade(self):
        """اختبار إضافة درجة"""
        grade_id = self.grades_model.add_grade(
            self.student_id, 'جغرافيا', 'امتحان شهري', 85.0, 100.0, date.today()
        )
        self.assertIsNotNone(grade_id, "فشل في إضافة الدرجة")
        
        # التحقق من الدرجة
        grades = self.grades_model.get_student_grades(self.student_id)
        self.assertEqual(len(grades), 1, "لم يتم حفظ الدرجة")
        self.assertEqual(grades[0]['score'], 85.0)

def run_tests():
    """تشغيل جميع الاختبارات"""
    print("🧪 بدء اختبار التطبيق...")
    print("=" * 50)
    
    # إنشاء مجموعة الاختبارات
    test_suite = unittest.TestSuite()
    
    # إضافة اختبارات قاعدة البيانات
    test_suite.addTest(unittest.makeSuite(TestDatabase))
    
    # إضافة اختبارات النماذج
    test_suite.addTest(unittest.makeSuite(TestStudentModel))
    test_suite.addTest(unittest.makeSuite(TestAttendanceModel))
    test_suite.addTest(unittest.makeSuite(TestGradesModel))
    
    # تشغيل الاختبارات
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # عرض النتائج
    print("\n" + "=" * 50)
    if result.wasSuccessful():
        print("✅ جميع الاختبارات نجحت!")
    else:
        print(f"❌ فشل {len(result.failures)} اختبار")
        print(f"⚠️ خطأ في {len(result.errors)} اختبار")
    
    return result.wasSuccessful()

if __name__ == "__main__":
    success = run_tests()
    if not success:
        sys.exit(1)
