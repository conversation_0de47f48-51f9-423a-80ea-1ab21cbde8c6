@echo off
chcp 65001 >nul
title نظام إدارة الطلاب - الإصدار الكامل النهائي

cls
color 0F
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    نظام إدارة الطلاب                        ║
echo ║               الإصدار الكامل النهائي v4.0                   ║
echo ║                   مستر أحمد عادل                           ║
echo ║              معلم الجغرافيا والتاريخ                        ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 🎉 التحسينات الجديدة في هذا الإصدار:
echo.
echo ✅ حل مشكلة openpyxl و SQLite threading
echo ✅ شريط تنقل في أعلى النافذة الرئيسية
echo ✅ أزرار للانتقال بين الصفحات بدون إغلاق
echo ✅ إزالة التلميح من صفحة تسجيل الدخول
echo ✅ تحسين تجربة المستخدم والتنقل
echo ✅ واجهة موحدة ومتكاملة
echo.

echo 🚀 بدء تشغيل النظام الكامل...
echo.

REM محاولة تشغيل البرنامج بطرق مختلفة

REM الطريقة الأولى: py launcher
py --version >nul 2>&1
if not errorlevel 1 (
    echo ✅ تم العثور على Python!
    py --version
    echo.
    echo 🎯 تشغيل نظام إدارة الطلاب الكامل...
    echo.
    py main.py
    goto end
)

REM الطريقة الثانية: python command
python --version >nul 2>&1
if not errorlevel 1 (
    echo ✅ تم العثور على Python!
    python --version
    echo.
    echo 🎯 تشغيل نظام إدارة الطلاب الكامل...
    echo.
    python main.py
    goto end
)

REM الطريقة الثالثة: python3 command
python3 --version >nul 2>&1
if not errorlevel 1 (
    echo ✅ تم العثور على Python3!
    python3 --version
    echo.
    echo 🎯 تشغيل نظام إدارة الطلاب الكامل...
    echo.
    python3 main.py
    goto end
)

echo ❌ لم يتم العثور على Python!
echo.
echo 💡 الحلول المتاحة:
echo.
echo 1️⃣ تثبيت Python من Microsoft Store:
echo    - افتح Microsoft Store
echo    - ابحث عن "Python"
echo    - ثبت Python 3.9 أو أحدث
echo.
echo 2️⃣ تثبيت Python من الموقع الرسمي:
echo    - اذهب إلى: https://python.org/downloads
echo    - حمل وثبت Python
echo    - تأكد من تحديد "Add Python to PATH"
echo.
echo 3️⃣ بناء ملف EXE:
echo    - شغل: build_simple.bat
echo    - احصل على ملف EXE يعمل بدون Python
echo.

echo 🌐 فتح صفحة تحميل Python...
start https://python.org/downloads

:end
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                      دليل الاستخدام الكامل                  ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.
echo 🔐 بيانات تسجيل الدخول:
echo    👤 اسم المستخدم: admin
echo    🔑 كلمة المرور: admin123
echo.
echo 🧭 شريط التنقل الجديد:
echo    🏠 الرئيسية - العودة للوحة التحكم
echo    👥 إدارة الطلاب - إضافة وتعديل الطلاب
echo    📋 تسجيل الحضور - تسجيل حضور الطلاب
echo    📊 إدارة الدرجات - إدخال ومتابعة الدرجات
echo    📈 التقارير - تقارير شاملة PDF/Excel
echo    ⚙️ الإعدادات - إعدادات النظام
echo    🚪 تسجيل الخروج - الخروج من النظام
echo.
echo 📝 مميزات التنقل:
echo    • انتقال سريع بين الصفحات
echo    • لا حاجة لإغلاق النوافذ
echo    • الزر النشط يظهر بلون مختلف
echo    • واجهة موحدة ومتكاملة
echo.
echo 📚 المميزات الكاملة:
echo    ✅ إدارة الطلاب مع جميع البيانات
echo    ✅ تسجيل الحضور السريع بالكود
echo    ✅ إدارة درجات الجغرافيا والتاريخ
echo    ✅ تقارير احترافية PDF/Excel
echo    ✅ نظام نسخ احتياطي متقدم
echo    ✅ واجهة عربية محسنة
echo    ✅ رسائل واضحة بخلفية بيضاء
echo    ✅ شريط تنقل متقدم
echo.
echo 🎓 تم التطوير والتحسين خصيصاً لمستر أحمد عادل
echo    معلم الجغرافيا والتاريخ
echo.
echo 📞 للدعم الفني: راجع ملف USER_GUIDE.md
echo.
echo 🎉 استمتع بالنظام الكامل المحسن!
echo.
pause
