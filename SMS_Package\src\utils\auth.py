# -*- coding: utf-8 -*-
"""
نظام المصادقة والأمان
Authentication and Security System
"""

import hashlib
import secrets
from datetime import datetime
from typing import Optional, Dict, Any, List
from ..database.database_manager import DatabaseManager

class AuthManager:
    """مدير المصادقة والأمان"""
    
    def __init__(self, db_manager: DatabaseManager):
        self.db = db_manager
        self.current_user: Optional[Dict[str, Any]] = None
    
    def hash_password(self, password: str) -> str:
        """تشفير كلمة المرور"""
        return hashlib.sha256(password.encode()).hexdigest()
    
    def verify_password(self, password: str, hashed_password: str) -> bool:
        """التحقق من كلمة المرور"""
        return self.hash_password(password) == hashed_password
    
    def login(self, username: str, password: str) -> bool:
        """
        تسجيل الدخول
        
        Args:
            username: اسم المستخدم
            password: كلمة المرور
            
        Returns:
            True في حالة نجاح تسجيل الدخول، False في حالة الفشل
        """
        try:
            # جلب بيانات المستخدم
            user = self.db.fetch_one("""
                SELECT * FROM users 
                WHERE username = ? AND is_active = 1
            """, (username,))
            
            if not user:
                return False
            
            # التحقق من كلمة المرور
            if not self.verify_password(password, user['password_hash']):
                return False
            
            # تحديث وقت آخر دخول
            self.db.execute_query("""
                UPDATE users 
                SET last_login = CURRENT_TIMESTAMP 
                WHERE id = ?
            """, (user['id'],))
            
            # حفظ بيانات المستخدم الحالي
            self.current_user = dict(user)
            self.current_user.pop('password_hash', None)  # إزالة كلمة المرور من الذاكرة
            
            return True
            
        except Exception as e:
            print(f"خطأ في تسجيل الدخول: {e}")
            return False
    
    def logout(self):
        """تسجيل الخروج"""
        self.current_user = None
    
    def is_logged_in(self) -> bool:
        """التحقق من حالة تسجيل الدخول"""
        return self.current_user is not None
    
    def get_current_user(self) -> Optional[Dict[str, Any]]:
        """جلب بيانات المستخدم الحالي"""
        return self.current_user
    
    def change_password(self, old_password: str, new_password: str) -> bool:
        """تغيير كلمة المرور"""
        if not self.is_logged_in():
            return False
        
        try:
            # التحقق من كلمة المرور القديمة
            user = self.db.fetch_one("""
                SELECT password_hash FROM users WHERE id = ?
            """, (self.current_user['id'],))
            
            if not user or not self.verify_password(old_password, user['password_hash']):
                return False
            
            # تحديث كلمة المرور
            new_hash = self.hash_password(new_password)
            self.db.execute_query("""
                UPDATE users 
                SET password_hash = ? 
                WHERE id = ?
            """, (new_hash, self.current_user['id']))
            
            return True
            
        except Exception as e:
            print(f"خطأ في تغيير كلمة المرور: {e}")
            return False
    
    def create_user(self, username: str, password: str, full_name: str, 
                   role: str = "admin") -> Optional[int]:
        """إنشاء مستخدم جديد"""
        try:
            # التحقق من عدم تكرار اسم المستخدم
            existing = self.db.fetch_one(
                "SELECT id FROM users WHERE username = ?", (username,)
            )
            
            if existing:
                raise ValueError(f"اسم المستخدم {username} موجود مسبقاً")
            
            # إنشاء المستخدم الجديد
            password_hash = self.hash_password(password)
            cursor = self.db.execute_query("""
                INSERT INTO users (username, password_hash, full_name, role)
                VALUES (?, ?, ?, ?)
            """, (username, password_hash, full_name, role))
            
            return cursor.lastrowid
            
        except Exception as e:
            print(f"خطأ في إنشاء المستخدم: {e}")
            return None
    
    def update_user_profile(self, full_name: str) -> bool:
        """تحديث الملف الشخصي للمستخدم"""
        if not self.is_logged_in():
            return False
        
        try:
            self.db.execute_query("""
                UPDATE users 
                SET full_name = ? 
                WHERE id = ?
            """, (full_name, self.current_user['id']))
            
            # تحديث البيانات في الذاكرة
            self.current_user['full_name'] = full_name
            
            return True
            
        except Exception as e:
            print(f"خطأ في تحديث الملف الشخصي: {e}")
            return False
    
    def get_all_users(self) -> List[Dict[str, Any]]:
        """جلب جميع المستخدمين (بدون كلمات المرور)"""
        return self.db.fetch_all("""
            SELECT id, username, full_name, role, is_active, 
                   created_at, last_login
            FROM users 
            ORDER BY created_at DESC
        """)
    
    def deactivate_user(self, user_id: int) -> bool:
        """إلغاء تفعيل مستخدم"""
        try:
            self.db.execute_query("""
                UPDATE users 
                SET is_active = 0 
                WHERE id = ?
            """, (user_id,))
            return True
        except Exception as e:
            print(f"خطأ في إلغاء تفعيل المستخدم: {e}")
            return False
    
    def activate_user(self, user_id: int) -> bool:
        """تفعيل مستخدم"""
        try:
            self.db.execute_query("""
                UPDATE users 
                SET is_active = 1 
                WHERE id = ?
            """, (user_id,))
            return True
        except Exception as e:
            print(f"خطأ في تفعيل المستخدم: {e}")
            return False
