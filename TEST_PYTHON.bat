@echo off
title اختبار Python

cls
echo.
echo ========================================
echo        اختبار Python
echo ========================================
echo.

echo جاري اختبار Python...
echo.

python --version
if not errorlevel 1 (
    echo ✅ Python يعمل!
    goto test_pyqt5
)

py --version
if not errorlevel 1 (
    echo ✅ Python يعمل!
    goto test_pyqt5
)

echo ❌ Python لم يكتمل تثبيته بعد
echo انتظر حتى اكتمال التثبيت من Microsoft Store
goto end

:test_pyqt5
echo.
echo جاري اختبار PyQt5...
python -c "import PyQt5; print('✅ PyQt5 موجود')" 2>nul
if not errorlevel 1 goto ready

echo تثبيت PyQt5...
python -m pip install PyQt5
if not errorlevel 1 goto ready

py -m pip install PyQt5
if not errorlevel 1 goto ready

echo ❌ فشل في تثبيت PyQt5
goto end

:ready
echo.
echo ✅ كل شيء جاهز!
echo.
echo يمكنك الآن تشغيل النظام من:
echo RUN_COMPACT_FINAL.bat
echo.
echo بيانات الدخول:
echo اسم المستخدم: admin
echo كلمة المرور: admin123
echo.

:end
pause
