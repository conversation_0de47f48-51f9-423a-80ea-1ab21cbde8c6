# -*- coding: utf-8 -*-
"""
ملف التكوين العام للتطبيق
Application Configuration File

يحتوي على جميع الإعدادات والثوابت المستخدمة في التطبيق
"""

import os
from pathlib import Path

# معلومات التطبيق
APP_INFO = {
    'name': 'نظام إدارة الطلاب',
    'name_en': 'Student Management System',
    'version': '1.0.0',
    'author': 'مستر أحمد عادل',
    'description': 'نظام شامل لإدارة الطلاب وتتبع الحضور والدرجات',
    'copyright': '© 2024 مستر أحمد عادل',
    'website': '',
    'email': '',
    'phone': ''
}

# مسارات التطبيق
BASE_DIR = Path(__file__).parent
PATHS = {
    'base': BASE_DIR,
    'src': BASE_DIR / 'src',
    'data': BASE_DIR / 'data',
    'logs': BASE_DIR / 'logs',
    'backups': BASE_DIR / 'backups',
    'exports': BASE_DIR / 'exports',
    'assets': BASE_DIR / 'assets',
    'temp': BASE_DIR / 'temp'
}

# قاعدة البيانات
DATABASE = {
    'name': 'students.db',
    'path': PATHS['data'] / 'students.db',
    'backup_interval': 7,  # أيام
    'auto_backup': False,
    'max_backups': 10
}

# إعدادات المصادقة
AUTH = {
    'default_username': 'admin',
    'default_password': 'admin123',
    'session_timeout': 3600,  # ثانية
    'max_login_attempts': 3,
    'lockout_duration': 300  # ثانية
}

# إعدادات الواجهة
UI = {
    'theme': 'default',
    'language': 'ar',
    'font_family': 'Segoe UI',
    'font_size': 10,
    'window_size': (1200, 800),
    'window_min_size': (800, 600),
    'rtl_layout': True
}

# إعدادات المدرسة الافتراضية
SCHOOL = {
    'name': 'مدرسة النجاح',
    'current_year': '2024-2025',
    'stages': ['إعدادي', 'ثانوي'],
    'grades': {
        'إعدادي': ['أولى إعدادي', 'ثانية إعدادي', 'ثالثة إعدادي'],
        'ثانوي': ['أولى ثانوي', 'ثانية ثانوي', 'ثالثة ثانوي']
    },
    'subjects': ['جغرافيا', 'تاريخ'],
    'passing_grade': 50
}

# إعدادات المعلم الافتراضية
TEACHER = {
    'name': 'مستر أحمد عادل',
    'email': '',
    'phone': '',
    'subjects': ['جغرافيا', 'تاريخ']
}

# أنواع الامتحانات
EXAM_TYPES = [
    'امتحان شهري',
    'امتحان نصف الفصل',
    'امتحان نهاية الفصل',
    'واجب منزلي',
    'مشاركة صفية',
    'بحث',
    'مشروع',
    'اختبار قصير'
]

# حالات الحضور
ATTENDANCE_STATUS = {
    'present': 'حاضر',
    'absent': 'غائب',
    'late': 'متأخر',
    'excused': 'غياب بعذر'
}

# إعدادات التقارير
REPORTS = {
    'formats': ['PDF', 'Excel', 'Word'],
    'default_format': 'PDF',
    'include_charts': True,
    'include_statistics': True,
    'page_size': 'A4',
    'orientation': 'portrait'
}

# إعدادات التصدير
EXPORT = {
    'excel_format': 'xlsx',
    'pdf_quality': 'high',
    'include_metadata': True,
    'compress_files': False
}

# إعدادات السجلات
LOGGING = {
    'level': 'INFO',
    'file_size': 10 * 1024 * 1024,  # 10 MB
    'backup_count': 5,
    'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    'date_format': '%Y-%m-%d %H:%M:%S'
}

# إعدادات الأمان
SECURITY = {
    'encrypt_database': False,
    'backup_encryption': False,
    'password_min_length': 6,
    'password_complexity': False,
    'auto_logout': False,
    'audit_trail': True
}

# إعدادات الأداء
PERFORMANCE = {
    'cache_size': 100,
    'lazy_loading': True,
    'batch_size': 1000,
    'connection_pool': 5
}

# الألوان والأنماط
COLORS = {
    'primary': '#2c3e50',
    'secondary': '#34495e',
    'success': '#27ae60',
    'warning': '#f39c12',
    'danger': '#e74c3c',
    'info': '#3498db',
    'light': '#ecf0f1',
    'dark': '#2c3e50',
    'white': '#ffffff',
    'gray': '#bdc3c7'
}

# رسائل النظام
MESSAGES = {
    'success': {
        'student_added': 'تم إضافة الطالب بنجاح',
        'student_updated': 'تم تحديث بيانات الطالب بنجاح',
        'student_deleted': 'تم حذف الطالب بنجاح',
        'attendance_recorded': 'تم تسجيل الحضور بنجاح',
        'grade_added': 'تم إضافة الدرجة بنجاح',
        'backup_created': 'تم إنشاء النسخة الاحتياطية بنجاح',
        'settings_saved': 'تم حفظ الإعدادات بنجاح'
    },
    'error': {
        'student_not_found': 'لم يتم العثور على الطالب',
        'duplicate_student_code': 'كود الطالب موجود مسبقاً',
        'invalid_grade': 'الدرجة المدخلة غير صحيحة',
        'database_error': 'خطأ في قاعدة البيانات',
        'file_not_found': 'الملف غير موجود',
        'permission_denied': 'ليس لديك صلاحية لهذا الإجراء'
    },
    'warning': {
        'unsaved_changes': 'يوجد تغييرات غير محفوظة',
        'delete_confirmation': 'هل أنت متأكد من الحذف؟',
        'backup_old': 'النسخة الاحتياطية قديمة',
        'low_disk_space': 'مساحة القرص منخفضة'
    },
    'info': {
        'loading': 'جاري التحميل...',
        'processing': 'جاري المعالجة...',
        'saving': 'جاري الحفظ...',
        'exporting': 'جاري التصدير...'
    }
}

# إعدادات التحديث
UPDATE = {
    'check_on_startup': False,
    'auto_download': False,
    'update_url': '',
    'version_check_url': ''
}

# إعدادات التطوير
DEBUG = {
    'enabled': False,
    'show_sql': False,
    'log_level': 'DEBUG',
    'test_mode': False
}

def get_setting(category, key, default=None):
    """الحصول على إعداد معين"""
    settings_map = {
        'app': APP_INFO,
        'database': DATABASE,
        'auth': AUTH,
        'ui': UI,
        'school': SCHOOL,
        'teacher': TEACHER,
        'reports': REPORTS,
        'export': EXPORT,
        'logging': LOGGING,
        'security': SECURITY,
        'performance': PERFORMANCE,
        'colors': COLORS,
        'messages': MESSAGES,
        'update': UPDATE,
        'debug': DEBUG
    }
    
    if category in settings_map:
        return settings_map[category].get(key, default)
    return default

def create_directories():
    """إنشاء المجلدات المطلوبة"""
    for path in PATHS.values():
        if isinstance(path, Path):
            path.mkdir(parents=True, exist_ok=True)

def get_database_path():
    """الحصول على مسار قاعدة البيانات"""
    return str(DATABASE['path'])

def get_app_version():
    """الحصول على إصدار التطبيق"""
    return APP_INFO['version']

def get_app_name():
    """الحصول على اسم التطبيق"""
    return APP_INFO['name']

def is_debug_mode():
    """التحقق من وضع التطوير"""
    return DEBUG['enabled']

# إنشاء المجلدات عند استيراد الملف
if __name__ != '__main__':
    create_directories()
