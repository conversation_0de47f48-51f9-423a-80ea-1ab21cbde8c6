# -*- coding: utf-8 -*-
"""
نظام الفيديوهات المربوطة بالامتحانات
Video-Exam Integrated System
"""

import os
import json
import time
from datetime import datetime
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                            QPushButton, QDialog, QDialogButtonBox, QFormLayout,
                            QLineEdit, QTextEdit, QComboBox, QSpinBox, QCheckBox,
                            QMessageBox, QFrame, QProgressBar, QRadioButton,
                            QButtonGroup, QScrollArea, QGroupBox)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal, QThread
from PyQt5.QtGui import QFont, QPixmap
from PyQt5.QtMultimedia import QMediaPlayer, QMediaContent
from PyQt5.QtMultimediaWidgets import QVideoWidget
from PyQt5.QtCore import QUrl

class VideoExamPlayer(QDialog):
    """مشغل الفيديو مع الامتحان المربوط"""
    
    exam_completed = pyqtSignal(dict)  # إشارة عند انتهاء الامتحان
    
    def __init__(self, video_info, student_info, exam_questions):
        super().__init__()
        self.video_info = video_info
        self.student_info = student_info
        self.exam_questions = exam_questions
        self.watch_count = self.get_watch_count()
        self.max_watches = video_info.get('max_views', 3)
        self.video_finished = False
        self.exam_started = False
        
        self.init_ui()
        self.setup_video_player()
        
    def init_ui(self):
        """إعداد واجهة المشغل"""
        self.setWindowTitle(f"فيديو تعليمي: {self.video_info['title']}")
        self.setModal(True)
        self.showFullScreen()  # شاشة كاملة إجبارية
        
        # منع إغلاق النافذة
        self.setWindowFlags(Qt.Window | Qt.WindowStaysOnTopHint | Qt.FramelessWindowHint)
        
        self.setStyleSheet("""
            QDialog {
                background-color: #1a1a1a;
                color: white;
            }
            QLabel {
                color: white;
                font-size: 16px;
                font-weight: bold;
            }
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                padding: 15px;
                border-radius: 8px;
                font-size: 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
            QPushButton:disabled {
                background-color: #7f8c8d;
            }
        """)
        
        layout = QVBoxLayout()
        
        # معلومات الفيديو والطالب
        info_frame = self.create_info_frame()
        layout.addWidget(info_frame)
        
        # مشغل الفيديو
        self.video_widget = QVideoWidget()
        self.video_widget.setMinimumHeight(400)
        layout.addWidget(self.video_widget)
        
        # أزرار التحكم
        controls_frame = self.create_controls_frame()
        layout.addWidget(controls_frame)
        
        # شريط التقدم
        self.progress_bar = QProgressBar()
        self.progress_bar.setStyleSheet("""
            QProgressBar {
                border: 2px solid #34495e;
                border-radius: 5px;
                text-align: center;
                font-size: 14px;
                font-weight: bold;
            }
            QProgressBar::chunk {
                background-color: #3498db;
                border-radius: 3px;
            }
        """)
        layout.addWidget(self.progress_bar)
        
        # رسالة التحذير
        warning_label = QLabel(
            "⚠️ تحذير: هذا المحتوى محمي بحقوق الملكية الفكرية\n"
            "🚫 أي محاولة تسجيل أو نسخ ستؤدي لإغلاق النظام فوراً\n"
            "📝 بعد انتهاء الفيديو سيظهر الامتحان تلقائياً ولا يمكن تجاهله"
        )
        warning_label.setAlignment(Qt.AlignCenter)
        warning_label.setStyleSheet("color: #e74c3c; font-size: 14px; font-weight: bold; padding: 10px;")
        layout.addWidget(warning_label)
        
        self.setLayout(layout)
        
    def create_info_frame(self):
        """إنشاء إطار المعلومات"""
        frame = QFrame()
        frame.setStyleSheet("background-color: #2c3e50; padding: 15px; border-radius: 8px;")
        layout = QHBoxLayout()
        
        # معلومات الفيديو
        video_info = QLabel(
            f"📹 {self.video_info['title']}\n"
            f"📚 {self.video_info['subject']} - {self.video_info['grade']}\n"
            f"📅 {self.video_info['term']}"
        )
        layout.addWidget(video_info)
        
        # معلومات الطالب
        student_info = QLabel(
            f"👤 الطالب: {self.student_info['name']}\n"
            f"🆔 الكود: {self.student_info['code']}\n"
            f"👁️ المشاهدة: {self.watch_count + 1}/{self.max_watches}"
        )
        layout.addWidget(student_info)
        
        # معلومات الامتحان
        exam_info = QLabel(
            f"📝 الامتحان: {len(self.exam_questions)} سؤال\n"
            f"⏱️ مدة الامتحان: 30 دقيقة\n"
            f"✅ تصحيح تلقائي"
        )
        layout.addWidget(exam_info)
        
        frame.setLayout(layout)
        return frame
        
    def create_controls_frame(self):
        """إنشاء إطار أزرار التحكم"""
        frame = QFrame()
        layout = QHBoxLayout()
        
        # زر التشغيل/الإيقاف
        self.play_pause_btn = QPushButton("▶️ تشغيل")
        self.play_pause_btn.clicked.connect(self.toggle_play_pause)
        layout.addWidget(self.play_pause_btn)
        
        # زر الإيقاف
        self.stop_btn = QPushButton("⏹️ إيقاف")
        self.stop_btn.clicked.connect(self.stop_video)
        layout.addWidget(self.stop_btn)
        
        # زر بدء الامتحان (معطل في البداية)
        self.exam_btn = QPushButton("📝 بدء الامتحان")
        self.exam_btn.clicked.connect(self.start_exam)
        self.exam_btn.setEnabled(False)
        self.exam_btn.setStyleSheet("background-color: #7f8c8d;")  # رمادي معطل
        layout.addWidget(self.exam_btn)
        
        # زر الخروج (معطل حتى انتهاء الامتحان)
        self.exit_btn = QPushButton("🚪 خروج")
        self.exit_btn.clicked.connect(self.safe_exit)
        self.exit_btn.setEnabled(False)
        layout.addWidget(self.exit_btn)
        
        frame.setLayout(layout)
        return frame
        
    def setup_video_player(self):
        """إعداد مشغل الفيديو"""
        self.media_player = QMediaPlayer()
        self.media_player.setVideoOutput(self.video_widget)
        
        # ربط الإشارات
        self.media_player.mediaStatusChanged.connect(self.handle_media_status)
        self.media_player.positionChanged.connect(self.update_progress)
        self.media_player.durationChanged.connect(self.set_duration)
        
        # تحميل الفيديو
        self.load_video()
        
    def load_video(self):
        """تحميل الفيديو"""
        try:
            # فحص عدد المشاهدات
            if self.watch_count >= self.max_watches:
                QMessageBox.critical(self, "انتهت المشاهدات", 
                                   f"لقد استنفدت عدد المشاهدات المسموحة ({self.max_watches})")
                self.reject()
                return
                
            # في التطبيق الحقيقي، هنا سيتم فك تشفير الفيديو
            # محاكاة تحميل الفيديو
            video_path = self.video_info.get('file_path', '')
            if video_path and os.path.exists(video_path):
                media_content = QMediaContent(QUrl.fromLocalFile(os.path.abspath(video_path)))
                self.media_player.setMedia(media_content)
            else:
                # محاكاة فيديو تجريبي
                QMessageBox.information(self, "فيديو تجريبي", 
                                       "هذا فيديو تجريبي للعرض\nسيتم تشغيل محاكاة للفيديو")
                
            # تحديث عداد المشاهدات
            self.watch_count += 1
            self.save_watch_count()
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل الفيديو: {str(e)}")
            
    def toggle_play_pause(self):
        """تشغيل/إيقاف مؤقت"""
        if self.media_player.state() == QMediaPlayer.PlayingState:
            self.media_player.pause()
            self.play_pause_btn.setText("▶️ تشغيل")
        else:
            self.media_player.play()
            self.play_pause_btn.setText("⏸️ إيقاف مؤقت")
            
    def stop_video(self):
        """إيقاف الفيديو"""
        self.media_player.stop()
        self.play_pause_btn.setText("▶️ تشغيل")
        
    def handle_media_status(self, status):
        """التعامل مع حالة الوسائط"""
        if status == QMediaPlayer.EndOfMedia:
            # انتهى الفيديو
            self.video_finished = True
            self.play_pause_btn.setText("✅ انتهى الفيديو")
            self.play_pause_btn.setEnabled(False)
            
            # تفعيل زر الامتحان
            self.exam_btn.setEnabled(True)
            self.exam_btn.setStyleSheet("background-color: #27ae60;")  # أخضر نشط
            
            QMessageBox.information(self, "انتهى الفيديو", 
                                   "🎉 تم انتهاء الفيديو بنجاح!\n"
                                   "📝 يجب عليك الآن أداء الامتحان\n"
                                   "⚠️ لا يمكن الخروج قبل إنهاء الامتحان")
            
            # بدء الامتحان تلقائياً بعد 3 ثوان
            QTimer.singleShot(3000, self.auto_start_exam)
            
    def auto_start_exam(self):
        """بدء الامتحان تلقائياً"""
        if not self.exam_started:
            self.start_exam()
            
    def update_progress(self, position):
        """تحديث شريط التقدم"""
        duration = self.media_player.duration()
        if duration > 0:
            progress = (position / duration) * 100
            self.progress_bar.setValue(int(progress))
            
            # عرض الوقت
            current_time = self.format_time(position)
            total_time = self.format_time(duration)
            self.progress_bar.setFormat(f"{current_time} / {total_time}")
            
    def set_duration(self, duration):
        """تعيين مدة الفيديو"""
        self.progress_bar.setMaximum(100)
        
    def format_time(self, milliseconds):
        """تنسيق الوقت"""
        seconds = milliseconds // 1000
        minutes = seconds // 60
        seconds = seconds % 60
        return f"{minutes:02d}:{seconds:02d}"
        
    def start_exam(self):
        """بدء الامتحان"""
        if not self.video_finished:
            QMessageBox.warning(self, "تحذير", "يجب انتهاء الفيديو أولاً قبل بدء الامتحان!")
            return
            
        if self.exam_started:
            QMessageBox.information(self, "معلومات", "الامتحان قيد التقدم بالفعل")
            return
            
        self.exam_started = True
        self.exam_btn.setText("📝 الامتحان قيد التقدم...")
        self.exam_btn.setEnabled(False)
        
        # إخفاء مشغل الفيديو وعرض الامتحان
        self.show_exam()
        
    def show_exam(self):
        """عرض الامتحان"""
        exam_dialog = ExamDialog(self.exam_questions, self.student_info, self.video_info)
        exam_dialog.exam_completed.connect(self.on_exam_completed)
        
        # تشغيل الامتحان في وضع شاشة كاملة
        exam_dialog.showFullScreen()
        result = exam_dialog.exec_()
        
    def on_exam_completed(self, exam_result):
        """عند انتهاء الامتحان"""
        # حفظ نتيجة الامتحان
        self.save_exam_result(exam_result)
        
        # تفعيل زر الخروج
        self.exit_btn.setEnabled(True)
        self.exit_btn.setStyleSheet("background-color: #e74c3c;")
        
        # عرض النتيجة
        score = exam_result['score']
        total = exam_result['total_questions']
        percentage = (score / total) * 100
        
        QMessageBox.information(self, "نتيجة الامتحان", 
                               f"🎉 تم انتهاء الامتحان!\n\n"
                               f"📊 النتيجة: {score} من {total}\n"
                               f"📈 النسبة المئوية: {percentage:.1f}%\n"
                               f"🏆 التقدير: {self.get_grade(percentage)}\n\n"
                               f"✅ تم حفظ النتيجة وإرسالها للمعلم")
        
        # إرسال إشارة انتهاء الامتحان
        self.exam_completed.emit(exam_result)
        
    def get_grade(self, percentage):
        """الحصول على التقدير"""
        if percentage >= 90:
            return "ممتاز"
        elif percentage >= 80:
            return "جيد جداً"
        elif percentage >= 70:
            return "جيد"
        elif percentage >= 60:
            return "مقبول"
        else:
            return "ضعيف"
            
    def save_exam_result(self, exam_result):
        """حفظ نتيجة الامتحان"""
        try:
            result_data = {
                'student_info': self.student_info,
                'video_info': self.video_info,
                'exam_result': exam_result,
                'completion_date': datetime.now().isoformat()
            }
            
            # حفظ في ملف JSON
            results_file = f"exam_result_{self.student_info['id']}_{self.video_info['id']}_{int(time.time())}.json"
            with open(results_file, 'w', encoding='utf-8') as f:
                json.dump(result_data, f, ensure_ascii=False, indent=2)
                
            print(f"✅ تم حفظ نتيجة الامتحان: {results_file}")
            
        except Exception as e:
            print(f"❌ خطأ في حفظ نتيجة الامتحان: {e}")
            
    def get_watch_count(self):
        """الحصول على عدد المشاهدات"""
        try:
            watch_file = f"watch_data_{self.student_info['id']}_{self.video_info['id']}.json"
            if os.path.exists(watch_file):
                with open(watch_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    return data.get('watch_count', 0)
            return 0
        except:
            return 0
            
    def save_watch_count(self):
        """حفظ عداد المشاهدات"""
        try:
            watch_data = {
                'student_id': self.student_info['id'],
                'video_id': self.video_info['id'],
                'watch_count': self.watch_count,
                'last_watch': datetime.now().isoformat()
            }
            
            watch_file = f"watch_data_{self.student_info['id']}_{self.video_info['id']}.json"
            with open(watch_file, 'w', encoding='utf-8') as f:
                json.dump(watch_data, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            print(f"خطأ في حفظ عداد المشاهدات: {e}")
            
    def safe_exit(self):
        """خروج آمن"""
        if not self.video_finished:
            QMessageBox.warning(self, "تحذير", "لا يمكن الخروج قبل انتهاء الفيديو!")
            return
            
        if not self.exam_started:
            QMessageBox.warning(self, "تحذير", "يجب أداء الامتحان قبل الخروج!")
            return
            
        self.accept()
        
    def keyPressEvent(self, event):
        """منع مفاتيح معينة"""
        blocked_keys = [
            Qt.Key_Escape,  # منع Escape
            Qt.Key_F11,     # منع F11
            Qt.Key_Alt,     # منع Alt
            Qt.Key_Tab,     # منع Tab
        ]
        
        if event.key() in blocked_keys:
            event.ignore()
            QMessageBox.warning(self, "تحذير", "هذا المفتاح محظور أثناء مشاهدة الفيديو!")
        else:
            super().keyPressEvent(event)
            
    def closeEvent(self, event):
        """منع إغلاق النافذة"""
        if not self.video_finished or not self.exam_started:
            event.ignore()
            QMessageBox.warning(self, "تحذير", "لا يمكن إغلاق النافذة قبل انتهاء الفيديو والامتحان!")
        else:
            event.accept()

class ExamDialog(QDialog):
    """نافذة الامتحان"""
    
    exam_completed = pyqtSignal(dict)
    
    def __init__(self, questions, student_info, video_info):
        super().__init__()
        self.questions = questions
        self.student_info = student_info
        self.video_info = video_info
        self.current_question = 0
        self.answers = {}
        self.start_time = datetime.now()
        
        self.init_ui()
        self.show_question()
        
        # مؤقت الامتحان (30 دقيقة)
        self.exam_timer = QTimer()
        self.exam_timer.timeout.connect(self.time_up)
        self.exam_timer.start(30 * 60 * 1000)  # 30 دقيقة
        
    def init_ui(self):
        """إعداد واجهة الامتحان"""
        self.setWindowTitle(f"امتحان: {self.video_info['title']}")
        self.setModal(True)
        
        # منع إغلاق النافذة
        self.setWindowFlags(Qt.Window | Qt.WindowStaysOnTopHint | Qt.FramelessWindowHint)
        
        self.setStyleSheet("""
            QDialog {
                background-color: #f8f9fa;
                font-family: Arial;
            }
            QLabel {
                color: #2c3e50;
                font-size: 16px;
            }
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 12px;
                border-radius: 6px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
            QRadioButton {
                font-size: 14px;
                padding: 8px;
            }
        """)
        
        layout = QVBoxLayout()
        
        # رأس الامتحان
        header = self.create_exam_header()
        layout.addWidget(header)
        
        # منطقة السؤال
        self.question_area = QScrollArea()
        self.question_widget = QWidget()
        self.question_layout = QVBoxLayout()
        self.question_widget.setLayout(self.question_layout)
        self.question_area.setWidget(self.question_widget)
        self.question_area.setWidgetResizable(True)
        layout.addWidget(self.question_area)
        
        # أزرار التنقل
        nav_layout = QHBoxLayout()
        
        self.prev_btn = QPushButton("⬅️ السابق")
        self.prev_btn.clicked.connect(self.previous_question)
        nav_layout.addWidget(self.prev_btn)
        
        self.question_label = QLabel()
        self.question_label.setAlignment(Qt.AlignCenter)
        nav_layout.addWidget(self.question_label)
        
        self.next_btn = QPushButton("➡️ التالي")
        self.next_btn.clicked.connect(self.next_question)
        nav_layout.addWidget(self.next_btn)
        
        layout.addLayout(nav_layout)
        
        # زر إنهاء الامتحان
        self.finish_btn = QPushButton("✅ إنهاء الامتحان")
        self.finish_btn.clicked.connect(self.finish_exam)
        layout.addWidget(self.finish_btn)
        
        self.setLayout(layout)
        
    def create_exam_header(self):
        """إنشاء رأس الامتحان"""
        frame = QFrame()
        frame.setStyleSheet("background-color: #2c3e50; color: white; padding: 15px; border-radius: 8px;")
        layout = QVBoxLayout()
        
        title = QLabel(f"📝 امتحان: {self.video_info['title']}")
        title.setStyleSheet("font-size: 20px; font-weight: bold;")
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)
        
        info = QLabel(f"👤 الطالب: {self.student_info['name']} | "
                     f"📚 المادة: {self.video_info['subject']} | "
                     f"⏱️ المدة: 30 دقيقة")
        info.setAlignment(Qt.AlignCenter)
        layout.addWidget(info)
        
        frame.setLayout(layout)
        return frame
        
    def show_question(self):
        """عرض السؤال الحالي"""
        # مسح المحتوى السابق
        for i in reversed(range(self.question_layout.count())):
            self.question_layout.itemAt(i).widget().setParent(None)
            
        if self.current_question >= len(self.questions):
            return
            
        question = self.questions[self.current_question]
        
        # نص السؤال
        question_text = QLabel(f"السؤال {self.current_question + 1}: {question['question']}")
        question_text.setStyleSheet("font-size: 18px; font-weight: bold; margin: 15px; padding: 10px; background-color: white; border-radius: 5px;")
        question_text.setWordWrap(True)
        self.question_layout.addWidget(question_text)
        
        # خيارات الإجابة
        if question['type'] == 'multiple_choice':
            self.show_multiple_choice(question)
        elif question['type'] == 'true_false':
            self.show_true_false(question)
            
        # تحديث التنقل
        self.update_navigation()
        
    def show_multiple_choice(self, question):
        """عرض سؤال اختيار متعدد"""
        self.answer_group = QButtonGroup()
        
        for i, option in enumerate(question['options']):
            radio = QRadioButton(f"{chr(65+i)}. {option}")
            radio.setStyleSheet("padding: 8px; margin: 5px; background-color: white; border-radius: 3px;")
            self.answer_group.addButton(radio, i)
            self.question_layout.addWidget(radio)
            
            # استرجاع الإجابة المحفوظة
            if self.current_question in self.answers:
                if self.answers[self.current_question] == i:
                    radio.setChecked(True)
                    
        self.answer_group.buttonClicked.connect(self.save_answer)
        
    def show_true_false(self, question):
        """عرض سؤال صح/خطأ"""
        self.answer_group = QButtonGroup()
        
        true_radio = QRadioButton("أ. صح")
        false_radio = QRadioButton("ب. خطأ")
        
        true_radio.setStyleSheet("padding: 8px; margin: 5px; background-color: white; border-radius: 3px;")
        false_radio.setStyleSheet("padding: 8px; margin: 5px; background-color: white; border-radius: 3px;")
        
        self.answer_group.addButton(true_radio, 1)
        self.answer_group.addButton(false_radio, 0)
        
        self.question_layout.addWidget(true_radio)
        self.question_layout.addWidget(false_radio)
        
        # استرجاع الإجابة المحفوظة
        if self.current_question in self.answers:
            if self.answers[self.current_question]:
                true_radio.setChecked(True)
            else:
                false_radio.setChecked(True)
                
        self.answer_group.buttonClicked.connect(self.save_answer)
        
    def save_answer(self):
        """حفظ الإجابة"""
        if hasattr(self, 'answer_group'):
            checked_button = self.answer_group.checkedButton()
            if checked_button:
                answer_id = self.answer_group.id(checked_button)
                self.answers[self.current_question] = answer_id
                
    def previous_question(self):
        """السؤال السابق"""
        if self.current_question > 0:
            self.current_question -= 1
            self.show_question()
            
    def next_question(self):
        """السؤال التالي"""
        if self.current_question < len(self.questions) - 1:
            self.current_question += 1
            self.show_question()
            
    def update_navigation(self):
        """تحديث أزرار التنقل"""
        self.prev_btn.setEnabled(self.current_question > 0)
        self.next_btn.setEnabled(self.current_question < len(self.questions) - 1)
        self.question_label.setText(f"السؤال {self.current_question + 1} من {len(self.questions)}")
        
    def finish_exam(self):
        """إنهاء الامتحان"""
        # التأكد من الإجابة على جميع الأسئلة
        unanswered = []
        for i in range(len(self.questions)):
            if i not in self.answers:
                unanswered.append(i + 1)
                
        if unanswered:
            reply = QMessageBox.question(self, "أسئلة غير مجابة", 
                                       f"لم تجب على الأسئلة: {', '.join(map(str, unanswered))}\n"
                                       "هل تريد إنهاء الامتحان؟",
                                       QMessageBox.Yes | QMessageBox.No)
            if reply == QMessageBox.No:
                return
                
        # حساب النتيجة
        score = self.calculate_score()
        
        # إنهاء الامتحان
        end_time = datetime.now()
        duration = end_time - self.start_time
        
        result = {
            'student_info': self.student_info,
            'video_info': self.video_info,
            'answers': self.answers,
            'score': score,
            'total_questions': len(self.questions),
            'start_time': self.start_time.isoformat(),
            'end_time': end_time.isoformat(),
            'duration': str(duration)
        }
        
        self.exam_completed.emit(result)
        self.accept()
        
    def calculate_score(self):
        """حساب النتيجة"""
        correct_answers = 0
        
        for i, question in enumerate(self.questions):
            if i in self.answers:
                user_answer = self.answers[i]
                correct_answer = question['correct_answer']
                
                if question['type'] == 'multiple_choice':
                    if user_answer == correct_answer:
                        correct_answers += 1
                elif question['type'] == 'true_false':
                    if (user_answer == 1) == correct_answer:
                        correct_answers += 1
                        
        return correct_answers
        
    def time_up(self):
        """انتهاء الوقت"""
        QMessageBox.warning(self, "انتهى الوقت", "انتهى وقت الامتحان!\nسيتم إنهاء الامتحان تلقائياً")
        self.finish_exam()
        
    def keyPressEvent(self, event):
        """منع مفاتيح معينة"""
        blocked_keys = [Qt.Key_Escape, Qt.Key_F11, Qt.Key_Alt, Qt.Key_Tab]
        
        if event.key() in blocked_keys:
            event.ignore()
        else:
            super().keyPressEvent(event)
            
    def closeEvent(self, event):
        """منع إغلاق النافذة"""
        event.ignore()
        QMessageBox.warning(self, "تحذير", "لا يمكن إغلاق الامتحان!\nيجب إنهاء الامتحان أولاً")
