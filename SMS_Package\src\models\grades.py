# -*- coding: utf-8 -*-
"""
نموذج الدرجات
Grades Model
"""

from datetime import datetime, date
from typing import Optional, List, Dict, Any
from ..database.database_manager import DatabaseManager

class Grades:
    """نموذج بيانات الدرجات"""
    
    def __init__(self, db_manager: DatabaseManager):
        self.db = db_manager
    
    def add_grade(self, student_id: int, subject: str, exam_type: str, 
                  score: float, max_score: float, exam_date: date = None, 
                  notes: str = "") -> Optional[int]:
        """
        إضافة درجة جديدة
        
        Args:
            student_id: معرف الطالب
            subject: المادة (جغرافيا أو تاريخ)
            exam_type: نوع الامتحان
            score: الدرجة المحصلة
            max_score: الدرجة الكاملة
            exam_date: تاريخ الامتحان
            notes: ملاحظات
            
        Returns:
            معرف السجل الجديد أو None في حالة الفشل
        """
        try:
            if exam_date is None:
                exam_date = date.today()
            
            cursor = self.db.execute_query("""
                INSERT INTO grades (
                    student_id, subject, exam_type, score, max_score, 
                    exam_date, notes
                ) VALUES (?, ?, ?, ?, ?, ?, ?)
            """, (student_id, subject, exam_type, score, max_score, exam_date, notes))
            
            # تحديث الدرجة في جدول الطلاب
            self.update_student_total_score(student_id, subject)
            
            return cursor.lastrowid
            
        except Exception as e:
            print(f"خطأ في إضافة الدرجة: {e}")
            return None
    
    def update_grade(self, grade_id: int, score: float, max_score: float = None, 
                    notes: str = None) -> bool:
        """تحديث درجة موجودة"""
        try:
            # جلب بيانات الدرجة الحالية
            current_grade = self.db.fetch_one(
                "SELECT * FROM grades WHERE id = ?", (grade_id,)
            )
            
            if not current_grade:
                return False
            
            # بناء استعلام التحديث
            update_fields = ["score = ?"]
            params = [score]
            
            if max_score is not None:
                update_fields.append("max_score = ?")
                params.append(max_score)
            
            if notes is not None:
                update_fields.append("notes = ?")
                params.append(notes)
            
            params.append(grade_id)
            
            query = f"UPDATE grades SET {', '.join(update_fields)} WHERE id = ?"
            self.db.execute_query(query, tuple(params))
            
            # تحديث الدرجة الإجمالية في جدول الطلاب
            self.update_student_total_score(current_grade['student_id'], current_grade['subject'])
            
            return True
            
        except Exception as e:
            print(f"خطأ في تحديث الدرجة: {e}")
            return False
    
    def delete_grade(self, grade_id: int) -> bool:
        """حذف درجة"""
        try:
            # جلب بيانات الدرجة قبل الحذف
            grade = self.db.fetch_one("SELECT * FROM grades WHERE id = ?", (grade_id,))
            
            if not grade:
                return False
            
            # حذف الدرجة
            self.db.execute_query("DELETE FROM grades WHERE id = ?", (grade_id,))
            
            # تحديث الدرجة الإجمالية
            self.update_student_total_score(grade['student_id'], grade['subject'])
            
            return True
            
        except Exception as e:
            print(f"خطأ في حذف الدرجة: {e}")
            return False
    
    def get_student_grades(self, student_id: int, subject: str = None) -> List[Dict[str, Any]]:
        """جلب درجات طالب معين"""
        query = """
            SELECT g.*, s.full_name, s.student_code
            FROM grades g
            JOIN students s ON g.student_id = s.id
            WHERE g.student_id = ?
        """
        params = [student_id]
        
        if subject:
            query += " AND g.subject = ?"
            params.append(subject)
        
        query += " ORDER BY g.exam_date DESC, g.subject"
        
        return self.db.fetch_all(query, tuple(params))
    
    def get_subject_grades(self, subject: str, grade_class: str = None) -> List[Dict[str, Any]]:
        """جلب درجات مادة معينة"""
        query = """
            SELECT g.*, s.full_name, s.student_code, s.grade, s.stage
            FROM grades g
            JOIN students s ON g.student_id = s.id
            WHERE g.subject = ?
        """
        params = [subject]
        
        if grade_class:
            query += " AND s.grade = ?"
            params.append(grade_class)
        
        query += " ORDER BY s.grade, s.full_name, g.exam_date DESC"
        
        return self.db.fetch_all(query, tuple(params))
    
    def get_class_grades(self, grade_class: str) -> List[Dict[str, Any]]:
        """جلب درجات صف معين"""
        return self.db.fetch_all("""
            SELECT s.id, s.student_code, s.full_name, s.geography_score, s.history_score,
                   AVG(CASE WHEN g.subject = 'جغرافيا' THEN (g.score/g.max_score)*100 END) as geography_avg,
                   AVG(CASE WHEN g.subject = 'تاريخ' THEN (g.score/g.max_score)*100 END) as history_avg
            FROM students s
            LEFT JOIN grades g ON s.id = g.student_id
            WHERE s.grade = ?
            GROUP BY s.id, s.student_code, s.full_name, s.geography_score, s.history_score
            ORDER BY s.full_name
        """, (grade_class,))
    
    def update_student_total_score(self, student_id: int, subject: str):
        """تحديث الدرجة الإجمالية للطالب في مادة معينة"""
        try:
            # حساب متوسط الدرجات للمادة
            avg_score = self.db.fetch_one("""
                SELECT AVG((score/max_score)*100) as average
                FROM grades 
                WHERE student_id = ? AND subject = ?
            """, (student_id, subject))
            
            average = avg_score['average'] if avg_score and avg_score['average'] else 0
            
            # تحديث الدرجة في جدول الطلاب
            if subject == 'جغرافيا':
                self.db.execute_query("""
                    UPDATE students 
                    SET geography_score = ?, updated_at = CURRENT_TIMESTAMP 
                    WHERE id = ?
                """, (average, student_id))
            elif subject == 'تاريخ':
                self.db.execute_query("""
                    UPDATE students 
                    SET history_score = ?, updated_at = CURRENT_TIMESTAMP 
                    WHERE id = ?
                """, (average, student_id))
            
        except Exception as e:
            print(f"خطأ في تحديث الدرجة الإجمالية: {e}")
    
    def get_grade_statistics(self, subject: str = None, grade_class: str = None) -> Dict[str, Any]:
        """جلب إحصائيات الدرجات"""
        query = """
            SELECT 
                COUNT(*) as total_grades,
                AVG((score/max_score)*100) as average_percentage,
                MIN((score/max_score)*100) as min_percentage,
                MAX((score/max_score)*100) as max_percentage,
                subject
            FROM grades g
            JOIN students s ON g.student_id = s.id
            WHERE 1=1
        """
        params = []
        
        if subject:
            query += " AND g.subject = ?"
            params.append(subject)
        
        if grade_class:
            query += " AND s.grade = ?"
            params.append(grade_class)
        
        if not subject:
            query += " GROUP BY g.subject"
        
        return self.db.fetch_all(query, tuple(params))
    
    def get_top_students(self, subject: str, limit: int = 10) -> List[Dict[str, Any]]:
        """جلب أفضل الطلاب في مادة معينة"""
        score_field = 'geography_score' if subject == 'جغرافيا' else 'history_score'
        
        return self.db.fetch_all(f"""
            SELECT student_code, full_name, grade, stage, {score_field} as score
            FROM students 
            WHERE {score_field} > 0
            ORDER BY {score_field} DESC
            LIMIT ?
        """, (limit,))
    
    def get_failing_students(self, subject: str, passing_grade: float = 50) -> List[Dict[str, Any]]:
        """جلب الطلاب الراسبين في مادة معينة"""
        score_field = 'geography_score' if subject == 'جغرافيا' else 'history_score'
        
        return self.db.fetch_all(f"""
            SELECT student_code, full_name, grade, stage, {score_field} as score
            FROM students 
            WHERE {score_field} < ? AND {score_field} > 0
            ORDER BY {score_field} ASC
        """, (passing_grade,))
    
    def get_exam_types(self) -> List[str]:
        """جلب أنواع الامتحانات المستخدمة"""
        result = self.db.fetch_all("SELECT DISTINCT exam_type FROM grades ORDER BY exam_type")
        return [row['exam_type'] for row in result]
