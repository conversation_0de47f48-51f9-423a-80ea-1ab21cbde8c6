# متطلبات نظام QR Code للحضور الذكي
# QR Code Smart Attendance System Requirements

# المكتبات الأساسية للنظام
PyQt5>=5.15.0
sqlite3

# مكتبات QR Code
qrcode[pil]>=7.4.2
pyzbar>=0.1.9

# مكتبات التشفير والأمان
cryptography>=41.0.0

# مكتبات معالجة الصور
Pillow>=10.0.0
opencv-python>=4.8.0

# مكتبات إضافية للتقارير (اختيارية)
reportlab>=4.0.0
openpyxl>=3.1.0
pandas>=2.0.0

# مكتبات النظام
datetime
json
base64
hashlib
hmac
os
io

# ملاحظات التثبيت:
# pip install -r requirements_qr.txt
# 
# في حالة مشاكل opencv-python على Windows:
# pip install opencv-python-headless
#
# في حالة مشاكل pyzbar:
# Windows: تحتاج تثبيت Visual C++ Redistributable
# Linux: sudo apt-get install libzbar0
# macOS: brew install zbar
