========================================
  Student Management System
  Mr. <PERSON>
  Geography and History Teacher
========================================

QUICK START GUIDE:

Option 1: Build EXE File (RECOMMENDED)
--------------------------------------
1. Double-click: build_simple.bat
2. Wait for the build to complete (few minutes)
3. Find StudentManagementSystem.exe in 'dist' folder
4. Double-click the EXE to run

Option 2: Quick Start Menu
--------------------------
1. Double-click: QUICK_START.bat
2. Choose option 2 to build EXE
3. Then choose option 3 to run

Option 3: Direct Run (if Python installed)
-------------------------------------------
1. Double-click: RUN_SIMPLE.bat
2. Application will start automatically

LOGIN CREDENTIALS:
------------------
Username: admin
Password: admin123

FEATURES:
---------
✓ Student Management
✓ Attendance Tracking
✓ Grade Management (Geography & History)
✓ Professional Reports (PDF/Excel)
✓ Arabic Interface
✓ Data Backup System

TROUBLESHOOTING:
----------------
- If build fails: Install Python from python.org
- If app doesn't start: Check logs folder
- For help: Read USER_GUIDE.md

IMPORTANT FILES:
----------------
- build_simple.bat: Build EXE file
- QUICK_START.bat: Quick start menu
- RUN_SIMPLE.bat: Direct run
- USER_GUIDE.md: Complete user guide

AFTER BUILDING EXE:
-------------------
The 'dist' folder will contain:
- StudentManagementSystem.exe (main application)
- README.txt (instructions)
- Required folders (logs, backups, exports)

You can copy the entire 'dist' folder to any Windows
computer and run the EXE file directly!

========================================
Developed specifically for Mr. Ahmed Adel
Geography and History Teacher
========================================
