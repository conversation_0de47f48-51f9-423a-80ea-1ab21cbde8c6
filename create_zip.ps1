# PowerShell script to compress the Student Management System project

Write-Host "=== Student Management System - Project Compression ===" -ForegroundColor Cyan
Write-Host ""

# Create timestamp for filename
$timestamp = Get-Date -Format "yyyyMMdd_HHmm"
$zipName = "StudentManagementSystem_$timestamp.zip"

Write-Host "Creating ZIP file: $zipName" -ForegroundColor Yellow
Write-Host ""

# Define files to include
$files = @(
    "main.py",
    "run.py", 
    "launch.py",
    "config.py",
    "requirements.txt",
    "run_app.bat",
    "start.bat",
    "build.bat",
    "zip_project.bat",
    "fix_imports.py",
    "test_app.py",
    "build_exe.py",
    "compress_project.py",
    "README.md",
    "README_FINAL.md",
    "USER_GUIDE.md",
    "INSTALLATION.md",
    "PROJECT_SUMMARY.md"
)

# Define directories to include
$dirs = @("src", "data", "assets")

# Create temporary directory
$tempDir = "temp_for_zip"
if (Test-Path $tempDir) {
    Remove-Item $tempDir -Recurse -Force
}
New-Item -ItemType Directory -Path $tempDir | Out-Null

Write-Host "Copying files..." -ForegroundColor Green

# Copy individual files
foreach ($file in $files) {
    if (Test-Path $file) {
        Copy-Item $file $tempDir
        Write-Host "  ✓ $file" -ForegroundColor Green
    } else {
        Write-Host "  ⚠ $file (not found)" -ForegroundColor Yellow
    }
}

Write-Host ""
Write-Host "Copying directories..." -ForegroundColor Green

# Copy directories
foreach ($dir in $dirs) {
    if (Test-Path $dir) {
        Copy-Item $dir $tempDir -Recurse
        Write-Host "  ✓ $dir/" -ForegroundColor Green
    } else {
        Write-Host "  ⚠ $dir/ (not found)" -ForegroundColor Yellow
    }
}

Write-Host ""
Write-Host "Creating empty directories..." -ForegroundColor Green

# Create empty directories with .gitkeep files
$emptyDirs = @("logs", "backups", "exports", "temp")
foreach ($dir in $emptyDirs) {
    $dirPath = Join-Path $tempDir $dir
    New-Item -ItemType Directory -Path $dirPath -Force | Out-Null
    New-Item -ItemType File -Path (Join-Path $dirPath ".gitkeep") -Force | Out-Null
    Write-Host "  ✓ $dir/" -ForegroundColor Green
}

Write-Host ""
Write-Host "Creating project info file..." -ForegroundColor Green

# Create project information file
$projectInfo = @"
# Student Management System - نظام إدارة الطلاب
## For Mr. Ahmed Adel - لمستر أحمد عادل

**Created:** $(Get-Date -Format "yyyy-MM-dd HH:mm:ss")
**Version:** 1.0.0

## Quick Start - البدء السريع

### Method 1 - الطريقة الأولى (Recommended - مُوصى به):
1. Extract all files to a folder - استخرج جميع الملفات في مجلد
2. Double-click `run_app.bat` - انقر مرتين على `run_app.bat`
3. Choose option 1 for normal run - اختر الخيار 1 للتشغيل العادي

### Method 2 - الطريقة الثانية:
1. Open command prompt in the folder - افتح سطر الأوامر في المجلد
2. Run: `python main.py` - شغل: `python main.py`

## Login Credentials - بيانات تسجيل الدخول:
- **Username - اسم المستخدم:** admin
- **Password - كلمة المرور:** admin123

## Important Files - الملفات المهمة:
- `run_app.bat` - Main launcher - ملف التشغيل الرئيسي
- `USER_GUIDE.md` - Complete user guide - دليل المستخدم الكامل
- `INSTALLATION.md` - Installation guide - دليل التثبيت المفصل
- `PROJECT_SUMMARY.md` - Project summary - ملخص المشروع

## Troubleshooting - حل المشاكل:
If you encounter issues - في حالة وجود مشاكل:
1. Run `run_app.bat` and choose option 4 - شغل `run_app.bat` واختر الخيار 4
2. Or run `python fix_imports.py` - أو شغل `python fix_imports.py`
3. Check the `logs/` folder for error details - تحقق من مجلد `logs/` لتفاصيل الأخطاء

## Features - المميزات:
- ✅ Student management - إدارة الطلاب
- ✅ Attendance tracking - تتبع الحضور
- ✅ Grade management - إدارة الدرجات
- ✅ Report generation - إنشاء التقارير
- ✅ Arabic interface - واجهة عربية
- ✅ Data backup - نسخ احتياطي للبيانات

## Developer - المطور:
Created specifically for Mr. Ahmed Adel
تم التطوير خصيصاً لمستر أحمد عادل

Geography and History Teacher
معلم الجغرافيا والتاريخ

---
**Enjoy using the Student Management System! 🎓**
**استمتع باستخدام نظام إدارة الطلاب! 🎓**
"@

$projectInfo | Out-File -FilePath (Join-Path $tempDir "PROJECT_INFO.txt") -Encoding UTF8
Write-Host "  ✓ PROJECT_INFO.txt" -ForegroundColor Green

Write-Host ""
Write-Host "Compressing files..." -ForegroundColor Magenta

# Create the ZIP file
try {
    Compress-Archive -Path (Join-Path $tempDir "*") -DestinationPath $zipName -Force
    Write-Host "  ✓ Compression completed" -ForegroundColor Green
} catch {
    Write-Host "  ✗ Compression failed: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Clean up temporary directory
Remove-Item $tempDir -Recurse -Force

# Check if ZIP file was created successfully
if (Test-Path $zipName) {
    $fileSize = (Get-Item $zipName).Length / 1MB
    $currentLocation = Get-Location
    
    Write-Host ""
    Write-Host "=== SUCCESS! ===" -ForegroundColor Green -BackgroundColor Black
    Write-Host ""
    Write-Host "📦 ZIP file created successfully!" -ForegroundColor Green
    Write-Host "📁 File name: $zipName" -ForegroundColor Cyan
    Write-Host "📏 File size: $([math]::Round($fileSize, 2)) MB" -ForegroundColor Cyan
    Write-Host "📍 Location: $currentLocation" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "🎓 The Student Management System is ready for distribution!" -ForegroundColor Green
    Write-Host ""
    Write-Host "📋 Instructions:" -ForegroundColor Yellow
    Write-Host "1. Save the ZIP file in a safe location" -ForegroundColor White
    Write-Host "2. Copy it to any Windows computer" -ForegroundColor White
    Write-Host "3. Extract the files and run run_app.bat" -ForegroundColor White
    Write-Host "4. Use admin/admin123 to login" -ForegroundColor White
    Write-Host ""
    
    # Try to open file explorer to show the ZIP file
    try {
        Start-Process explorer -ArgumentList "/select,`"$zipName`""
        Write-Host "📂 File explorer opened to show the ZIP file" -ForegroundColor Green
    } catch {
        Write-Host "📂 ZIP file location: $((Get-Location).Path)\$zipName" -ForegroundColor Yellow
    }
    
} else {
    Write-Host ""
    Write-Host "=== ERROR ===" -ForegroundColor Red -BackgroundColor Black
    Write-Host "❌ Failed to create ZIP file" -ForegroundColor Red
    Write-Host "Please try manual compression or check for errors above." -ForegroundColor Yellow
    exit 1
}

Write-Host ""
Write-Host "Press any key to exit..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
