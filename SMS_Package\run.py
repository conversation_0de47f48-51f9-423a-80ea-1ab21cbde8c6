#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ملف تشغيل التطبيق المحسن
Enhanced Application Launcher

هذا الملف يوفر طريقة محسنة لتشغيل التطبيق مع معالجة الأخطاء
والتحقق من المتطلبات
"""

import sys
import os
import traceback
from pathlib import Path

def check_python_version():
    """التحقق من إصدار Python"""
    if sys.version_info < (3, 6):
        print("❌ خطأ: يتطلب التطبيق Python 3.6 أو أحدث")
        print(f"الإصدار الحالي: {sys.version}")
        return False
    return True

def check_dependencies():
    """التحقق من المكتبات المطلوبة"""
    required_packages = [
        ('PyQt5', 'PyQt5'),
        ('sqlite3', 'sqlite3'),
        ('datetime', 'datetime'),
        ('pathlib', 'pathlib')
    ]
    
    missing_packages = []
    
    for package_name, import_name in required_packages:
        try:
            __import__(import_name)
            print(f"✅ {package_name}: موجود")
        except ImportError:
            missing_packages.append(package_name)
            print(f"❌ {package_name}: غير موجود")
    
    if missing_packages:
        print(f"\n❌ المكتبات المفقودة: {', '.join(missing_packages)}")
        print("يرجى تثبيت المكتبات المطلوبة باستخدام:")
        print("pip install -r requirements.txt")
        return False
    
    return True

def setup_environment():
    """إعداد البيئة"""
    # إضافة مسار المشروع إلى sys.path
    project_root = Path(__file__).parent
    if str(project_root) not in sys.path:
        sys.path.insert(0, str(project_root))
    
    # إنشاء المجلدات المطلوبة
    required_dirs = ['data', 'logs', 'backups', 'exports']
    for dir_name in required_dirs:
        dir_path = project_root / dir_name
        dir_path.mkdir(exist_ok=True)
    
    print("✅ تم إعداد البيئة بنجاح")

def create_desktop_shortcut():
    """إنشاء اختصار على سطح المكتب (Windows)"""
    try:
        if sys.platform == 'win32':
            import winshell
            from win32com.client import Dispatch
            
            desktop = winshell.desktop()
            path = os.path.join(desktop, "نظام إدارة الطلاب.lnk")
            target = sys.executable
            wDir = os.path.dirname(os.path.abspath(__file__))
            icon = os.path.join(wDir, "assets", "icon.ico")
            
            shell = Dispatch('WScript.Shell')
            shortcut = shell.CreateShortCut(path)
            shortcut.Targetpath = target
            shortcut.Arguments = f'"{os.path.abspath(__file__)}"'
            shortcut.WorkingDirectory = wDir
            shortcut.IconLocation = icon if os.path.exists(icon) else target
            shortcut.save()
            
            print("✅ تم إنشاء اختصار على سطح المكتب")
    except ImportError:
        print("ℹ️ لإنشاء اختصار سطح المكتب، قم بتثبيت: pip install winshell pywin32")
    except Exception as e:
        print(f"⚠️ فشل في إنشاء اختصار سطح المكتب: {e}")

def main():
    """الدالة الرئيسية"""
    print("🚀 بدء تشغيل نظام إدارة الطلاب لمستر أحمد عادل")
    print("=" * 50)
    
    # التحقق من إصدار Python
    if not check_python_version():
        input("اضغط Enter للخروج...")
        return
    
    print("\n📋 التحقق من المتطلبات:")
    # التحقق من المكتبات
    if not check_dependencies():
        input("اضغط Enter للخروج...")
        return
    
    print("\n⚙️ إعداد البيئة:")
    # إعداد البيئة
    setup_environment()
    
    try:
        print("\n🎯 تشغيل التطبيق...")
        
        # استيراد وتشغيل التطبيق
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import Qt
        from PyQt5.QtGui import QFont, QIcon
        
        # إضافة مسار المجلدات للاستيراد
        sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))
        
        from src.ui.login_window import LoginWindow
        from src.database.database_manager import DatabaseManager
        
        # إنشاء التطبيق
        app = QApplication(sys.argv)
        
        # إعداد خصائص التطبيق
        app.setApplicationName("نظام إدارة الطلاب")
        app.setApplicationVersion("1.0")
        app.setOrganizationName("مستر أحمد عادل")
        
        # إعداد الأيقونة
        icon_path = os.path.join(os.path.dirname(__file__), 'assets', 'icon.ico')
        if os.path.exists(icon_path):
            app.setWindowIcon(QIcon(icon_path))
        
        # إعداد الخط العربي
        font = QFont("Segoe UI", 10)
        app.setFont(font)
        
        # إعداد اتجاه النص للعربية
        app.setLayoutDirection(Qt.RightToLeft)
        
        # تهيئة قاعدة البيانات
        print("📊 تهيئة قاعدة البيانات...")
        db_manager = DatabaseManager()
        db_manager.initialize_database()
        
        # إنشاء وعرض نافذة تسجيل الدخول
        print("🔐 فتح نافذة تسجيل الدخول...")
        login_window = LoginWindow()
        login_window.show()
        
        print("✅ تم تشغيل التطبيق بنجاح!")
        print("💡 نصيحة: يمكنك إنشاء اختصار على سطح المكتب بتشغيل هذا الملف مع المعامل --create-shortcut")
        
        # تشغيل التطبيق
        sys.exit(app.exec_())
        
    except ImportError as e:
        print(f"❌ خطأ في الاستيراد: {e}")
        print("تأكد من تثبيت جميع المكتبات المطلوبة")
        traceback.print_exc()
    except Exception as e:
        print(f"❌ خطأ غير متوقع: {e}")
        traceback.print_exc()
    finally:
        input("اضغط Enter للخروج...")

if __name__ == "__main__":
    # التحقق من المعاملات
    if len(sys.argv) > 1 and sys.argv[1] == "--create-shortcut":
        create_desktop_shortcut()
    else:
        main()
