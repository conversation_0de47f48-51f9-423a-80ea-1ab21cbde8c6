@echo off
chcp 65001 >nul
title بناء نظام إدارة الطلاب - EXE Builder

echo.
echo ========================================
echo    بناء نظام إدارة الطلاب
echo    تحويل إلى ملف EXE
echo ========================================
echo.

REM التحقق من وجود Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ خطأ: Python غير مثبت على النظام
    echo يرجى تحميل وتثبيت Python من: https://python.org
    echo.
    pause
    exit /b 1
)

echo ✅ تم العثور على Python
echo.

REM التحقق من وجود pip
pip --version >nul 2>&1
if errorlevel 1 (
    echo ❌ خطأ: pip غير متوفر
    echo.
    pause
    exit /b 1
)

echo ✅ تم العثور على pip
echo.

REM تثبيت PyInstaller إذا لم يكن موجوداً
echo 📦 التحقق من PyInstaller...
python -c "import PyInstaller" 2>nul
if errorlevel 1 (
    echo 📥 تثبيت PyInstaller...
    pip install pyinstaller
    if errorlevel 1 (
        echo ❌ فشل في تثبيت PyInstaller
        echo.
        pause
        exit /b 1
    )
    echo ✅ تم تثبيت PyInstaller بنجاح
) else (
    echo ✅ PyInstaller موجود
)

echo.

REM تثبيت المتطلبات الأساسية
echo 📋 تثبيت المتطلبات...
pip install -r requirements.txt --quiet
if errorlevel 1 (
    echo ❌ فشل في تثبيت بعض المتطلبات
    echo ⚠️ سيتم المتابعة مع المتطلبات المتوفرة
)

echo ✅ تم التحقق من المتطلبات
echo.

REM تشغيل سكريبت البناء
echo 🏗️ بدء بناء ملف EXE...
echo.
python build_exe.py

REM التحقق من نجاح البناء
if exist "dist\StudentManagementSystem.exe" (
    echo.
    echo ========================================
    echo 🎉 تم بناء التطبيق بنجاح!
    echo ========================================
    echo.
    echo 📁 ملف EXE: dist\StudentManagementSystem.exe
    echo 📦 حزمة التوزيع: مجلد dist\
    echo 🔧 ملف التثبيت: dist\installer.bat
    echo.
    echo للتثبيت على جهاز آخر:
    echo 1. انسخ مجلد dist بالكامل
    echo 2. شغل installer.bat
    echo.
    
    REM سؤال المستخدم عن تشغيل التطبيق
    set /p choice="هل تريد تشغيل التطبيق الآن؟ (y/n): "
    if /i "%choice%"=="y" (
        echo 🚀 تشغيل التطبيق...
        start "" "dist\StudentManagementSystem.exe"
    ) else if /i "%choice%"=="yes" (
        echo 🚀 تشغيل التطبيق...
        start "" "dist\StudentManagementSystem.exe"
    ) else if /i "%choice%"=="ن" (
        echo 🚀 تشغيل التطبيق...
        start "" "dist\StudentManagementSystem.exe"
    ) else if /i "%choice%"=="نعم" (
        echo 🚀 تشغيل التطبيق...
        start "" "dist\StudentManagementSystem.exe"
    )
    
) else (
    echo.
    echo ❌ فشل في بناء ملف EXE
    echo يرجى مراجعة الأخطاء أعلاه
    echo.
)

echo.
echo اضغط أي مفتاح للخروج...
pause >nul
