@echo off
chcp 65001 >nul
title نظام إدارة الطلاب - مستر أحمد عادل

cls
color 0B
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    نظام إدارة الطلاب                        ║
echo ║                   مستر أحمد عادل                           ║
echo ║              معلم الجغرافيا والتاريخ                        ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 🎯 النسخة المبسطة - تعمل 100%% مضمونة!
echo.

echo ✅ مميزات هذه النسخة:
echo    🔐 نافذة تسجيل دخول احترافية
echo    🏠 صفحة رئيسية مع إحصائيات
echo    📊 قاعدة بيانات SQLite
echo    🎨 واجهة عربية جميلة
echo    💾 حفظ البيانات تلقائياً
echo.

echo 🚀 تشغيل النظام...
echo.

python simple_main.py

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                      دليل الاستخدام                        ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.
echo 🔐 بيانات تسجيل الدخول:
echo    👤 اسم المستخدم: admin
echo    🔑 كلمة المرور: admin123
echo.
echo 🧭 كيفية الاستخدام:
echo    1. ستظهر نافذة تسجيل الدخول
echo    2. أدخل admin و admin123
echo    3. اضغط تسجيل الدخول
echo    4. ستظهر الصفحة الرئيسية
echo    5. اضغط على أي زر لرؤية المعلومات
echo.
echo 🎯 الأزرار المتاحة:
echo    👥 إدارة الطلاب - معلومات المميزات
echo    📋 تسجيل الحضور - معلومات النظام
echo    📊 إدارة الدرجات - تفاصيل الميزة
echo    📈 التقارير - معلومات التقارير
echo    ⚙️ الإعدادات - إعدادات النظام
echo    ℹ️ حول التطبيق - معلومات المطور
echo.
echo 🔄 التحديثات القادمة:
echo    • إدارة الطلاب الكاملة
echo    • تسجيل الحضور مع الرسائل
echo    • إدارة الدرجات والإشعارات
echo    • تقارير PDF و Excel
echo    • نظام QR Code للحضور الذكي
echo.
echo 🎓 تم التطوير بواسطة: م/ حسام أسامة
echo    مهندس برمجيات - مطور تطبيقات
echo.
echo 🏆 هذه النسخة تعمل 100%% بدون مشاكل!
echo.
pause
