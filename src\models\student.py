# -*- coding: utf-8 -*-
"""
نموذج الطالب
Student Model
"""

from datetime import datetime
from typing import Optional, List, Dict, Any
from ..database.database_manager import DatabaseManager

class Student:
    """نموذج بيانات الطالب"""
    
    def __init__(self, db_manager: DatabaseManager):
        self.db = db_manager
    
    def create_student(self, student_data: Dict[str, Any]) -> Optional[int]:
        """
        إنشاء طالب جديد
        
        Args:
            student_data: بيانات الطالب
            
        Returns:
            معرف الطالب الجديد أو None في حالة الفشل
        """
        try:
            # التحقق من عدم تكرار كود الطالب
            existing = self.db.fetch_one(
                "SELECT id FROM students WHERE student_code = ?", 
                (student_data['student_code'],)
            )
            
            if existing:
                raise ValueError(f"كود الطالب {student_data['student_code']} موجود مسبقاً")
            
            # إدراج الطالب الجديد
            cursor = self.db.execute_query("""
                INSERT INTO students (
                    student_code, full_name, gender, stage, grade, group_name,
                    phone, parent_phone, geography_score, history_score
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                student_data['student_code'],
                student_data['full_name'],
                student_data['gender'],
                student_data['stage'],
                student_data['grade'],
                student_data.get('group_name', 'لا توجد مجموعة'),
                student_data.get('phone', ''),
                student_data.get('parent_phone', ''),
                student_data.get('geography_score', 0),
                student_data.get('history_score', 0)
            ))
            
            return cursor.lastrowid
            
        except Exception as e:
            print(f"خطأ في إنشاء الطالب: {e}")
            return None
    
    def get_student_by_id(self, student_id: int) -> Optional[Dict[str, Any]]:
        """جلب بيانات طالب بالمعرف"""
        return self.db.fetch_one(
            "SELECT * FROM students WHERE id = ?", 
            (student_id,)
        )
    
    def get_student_by_code(self, student_code: str) -> Optional[Dict[str, Any]]:
        """جلب بيانات طالب بالكود"""
        return self.db.fetch_one(
            "SELECT * FROM students WHERE student_code = ?", 
            (student_code,)
        )
    
    def get_all_students(self) -> List[Dict[str, Any]]:
        """جلب جميع الطلاب"""
        return self.db.fetch_all(
            "SELECT * FROM students ORDER BY full_name"
        )
    
    def get_students_by_stage(self, stage: str) -> List[Dict[str, Any]]:
        """جلب الطلاب حسب المرحلة"""
        return self.db.fetch_all(
            "SELECT * FROM students WHERE stage = ? ORDER BY grade, full_name", 
            (stage,)
        )
    
    def get_students_by_grade(self, grade: str) -> List[Dict[str, Any]]:
        """جلب الطلاب حسب الصف"""
        return self.db.fetch_all(
            "SELECT * FROM students WHERE grade = ? ORDER BY full_name", 
            (grade,)
        )
    
    def search_students(self, search_term: str) -> List[Dict[str, Any]]:
        """البحث عن الطلاب بالاسم أو الكود"""
        search_pattern = f"%{search_term}%"
        return self.db.fetch_all("""
            SELECT * FROM students 
            WHERE full_name LIKE ? OR student_code LIKE ?
            ORDER BY full_name
        """, (search_pattern, search_pattern))
    
    def update_student(self, student_id: int, student_data: Dict[str, Any]) -> bool:
        """تحديث بيانات طالب"""
        try:
            # التحقق من عدم تكرار كود الطالب (إذا تم تغييره)
            if 'student_code' in student_data:
                existing = self.db.fetch_one("""
                    SELECT id FROM students 
                    WHERE student_code = ? AND id != ?
                """, (student_data['student_code'], student_id))
                
                if existing:
                    raise ValueError(f"كود الطالب {student_data['student_code']} موجود مسبقاً")
            
            # بناء استعلام التحديث
            update_fields = []
            params = []
            
            for field in ['student_code', 'full_name', 'gender', 'stage', 'grade', 
                         'geography_score', 'history_score']:
                if field in student_data:
                    update_fields.append(f"{field} = ?")
                    params.append(student_data[field])
            
            if not update_fields:
                return False
            
            update_fields.append("updated_at = CURRENT_TIMESTAMP")
            params.append(student_id)
            
            query = f"UPDATE students SET {', '.join(update_fields)} WHERE id = ?"
            
            self.db.execute_query(query, tuple(params))
            return True
            
        except Exception as e:
            print(f"خطأ في تحديث الطالب: {e}")
            return False
    
    def delete_student(self, student_id: int) -> bool:
        """حذف طالب"""
        try:
            self.db.execute_query("DELETE FROM students WHERE id = ?", (student_id,))
            return True
        except Exception as e:
            print(f"خطأ في حذف الطالب: {e}")
            return False
    
    def get_student_statistics(self) -> Dict[str, Any]:
        """جلب إحصائيات الطلاب"""
        stats = {}
        
        # العدد الإجمالي
        total = self.db.fetch_one("SELECT COUNT(*) as count FROM students")
        stats['total_students'] = total['count'] if total else 0
        
        # حسب الجنس
        gender_stats = self.db.fetch_all("""
            SELECT gender, COUNT(*) as count 
            FROM students 
            GROUP BY gender
        """)
        stats['by_gender'] = {row['gender']: row['count'] for row in gender_stats}
        
        # حسب المرحلة
        stage_stats = self.db.fetch_all("""
            SELECT stage, COUNT(*) as count 
            FROM students 
            GROUP BY stage
        """)
        stats['by_stage'] = {row['stage']: row['count'] for row in stage_stats}
        
        # حسب الصف
        grade_stats = self.db.fetch_all("""
            SELECT grade, COUNT(*) as count 
            FROM students 
            GROUP BY grade
            ORDER BY grade
        """)
        stats['by_grade'] = {row['grade']: row['count'] for row in grade_stats}
        
        return stats
    
    def generate_student_code(self) -> str:
        """توليد كود طالب جديد"""
        # جلب آخر كود مستخدم
        last_student = self.db.fetch_one("""
            SELECT student_code FROM students 
            WHERE student_code LIKE 'STD%'
            ORDER BY CAST(SUBSTR(student_code, 4) AS INTEGER) DESC 
            LIMIT 1
        """)
        
        if last_student:
            # استخراج الرقم من آخر كود
            last_code = last_student['student_code']
            if last_code.startswith('STD'):
                try:
                    last_number = int(last_code[3:])
                    new_number = last_number + 1
                except ValueError:
                    new_number = 1
            else:
                new_number = 1
        else:
            new_number = 1
        
        return f"STD{new_number:04d}"  # مثال: STD0001

    def search_students_by_name(self, search_text: str) -> List[Dict[str, Any]]:
        """
        البحث عن الطلاب بالاسم

        Args:
            search_text: النص المراد البحث عنه

        Returns:
            قائمة بالطلاب المطابقين
        """
        try:
            # البحث في الاسم الكامل
            students = self.db.fetch_all("""
                SELECT * FROM students
                WHERE full_name LIKE ?
                ORDER BY full_name
            """, (f"%{search_text}%",))

            return students if students else []

        except Exception as e:
            print(f"خطأ في البحث عن الطلاب: {e}")
            return []
