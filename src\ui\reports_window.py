# -*- coding: utf-8 -*-
"""
نافذة التقارير
Reports Window
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                            QPushButton, QComboBox, QMessageBox, QFrame, 
                            QGroupBox, QFormLayout, QDateEdit, QTabWidget,
                            QTextEdit, QProgressBar, QFileDialog, QCheckBox)
from PyQt5.QtCore import Qt, QDate, QThread, pyqtSignal
from PyQt5.QtGui import QFont

from datetime import date, datetime
from ..database.database_manager import DatabaseManager
from ..reports.report_generator import ReportGenerator, PDFReportGenerator, ExcelReportGenerator

class ReportGenerationThread(QThread):
    """خيط منفصل لإنشاء التقارير"""
    
    progress_updated = pyqtSignal(int)
    report_generated = pyqtSignal(dict)
    error_occurred = pyqtSignal(str)
    
    def __init__(self, generator, report_type, **kwargs):
        super().__init__()
        self.generator = generator
        self.report_type = report_type
        self.kwargs = kwargs
    
    def run(self):
        """تشغيل إنشاء التقرير"""
        try:
            self.progress_updated.emit(25)
            
            if self.report_type == 'students':
                report_data = self.generator.generate_students_report(**self.kwargs)
            elif self.report_type == 'attendance':
                report_data = self.generator.generate_attendance_report(**self.kwargs)
            elif self.report_type == 'grades':
                report_data = self.generator.generate_grades_report(**self.kwargs)
            elif self.report_type == 'comprehensive':
                report_data = self.generator.generate_comprehensive_report(**self.kwargs)
            else:
                raise ValueError(f"نوع تقرير غير مدعوم: {self.report_type}")
            
            self.progress_updated.emit(100)
            self.report_generated.emit(report_data)
            
        except Exception as e:
            self.error_occurred.emit(str(e))

class ReportsWindow(QWidget):
    """نافذة التقارير"""
    
    def __init__(self, db_manager: DatabaseManager):
        super().__init__()
        self.db_manager = db_manager
        self.report_generator = ReportGenerator(db_manager)
        self.pdf_generator = PDFReportGenerator(db_manager)
        self.excel_generator = ExcelReportGenerator(db_manager)
        
        self.current_report_data = None
        self.generation_thread = None
        
        self.init_ui()
        self.setup_styles()
    
    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        self.setWindowTitle("التقارير")
        self.setGeometry(100, 100, 1000, 700)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout()
        
        # التبويبات
        tabs = QTabWidget()
        
        # تبويب تقرير الطلاب
        students_tab = self.create_students_report_tab()
        tabs.addTab(students_tab, "تقرير الطلاب")
        
        # تبويب تقرير الحضور
        attendance_tab = self.create_attendance_report_tab()
        tabs.addTab(attendance_tab, "تقرير الحضور")
        
        # تبويب تقرير الدرجات
        grades_tab = self.create_grades_report_tab()
        tabs.addTab(grades_tab, "تقرير الدرجات")
        
        # تبويب التقرير الشامل
        comprehensive_tab = self.create_comprehensive_report_tab()
        tabs.addTab(comprehensive_tab, "التقرير الشامل")
        
        main_layout.addWidget(tabs)
        
        # شريط التقدم
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        main_layout.addWidget(self.progress_bar)
        
        # منطقة عرض التقرير
        self.report_display = QTextEdit()
        self.report_display.setReadOnly(True)
        self.report_display.setPlaceholderText("اختر نوع التقرير وانقر على 'إنشاء التقرير' لعرض النتائج هنا")
        main_layout.addWidget(self.report_display)
        
        # أزرار التصدير
        export_layout = QHBoxLayout()
        
        self.export_pdf_button = QPushButton("تصدير PDF")
        self.export_pdf_button.clicked.connect(self.export_to_pdf)
        self.export_pdf_button.setEnabled(False)
        
        self.export_excel_button = QPushButton("تصدير Excel")
        self.export_excel_button.clicked.connect(self.export_to_excel)
        self.export_excel_button.setEnabled(False)
        
        self.print_button = QPushButton("طباعة")
        self.print_button.clicked.connect(self.print_report)
        self.print_button.setEnabled(False)
        
        export_layout.addWidget(self.export_pdf_button)
        export_layout.addWidget(self.export_excel_button)
        export_layout.addWidget(self.print_button)
        export_layout.addStretch()
        
        main_layout.addLayout(export_layout)
        
        self.setLayout(main_layout)
    
    def create_students_report_tab(self):
        """إنشاء تبويب تقرير الطلاب"""
        widget = QWidget()
        layout = QVBoxLayout()
        
        # إعدادات التقرير
        settings_group = QGroupBox("إعدادات تقرير الطلاب")
        settings_layout = QFormLayout()
        
        # فلتر المرحلة
        self.students_stage_combo = QComboBox()
        self.students_stage_combo.addItems(["الكل", "إعدادي", "ثانوي"])
        settings_layout.addRow("المرحلة:", self.students_stage_combo)
        
        # فلتر الصف
        self.students_grade_combo = QComboBox()
        self.students_grade_combo.addItems(["الكل", "أولى إعدادي", "ثانية إعدادي", "ثالثة إعدادي",
                                           "أولى ثانوي", "ثانية ثانوي", "ثالثة ثانوي"])
        settings_layout.addRow("الصف:", self.students_grade_combo)
        
        # خيارات التقرير
        self.include_grades_checkbox = QCheckBox("تضمين الدرجات")
        self.include_grades_checkbox.setChecked(True)
        settings_layout.addRow("", self.include_grades_checkbox)
        
        # زر إنشاء التقرير
        self.generate_students_button = QPushButton("إنشاء تقرير الطلاب")
        self.generate_students_button.clicked.connect(self.generate_students_report)
        settings_layout.addRow("", self.generate_students_button)
        
        settings_group.setLayout(settings_layout)
        layout.addWidget(settings_group)
        layout.addStretch()
        
        widget.setLayout(layout)
        return widget
    
    def create_attendance_report_tab(self):
        """إنشاء تبويب تقرير الحضور"""
        widget = QWidget()
        layout = QVBoxLayout()
        
        # إعدادات التقرير
        settings_group = QGroupBox("إعدادات تقرير الحضور")
        settings_layout = QFormLayout()
        
        # فترة التقرير
        self.attendance_start_date = QDateEdit()
        self.attendance_start_date.setDate(QDate.currentDate().addDays(-30))
        settings_layout.addRow("من تاريخ:", self.attendance_start_date)
        
        self.attendance_end_date = QDateEdit()
        self.attendance_end_date.setDate(QDate.currentDate())
        settings_layout.addRow("إلى تاريخ:", self.attendance_end_date)
        
        # فلتر المرحلة
        self.attendance_stage_combo = QComboBox()
        self.attendance_stage_combo.addItems(["الكل", "إعدادي", "ثانوي"])
        settings_layout.addRow("المرحلة:", self.attendance_stage_combo)
        
        # خيارات التقرير
        self.include_daily_details_checkbox = QCheckBox("تضمين التفاصيل اليومية")
        self.include_daily_details_checkbox.setChecked(True)
        settings_layout.addRow("", self.include_daily_details_checkbox)
        
        # زر إنشاء التقرير
        self.generate_attendance_button = QPushButton("إنشاء تقرير الحضور")
        self.generate_attendance_button.clicked.connect(self.generate_attendance_report)
        settings_layout.addRow("", self.generate_attendance_button)
        
        settings_group.setLayout(settings_layout)
        layout.addWidget(settings_group)
        layout.addStretch()
        
        widget.setLayout(layout)
        return widget
    
    def create_grades_report_tab(self):
        """إنشاء تبويب تقرير الدرجات"""
        widget = QWidget()
        layout = QVBoxLayout()
        
        # إعدادات التقرير
        settings_group = QGroupBox("إعدادات تقرير الدرجات")
        settings_layout = QFormLayout()
        
        # فلتر المادة
        self.grades_subject_combo = QComboBox()
        self.grades_subject_combo.addItems(["الكل", "جغرافيا", "تاريخ"])
        settings_layout.addRow("المادة:", self.grades_subject_combo)
        
        # فلتر الصف
        self.grades_grade_combo = QComboBox()
        self.grades_grade_combo.addItems(["الكل", "أولى إعدادي", "ثانية إعدادي", "ثالثة إعدادي",
                                         "أولى ثانوي", "ثانية ثانوي", "ثالثة ثانوي"])
        settings_layout.addRow("الصف:", self.grades_grade_combo)
        
        # خيارات التقرير
        self.include_statistics_checkbox = QCheckBox("تضمين الإحصائيات")
        self.include_statistics_checkbox.setChecked(True)
        settings_layout.addRow("", self.include_statistics_checkbox)
        
        self.include_top_students_checkbox = QCheckBox("تضمين أفضل الطلاب")
        self.include_top_students_checkbox.setChecked(True)
        settings_layout.addRow("", self.include_top_students_checkbox)
        
        # زر إنشاء التقرير
        self.generate_grades_button = QPushButton("إنشاء تقرير الدرجات")
        self.generate_grades_button.clicked.connect(self.generate_grades_report)
        settings_layout.addRow("", self.generate_grades_button)
        
        settings_group.setLayout(settings_layout)
        layout.addWidget(settings_group)
        layout.addStretch()
        
        widget.setLayout(layout)
        return widget
    
    def create_comprehensive_report_tab(self):
        """إنشاء تبويب التقرير الشامل"""
        widget = QWidget()
        layout = QVBoxLayout()
        
        # إعدادات التقرير
        settings_group = QGroupBox("إعدادات التقرير الشامل")
        settings_layout = QFormLayout()
        
        # فلتر المرحلة
        self.comprehensive_stage_combo = QComboBox()
        self.comprehensive_stage_combo.addItems(["الكل", "إعدادي", "ثانوي"])
        settings_layout.addRow("المرحلة:", self.comprehensive_stage_combo)
        
        # وصف التقرير
        description = QLabel("""
التقرير الشامل يتضمن:
• إحصائيات الطلاب
• تقرير الحضور (آخر 30 يوم)
• تقرير الدرجات
• الإحصائيات العامة
        """)
        description.setWordWrap(True)
        settings_layout.addRow("الوصف:", description)
        
        # زر إنشاء التقرير
        self.generate_comprehensive_button = QPushButton("إنشاء التقرير الشامل")
        self.generate_comprehensive_button.clicked.connect(self.generate_comprehensive_report)
        settings_layout.addRow("", self.generate_comprehensive_button)
        
        settings_group.setLayout(settings_layout)
        layout.addWidget(settings_group)
        layout.addStretch()
        
        widget.setLayout(layout)
        return widget

    def generate_students_report(self):
        """إنشاء تقرير الطلاب"""
        stage_filter = self.students_stage_combo.currentText()
        grade_filter = self.students_grade_combo.currentText()

        if stage_filter == "الكل":
            stage_filter = None
        if grade_filter == "الكل":
            grade_filter = None

        self.start_report_generation('students', stage_filter=stage_filter, grade_filter=grade_filter)

    def generate_attendance_report(self):
        """إنشاء تقرير الحضور"""
        start_date = self.attendance_start_date.date().toPyDate()
        end_date = self.attendance_end_date.date().toPyDate()
        stage_filter = self.attendance_stage_combo.currentText()

        if stage_filter == "الكل":
            stage_filter = None

        self.start_report_generation('attendance', start_date=start_date,
                                   end_date=end_date, stage_filter=stage_filter)

    def generate_grades_report(self):
        """إنشاء تقرير الدرجات"""
        subject_filter = self.grades_subject_combo.currentText()
        grade_filter = self.grades_grade_combo.currentText()

        if subject_filter == "الكل":
            subject_filter = None
        if grade_filter == "الكل":
            grade_filter = None

        self.start_report_generation('grades', subject_filter=subject_filter, grade_filter=grade_filter)

    def generate_comprehensive_report(self):
        """إنشاء التقرير الشامل"""
        stage_filter = self.comprehensive_stage_combo.currentText()

        if stage_filter == "الكل":
            stage_filter = None

        self.start_report_generation('comprehensive', stage_filter=stage_filter)

    def start_report_generation(self, report_type, **kwargs):
        """بدء إنشاء التقرير في خيط منفصل"""
        # تعطيل الأزرار
        self.set_buttons_enabled(False)

        # إظهار شريط التقدم
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)

        # إنشاء وتشغيل الخيط
        self.generation_thread = ReportGenerationThread(
            self.report_generator, report_type, **kwargs
        )
        self.generation_thread.progress_updated.connect(self.progress_bar.setValue)
        self.generation_thread.report_generated.connect(self.on_report_generated)
        self.generation_thread.error_occurred.connect(self.on_report_error)
        self.generation_thread.start()

    def on_report_generated(self, report_data):
        """معالجة إنشاء التقرير بنجاح"""
        self.current_report_data = report_data

        # عرض التقرير
        self.display_report(report_data)

        # إخفاء شريط التقدم
        self.progress_bar.setVisible(False)

        # تفعيل الأزرار
        self.set_buttons_enabled(True)
        self.export_pdf_button.setEnabled(True)
        self.export_excel_button.setEnabled(True)
        self.print_button.setEnabled(True)

    def on_report_error(self, error_message):
        """معالجة خطأ في إنشاء التقرير"""
        QMessageBox.critical(self, "خطأ", f"فشل في إنشاء التقرير:\n{error_message}")

        # إخفاء شريط التقدم
        self.progress_bar.setVisible(False)

        # تفعيل الأزرار
        self.set_buttons_enabled(True)

    def set_buttons_enabled(self, enabled):
        """تفعيل/تعطيل أزرار إنشاء التقارير"""
        self.generate_students_button.setEnabled(enabled)
        self.generate_attendance_button.setEnabled(enabled)
        self.generate_grades_button.setEnabled(enabled)
        self.generate_comprehensive_button.setEnabled(enabled)

    def display_report(self, report_data):
        """عرض التقرير في منطقة النص"""
        report_text = self.format_report_text(report_data)
        self.report_display.setText(report_text)

    def format_report_text(self, report_data):
        """تنسيق نص التقرير للعرض"""
        text = f"<h2>{report_data['title']}</h2>"
        text += f"<p><b>تاريخ الإنشاء:</b> {report_data['generated_at'].strftime('%Y-%m-%d %H:%M')}</p>"

        # تنسيق حسب نوع التقرير
        if 'students' in report_data:
            text += self.format_students_report(report_data)
        elif 'daily_attendance' in report_data:
            text += self.format_attendance_report(report_data)
        elif 'grades' in report_data:
            text += self.format_grades_report(report_data)
        elif report_data['title'] == 'التقرير الشامل':
            text += self.format_comprehensive_report(report_data)

        return text

    def format_students_report(self, report_data):
        """تنسيق تقرير الطلاب"""
        text = "<h3>إحصائيات الطلاب</h3>"
        stats = report_data['statistics']

        text += f"<p><b>إجمالي الطلاب:</b> {stats.get('total_students', 0)}</p>"

        # إحصائيات حسب الجنس
        if 'by_gender' in stats:
            text += "<p><b>حسب الجنس:</b></p><ul>"
            for gender, count in stats['by_gender'].items():
                text += f"<li>{gender}: {count}</li>"
            text += "</ul>"

        # إحصائيات حسب المرحلة
        if 'by_stage' in stats:
            text += "<p><b>حسب المرحلة:</b></p><ul>"
            for stage, count in stats['by_stage'].items():
                text += f"<li>{stage}: {count}</li>"
            text += "</ul>"

        # عينة من الطلاب
        text += "<h3>عينة من الطلاب</h3>"
        text += "<table border='1' cellpadding='5'>"
        text += "<tr><th>الكود</th><th>الاسم</th><th>الصف</th><th>جغرافيا</th><th>تاريخ</th></tr>"

        for student in report_data['students'][:10]:  # أول 10 طلاب
            text += f"<tr>"
            text += f"<td>{student['student_code']}</td>"
            text += f"<td>{student['full_name']}</td>"
            text += f"<td>{student['grade']}</td>"
            text += f"<td>{student['geography_score']:.1f}</td>"
            text += f"<td>{student['history_score']:.1f}</td>"
            text += f"</tr>"

        text += "</table>"

        return text

    def format_attendance_report(self, report_data):
        """تنسيق تقرير الحضور"""
        text = "<h3>إحصائيات الحضور</h3>"
        stats = report_data['statistics']

        text += f"<p><b>إجمالي السجلات:</b> {stats.get('total_records', 0)}</p>"

        # إحصائيات حسب الحالة
        if 'by_status' in stats:
            text += "<p><b>حسب الحالة:</b></p><ul>"
            for status, count in stats['by_status'].items():
                text += f"<li>{status}: {count}</li>"
            text += "</ul>"

        return text

    def format_grades_report(self, report_data):
        """تنسيق تقرير الدرجات"""
        text = "<h3>إحصائيات الدرجات</h3>"

        # أفضل الطلاب
        if 'top_students' in report_data:
            for subject, students in report_data['top_students'].items():
                if students:
                    text += f"<h4>أفضل الطلاب في {subject}</h4>"
                    text += "<table border='1' cellpadding='5'>"
                    text += "<tr><th>الاسم</th><th>الصف</th><th>الدرجة</th></tr>"

                    for student in students[:5]:  # أفضل 5
                        text += f"<tr>"
                        text += f"<td>{student['full_name']}</td>"
                        text += f"<td>{student['grade']}</td>"
                        text += f"<td>{student['score']:.1f}%</td>"
                        text += f"</tr>"

                    text += "</table><br>"

        return text

    def format_comprehensive_report(self, report_data):
        """تنسيق التقرير الشامل"""
        text = "<h3>التقرير الشامل</h3>"

        # ملخص الطلاب
        students_data = report_data.get('students', {})
        if students_data:
            stats = students_data.get('statistics', {})
            text += f"<p><b>إجمالي الطلاب:</b> {stats.get('total_students', 0)}</p>"

        # ملخص الحضور
        attendance_data = report_data.get('attendance', {})
        if attendance_data:
            stats = attendance_data.get('statistics', {})
            text += f"<p><b>إجمالي سجلات الحضور:</b> {stats.get('total_records', 0)}</p>"

        # ملخص الدرجات
        grades_data = report_data.get('grades', {})
        if grades_data:
            text += f"<p><b>إجمالي الدرجات:</b> {grades_data.get('total_count', 0)}</p>"

        return text

    def export_to_pdf(self):
        """تصدير التقرير إلى PDF"""
        if not self.current_report_data:
            QMessageBox.warning(self, "تحذير", "لا يوجد تقرير لتصديره")
            return

        file_path, _ = QFileDialog.getSaveFileName(
            self, "حفظ التقرير كـ PDF",
            f"{self.current_report_data['title']}_{datetime.now().strftime('%Y%m%d_%H%M')}.pdf",
            "PDF Files (*.pdf)"
        )

        if file_path:
            if self.pdf_generator.export_to_pdf(self.current_report_data, file_path):
                QMessageBox.information(self, "تم", f"تم حفظ التقرير في:\n{file_path}")
            else:
                QMessageBox.critical(self, "خطأ", "فشل في تصدير التقرير إلى PDF")

    def export_to_excel(self):
        """تصدير التقرير إلى Excel"""
        if not self.current_report_data:
            QMessageBox.warning(self, "تحذير", "لا يوجد تقرير لتصديره")
            return

        file_path, _ = QFileDialog.getSaveFileName(
            self, "حفظ التقرير كـ Excel",
            f"{self.current_report_data['title']}_{datetime.now().strftime('%Y%m%d_%H%M')}.xlsx",
            "Excel Files (*.xlsx)"
        )

        if file_path:
            if self.excel_generator.export_to_excel(self.current_report_data, file_path):
                QMessageBox.information(self, "تم", f"تم حفظ التقرير في:\n{file_path}")
            else:
                QMessageBox.critical(self, "خطأ", "فشل في تصدير التقرير إلى Excel")

    def print_report(self):
        """طباعة التقرير"""
        if not self.current_report_data:
            QMessageBox.warning(self, "تحذير", "لا يوجد تقرير للطباعة")
            return

        from PyQt5.QtPrintSupport import QPrintDialog, QPrinter

        printer = QPrinter()
        dialog = QPrintDialog(printer, self)

        if dialog.exec_() == QPrintDialog.Accepted:
            self.report_display.print_(printer)

    def setup_styles(self):
        """تطبيق الأنماط"""
        style = """
        QGroupBox {
            font-weight: bold;
            border: 2px solid #cccccc;
            border-radius: 5px;
            margin-top: 1ex;
            padding-top: 10px;
        }

        QGroupBox::title {
            subcontrol-origin: margin;
            left: 10px;
            padding: 0 5px 0 5px;
        }

        QPushButton {
            background-color: #3498db;
            color: white;
            border: none;
            padding: 8px;
            border-radius: 4px;
            font-weight: bold;
        }

        QPushButton:hover {
            background-color: #2980b9;
        }

        QPushButton:pressed {
            background-color: #21618c;
        }

        QPushButton:disabled {
            background-color: #bdc3c7;
        }

        QComboBox, QDateEdit {
            padding: 5px;
            border: 1px solid #bdc3c7;
            border-radius: 3px;
        }

        QComboBox:focus, QDateEdit:focus {
            border-color: #3498db;
        }

        QTextEdit {
            border: 1px solid #bdc3c7;
            border-radius: 5px;
            background-color: white;
        }

        QProgressBar {
            border: 1px solid #bdc3c7;
            border-radius: 5px;
            text-align: center;
        }

        QProgressBar::chunk {
            background-color: #3498db;
            border-radius: 3px;
        }

        QTabWidget::pane {
            border: 1px solid #bdc3c7;
            background-color: white;
        }

        QTabBar::tab {
            background-color: #ecf0f1;
            padding: 8px 16px;
            margin-right: 2px;
            border-top-left-radius: 4px;
            border-top-right-radius: 4px;
        }

        QTabBar::tab:selected {
            background-color: white;
            border-bottom: 2px solid #3498db;
        }

        QTabBar::tab:hover {
            background-color: #d5dbdb;
        }
        """

        self.setStyleSheet(style)
