# -*- coding: utf-8 -*-
"""
النافذة الرئيسية
Main Window
"""

from PyQt5.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, 
                            QLabel, QPushButton, QFrame, QMenuBar, QMenu,
                            QAction, QStatusBar, QMessageBox, QGridLayout)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont, QIcon

from ..utils.auth import AuthManager
from ..models.student import Student
from ..models.attendance import Attendance
from ..models.grades import Grades
from .students_window import StudentsWindow
from .groups_window import GroupsWindow
from .payments_window import PaymentsWindow
# from .qr_attendance_window import QRAttendanceWindow  # معطل مؤقتاً
from .attendance_window import AttendanceWindow
from .grades_window import GradesWindow
from .reports_window import ReportsWindow
from .settings_window import SettingsWindow
from ..utils.styles import get_dashboard_style, get_arabic_font_style

class MainWindow(QMainWindow):
    """النافذة الرئيسية للتطبيق"""
    
    # إشارة تسجيل الخروج
    logout_signal = pyqtSignal()
    
    def __init__(self, auth_manager: AuthManager):
        super().__init__()
        self.auth_manager = auth_manager

        # تهيئة النماذج
        self.student_model = Student(auth_manager.db)
        self.attendance_model = Attendance(auth_manager.db)
        self.grades_model = Grades(auth_manager.db)

        self.init_ui()
        self.setup_menu()
        self.setup_status_bar()
        self.setup_styles()
        self.load_statistics()
    
    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        self.setWindowTitle("نظام إدارة الطلاب - مستر أحمد عادل")
        # تكبير النافذة لتكون أوضح
        self.setGeometry(50, 50, 1400, 900)
        # تكبير الخط الأساسي
        font = QFont("Arial", 14)  # زيادة حجم الخط
        self.setFont(font)
        
        # الويدجت المركزي
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout()

        # إضافة شريط التنقل
        nav_bar = self.create_navigation_bar()
        main_layout.addWidget(nav_bar)

        # إطار الترحيب
        welcome_frame = self.create_welcome_frame()
        main_layout.addWidget(welcome_frame)
        
        # إطار الإحصائيات السريعة
        stats_frame = self.create_stats_frame()
        main_layout.addWidget(stats_frame)
        
        # إطار الأزرار الرئيسية
        buttons_frame = self.create_main_buttons_frame()
        main_layout.addWidget(buttons_frame)
        
        main_layout.addStretch()
        central_widget.setLayout(main_layout)

    def create_navigation_bar(self):
        """إنشاء شريط التنقل"""
        nav_frame = QFrame()
        nav_frame.setObjectName("navigationBar")
        nav_frame.setStyleSheet("""
            QFrame#navigationBar {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                            stop:0 #2c3e50, stop:1 #34495e);
                border: none;
                border-radius: 8px;
                padding: 10px;
                margin: 5px;
            }
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 12px 20px;
                border-radius: 6px;
                font-size: 14px;
                font-weight: bold;
                margin: 0 5px;
                min-width: 120px;
            }
            QPushButton:hover {
                background-color: #2980b9;
                transform: translateY(-2px);
            }
            QPushButton:pressed {
                background-color: #21618c;
                transform: translateY(1px);
            }
            QPushButton#activeButton {
                background-color: #27ae60;
            }
            QPushButton#activeButton:hover {
                background-color: #229954;
            }
        """)

        layout = QHBoxLayout()
        layout.setSpacing(10)

        # أزرار التنقل
        self.nav_buttons = {}

        # زر الرئيسية
        home_btn = QPushButton("🏠 الرئيسية")
        home_btn.setObjectName("activeButton")
        home_btn.clicked.connect(self.show_dashboard)
        self.nav_buttons['home'] = home_btn
        layout.addWidget(home_btn)

        # زر إدارة الطلاب
        students_btn = QPushButton("👥 إدارة الطلاب")
        students_btn.clicked.connect(self.open_students_window)
        self.nav_buttons['students'] = students_btn
        layout.addWidget(students_btn)

        # زر إدارة المجموعات
        groups_btn = QPushButton("👨‍👩‍👧‍👦 إدارة المجموعات")
        groups_btn.clicked.connect(self.open_groups_window)
        self.nav_buttons['groups'] = groups_btn
        layout.addWidget(groups_btn)

        # زر إدارة المدفوعات
        payments_btn = QPushButton("💰 إدارة المدفوعات")
        payments_btn.clicked.connect(self.open_payments_window)
        self.nav_buttons['payments'] = payments_btn
        layout.addWidget(payments_btn)

        # زر إدارة الفيديوهات
        videos_btn = QPushButton("🎥 إدارة الفيديوهات")
        videos_btn.clicked.connect(self.open_videos_window)
        self.nav_buttons['videos'] = videos_btn
        layout.addWidget(videos_btn)

        # زر تسجيل الحضور
        attendance_btn = QPushButton("📋 تسجيل الحضور")
        attendance_btn.clicked.connect(self.open_attendance_window)
        self.nav_buttons['attendance'] = attendance_btn
        layout.addWidget(attendance_btn)

        # زر إدارة الدرجات
        grades_btn = QPushButton("📊 إدارة الدرجات")
        grades_btn.clicked.connect(self.open_grades_window)
        self.nav_buttons['grades'] = grades_btn
        layout.addWidget(grades_btn)

        # زر التقارير
        reports_btn = QPushButton("📈 التقارير")
        reports_btn.clicked.connect(self.open_reports_window)
        self.nav_buttons['reports'] = reports_btn
        layout.addWidget(reports_btn)

        # زر الإعدادات
        settings_btn = QPushButton("⚙️ الإعدادات")
        settings_btn.clicked.connect(self.open_settings_window)
        self.nav_buttons['settings'] = settings_btn
        layout.addWidget(settings_btn)

        # مساحة فارغة
        layout.addStretch()

        # زر تسجيل الخروج
        logout_btn = QPushButton("🚪 تسجيل الخروج")
        logout_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        logout_btn.clicked.connect(self.logout)
        layout.addWidget(logout_btn)

        nav_frame.setLayout(layout)
        return nav_frame

    def set_active_button(self, button_name):
        """تعيين الزر النشط"""
        for name, button in self.nav_buttons.items():
            if name == button_name:
                button.setObjectName("activeButton")
            else:
                button.setObjectName("")
            button.style().unpolish(button)
            button.style().polish(button)

    def show_dashboard(self):
        """إظهار لوحة التحكم"""
        self.set_active_button('home')
        # إغلاق النوافذ المفتوحة
        self.close_all_windows()
        # تحديث الإحصائيات
        self.update_dashboard_stats()

    def close_all_windows(self):
        """إغلاق جميع النوافذ المفتوحة"""
        if hasattr(self, 'students_window') and self.students_window:
            self.students_window.close()
            self.students_window = None
        if hasattr(self, 'groups_window') and self.groups_window:
            self.groups_window.close()
            self.groups_window = None
        if hasattr(self, 'payments_window') and self.payments_window:
            self.payments_window.close()
            self.payments_window = None
        if hasattr(self, 'qr_attendance_window') and self.qr_attendance_window:
            self.qr_attendance_window.close()
            self.qr_attendance_window = None
        if hasattr(self, 'attendance_window') and self.attendance_window:
            self.attendance_window.close()
            self.attendance_window = None
        if hasattr(self, 'grades_window') and self.grades_window:
            self.grades_window.close()
            self.grades_window = None
        if hasattr(self, 'reports_window') and self.reports_window:
            self.reports_window.close()
            self.reports_window = None
        if hasattr(self, 'settings_window') and self.settings_window:
            self.settings_window.close()
            self.settings_window = None

    def create_welcome_frame(self):
        """إنشاء إطار الترحيب"""
        frame = QFrame()
        frame.setObjectName("welcomeFrame")
        frame.setStyleSheet("""
            QFrame#welcomeFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                            stop:0 #3498db, stop:1 #2980b9);
                border-radius: 15px;
                margin: 10px;
                padding: 20px;
            }
            QLabel {
                color: white;
                font-weight: bold;
                background: transparent;
            }
        """)
        layout = QVBoxLayout()

        # رسالة الترحيب الرئيسية
        main_welcome = QLabel("أهلاً وسهلاً أ/ أحمد عادل")
        main_welcome.setAlignment(Qt.AlignCenter)
        main_welcome.setStyleSheet("font-size: 28px; font-weight: bold; margin: 10px;")

        # رسالة فرعية
        sub_welcome = QLabel("معلم الجغرافيا والتاريخ")
        sub_welcome.setAlignment(Qt.AlignCenter)
        sub_welcome.setStyleSheet("font-size: 18px; margin: 5px;")

        # النص المطلوب - قناة نظام الطلاب
        channel_info = QLabel("قناة نظام طلاب مستر أحمد عادل – دروس، امتحانات، ومتابعة مستمرة.")
        channel_info.setAlignment(Qt.AlignCenter)
        channel_info.setStyleSheet("font-size: 16px; font-weight: bold; margin: 15px; color: #FFD700; text-shadow: 2px 2px 4px rgba(0,0,0,0.5);")
        channel_info.setWordWrap(True)

        # تاريخ اليوم
        from datetime import datetime
        today = datetime.now().strftime("%A, %Y-%m-%d")
        date_label = QLabel(f"التاريخ: {today}")
        date_label.setAlignment(Qt.AlignCenter)
        date_label.setStyleSheet("font-size: 14px; margin: 5px;")

        # معلومات المطور
        developer_label = QLabel("تم التطوير بواسطة: م/ حسام أسامة")
        developer_label.setAlignment(Qt.AlignCenter)
        developer_label.setStyleSheet("font-size: 12px; margin: 10px; opacity: 0.8;")

        layout.addWidget(main_welcome)
        layout.addWidget(sub_welcome)
        layout.addWidget(channel_info)  # إضافة النص المطلوب
        layout.addWidget(date_label)
        layout.addWidget(developer_label)

        frame.setLayout(layout)
        return frame
    
    def create_stats_frame(self):
        """إنشاء إطار الإحصائيات السريعة"""
        frame = QFrame()
        frame.setObjectName("statsFrame")
        frame.setStyleSheet("""
            QFrame#statsFrame {
                background: transparent;
                margin: 10px;
            }
        """)
        self.stats_layout = QGridLayout()
        self.stats_layout.setSpacing(10)
        self.stats_layout.setContentsMargins(20, 10, 20, 10)

        # إحصائيات الحضور والدرجات
        self.stats_widgets = {}
        stats = [
            ("الحضور", "0", "#27ae60", "👥"),
            ("الغياب", "0", "#e74c3c", "❌"),
            ("التأخير", "0", "#f39c12", "⏰"),
            ("المعدل", "0%", "#9b59b6", "📊")
        ]

        for i, (title, value, color, icon) in enumerate(stats):
            stat_widget = self.create_stat_widget(title, value, color, icon)
            self.stats_widgets[title] = stat_widget
            self.stats_layout.addWidget(stat_widget, 0, i)

        frame.setLayout(self.stats_layout)

        # تحديث الإحصائيات عند إنشاء الإطار
        self.update_dashboard_stats()

        return frame
    
    def create_stat_widget(self, title, value, color, icon=""):
        """إنشاء ويدجت إحصائية"""
        widget = QFrame()
        widget.setObjectName("statWidget")
        widget.setStyleSheet(f"""
            QFrame#statWidget {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                            stop:0 {color}, stop:1 {self.darken_color(color)});
                border-radius: 10px;
                padding: 15px;
                margin: 5px;
                border: 1px solid rgba(255, 255, 255, 0.2);
                min-width: 120px;
                max-width: 150px;
                min-height: 100px;
                max-height: 120px;
            }}
            QLabel {{
                color: white;
                background: transparent;
                font-weight: bold;
            }}
        """)

        layout = QVBoxLayout()
        layout.setSpacing(5)
        layout.setContentsMargins(5, 5, 5, 5)

        # أيقونة
        if icon:
            icon_label = QLabel(icon)
            icon_label.setAlignment(Qt.AlignCenter)
            icon_label.setStyleSheet("font-size: 20px; margin: 2px;")
            layout.addWidget(icon_label)

        # القيمة
        value_label = QLabel(value)
        value_label.setAlignment(Qt.AlignCenter)
        value_label.setObjectName("statValue")
        value_label.setStyleSheet("font-size: 18px; font-weight: bold; margin: 2px;")

        # العنوان
        title_label = QLabel(title)
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setObjectName("statTitle")
        title_label.setStyleSheet("font-size: 12px; margin: 2px;")

        layout.addWidget(value_label)
        layout.addWidget(title_label)

        widget.setLayout(layout)
        widget.value_label = value_label  # حفظ مرجع للتحديث
        return widget

    def darken_color(self, color):
        """تغميق اللون للتدرج"""
        color_map = {
            "#27ae60": "#229954",
            "#e74c3c": "#c0392b",
            "#f39c12": "#e67e22",
            "#9b59b6": "#8e44ad",
            "#3498db": "#2980b9"
        }
        return color_map.get(color, color)

    def update_dashboard_stats(self):
        """تحديث إحصائيات لوحة التحكم"""
        try:
            from datetime import date
            today = date.today().strftime("%Y-%m-%d")

            # إحصائيات الحضور لليوم
            attendance_query = """
                SELECT status, COUNT(*)
                FROM attendance
                WHERE attendance_date = ?
                GROUP BY status
            """
            attendance_result = self.auth_manager.db.execute_query_with_fetch(
                attendance_query, (today,), fetch=True
            )

            # تجميع إحصائيات الحضور
            attendance_stats = {"حاضر": 0, "غائب": 0, "متأخر": 0}
            for status, count in attendance_result:
                if status in attendance_stats:
                    attendance_stats[status] = count

            # إحصائيات الدرجات
            grades_query = """
                SELECT AVG((score * 100.0) / max_score) as average
                FROM grades
                WHERE score > 0 AND max_score > 0
            """
            grades_result = self.auth_manager.db.execute_query_with_fetch(
                grades_query, fetch=True
            )

            average_grade = 0
            if grades_result and grades_result[0][0]:
                average_grade = round(grades_result[0][0], 1)

            # تحديث الويدجتات
            if "الحضور" in self.stats_widgets:
                self.stats_widgets["الحضور"].value_label.setText(str(attendance_stats["حاضر"]))

            if "الغياب" in self.stats_widgets:
                self.stats_widgets["الغياب"].value_label.setText(str(attendance_stats["غائب"]))

            if "التأخير" in self.stats_widgets:
                self.stats_widgets["التأخير"].value_label.setText(str(attendance_stats["متأخر"]))

            if "المعدل" in self.stats_widgets:
                self.stats_widgets["المعدل"].value_label.setText(f"{average_grade}%")

        except Exception as e:
            print(f"خطأ في تحديث الإحصائيات: {e}")
            # قيم افتراضية في حالة الخطأ
            default_stats = {"الحضور": "0", "الغياب": "0", "التأخير": "0", "المعدل": "0%"}
            for stat_name, value in default_stats.items():
                if stat_name in self.stats_widgets:
                    self.stats_widgets[stat_name].value_label.setText(value)
    
    def create_main_buttons_frame(self):
        """إنشاء إطار الأزرار الرئيسية"""
        frame = QFrame()
        layout = QGridLayout()
        layout.setSpacing(20)
        
        # أزرار الوظائف الرئيسية
        buttons = [
            ("إدارة الطلاب", "إضافة وتعديل وحذف الطلاب", self.open_students_management),
            ("تسجيل الحضور", "تسجيل حضور وغياب الطلاب", self.open_attendance),
            ("إدارة الدرجات", "إدخال وتعديل درجات الطلاب", self.open_grades_management),
            ("التقارير", "إنشاء وطباعة التقارير", self.open_reports),
            ("الإعدادات", "إعدادات التطبيق العامة", self.open_settings),
            ("تسجيل الخروج", "الخروج من التطبيق", self.logout)
        ]
        
        for i, (title, description, callback) in enumerate(buttons):
            button = self.create_main_button(title, description, callback)
            row = i // 3
            col = i % 3
            layout.addWidget(button, row, col)
        
        frame.setLayout(layout)
        return frame
    
    def create_main_button(self, title, description, callback):
        """إنشاء زر رئيسي"""
        button = QPushButton()
        button.setObjectName("mainButton")
        button.clicked.connect(callback)
        
        layout = QVBoxLayout()
        
        title_label = QLabel(title)
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setObjectName("buttonTitle")
        
        desc_label = QLabel(description)
        desc_label.setAlignment(Qt.AlignCenter)
        desc_label.setObjectName("buttonDesc")
        desc_label.setWordWrap(True)
        
        layout.addWidget(title_label)
        layout.addWidget(desc_label)
        
        button.setLayout(layout)
        return button
    
    def setup_menu(self):
        """إعداد شريط القوائم"""
        menubar = self.menuBar()
        
        # قائمة الملف
        file_menu = menubar.addMenu('ملف')
        
        logout_action = QAction('تسجيل الخروج', self)
        logout_action.triggered.connect(self.logout)
        file_menu.addAction(logout_action)
        
        file_menu.addSeparator()
        
        exit_action = QAction('خروج', self)
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        # قائمة الطلاب
        students_menu = menubar.addMenu('الطلاب')
        
        manage_students_action = QAction('إدارة الطلاب', self)
        manage_students_action.triggered.connect(self.open_students_management)
        students_menu.addAction(manage_students_action)
        
        attendance_action = QAction('تسجيل الحضور', self)
        attendance_action.triggered.connect(self.open_attendance)
        students_menu.addAction(attendance_action)
        
        grades_action = QAction('إدارة الدرجات', self)
        grades_action.triggered.connect(self.open_grades_management)
        students_menu.addAction(grades_action)
        
        # قائمة التقارير
        reports_menu = menubar.addMenu('التقارير')
        
        reports_action = QAction('إنشاء التقارير', self)
        reports_action.triggered.connect(self.open_reports)
        reports_menu.addAction(reports_action)
        
        # قائمة المساعدة
        help_menu = menubar.addMenu('مساعدة')
        
        about_action = QAction('حول التطبيق', self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)
    
    def setup_status_bar(self):
        """إعداد شريط الحالة"""
        status_bar = self.statusBar()
        
        user = self.auth_manager.get_current_user()
        status_bar.showMessage(f"مسجل الدخول: {user['full_name']} | نظام إدارة الطلاب v1.0")
    
    def setup_styles(self):
        """تطبيق الأنماط"""
        # تطبيق الأنماط الجديدة
        style = get_dashboard_style() + get_arabic_font_style()
        self.setStyleSheet(style)

    def load_statistics(self):
        """تحميل الإحصائيات الحقيقية"""
        try:
            # إحصائيات الطلاب
            student_stats = self.student_model.get_student_statistics()
            total_students = student_stats.get('total_students', 0)

            # إحصائيات الحضور لليوم
            from datetime import date
            today = date.today()
            daily_attendance = self.attendance_model.get_daily_attendance(today)

            present_count = sum(1 for record in daily_attendance if record.get('status') == 'حاضر')
            absent_count = sum(1 for record in daily_attendance if record.get('status') == 'غائب' or record.get('status') is None)

            # متوسط الدرجات
            all_students = self.student_model.get_all_students()
            if all_students:
                total_geography = sum(s.get('geography_score', 0) for s in all_students)
                total_history = sum(s.get('history_score', 0) for s in all_students)
                avg_score = (total_geography + total_history) / (2 * len(all_students))
            else:
                avg_score = 0

            # تحديث الويدجتات
            self.update_stat_widget("إجمالي الطلاب", str(total_students))
            self.update_stat_widget("الحاضرين اليوم", str(present_count))
            self.update_stat_widget("الغائبين اليوم", str(absent_count))
            self.update_stat_widget("متوسط الدرجات", f"{avg_score:.1f}%")

        except Exception as e:
            print(f"خطأ في تحميل الإحصائيات: {e}")

    def update_stat_widget(self, title, value):
        """تحديث قيمة ويدجت إحصائية"""
        if title in self.stats_widgets:
            widget = self.stats_widgets[title]
            layout = widget.layout()
            if layout and layout.count() > 0:
                value_label = layout.itemAt(0).widget()
                if value_label:
                    value_label.setText(value)

    # دوال معالجة الأحداث (ستتم إضافة التفاصيل لاحقاً)
    def open_students_window(self):
        """فتح نافذة إدارة الطلاب"""
        try:
            self.set_active_button('students')
            self.close_all_windows()
            self.students_window = StudentsWindow(self.auth_manager.db)
            self.students_window.show()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في فتح نافذة إدارة الطلاب: {str(e)}")

    def open_students_management(self):
        """فتح نافذة إدارة الطلاب (للتوافق مع الكود القديم)"""
        self.open_students_window()

    def open_groups_window(self):
        """فتح نافذة إدارة المجموعات"""
        try:
            self.set_active_button('groups')
            self.close_all_windows()
            self.groups_window = GroupsWindow(self.auth_manager.db)
            self.groups_window.show()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في فتح نافذة إدارة المجموعات: {str(e)}")

    def open_payments_window(self):
        """فتح نافذة إدارة المدفوعات"""
        try:
            self.set_active_button('payments')
            self.close_all_windows()
            self.payments_window = PaymentsWindow(self.auth_manager.db)
            self.payments_window.show()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في فتح نافذة إدارة المدفوعات: {str(e)}")

    def open_videos_window(self):
        """فتح نافذة إدارة الفيديوهات"""
        try:
            self.set_active_button('videos')
            self.close_all_windows()
            from .videos_window import VideosWindow
            self.videos_window = VideosWindow(self.auth_manager.db)
            self.videos_window.show()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في فتح نافذة إدارة الفيديوهات: {str(e)}")
    
    def open_attendance_window(self):
        """فتح نافذة تسجيل الحضور"""
        try:
            self.set_active_button('attendance')
            self.close_all_windows()
            self.attendance_window = AttendanceWindow(self.auth_manager.db)
            self.attendance_window.show()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في فتح نافذة تسجيل الحضور: {str(e)}")

    def open_attendance(self):
        """فتح نافذة تسجيل الحضور (للتوافق مع الكود القديم)"""
        self.open_attendance_window()
    
    def open_grades_window(self):
        """فتح نافذة إدارة الدرجات"""
        try:
            self.set_active_button('grades')
            self.close_all_windows()
            self.grades_window = GradesWindow(self.auth_manager.db)
            self.grades_window.show()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في فتح نافذة إدارة الدرجات: {str(e)}")

    def open_grades_management(self):
        """فتح نافذة إدارة الدرجات (للتوافق مع الكود القديم)"""
        self.open_grades_window()

    def open_reports_window(self):
        """فتح نافذة التقارير"""
        try:
            self.set_active_button('reports')
            self.close_all_windows()
            self.reports_window = ReportsWindow(self.auth_manager.db)
            self.reports_window.show()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في فتح نافذة التقارير: {str(e)}")

    def open_settings_window(self):
        """فتح نافذة الإعدادات"""
        try:
            self.set_active_button('settings')
            self.close_all_windows()
            self.settings_window = SettingsWindow(self.auth_manager.db, self.auth_manager)
            self.settings_window.show()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في فتح نافذة الإعدادات: {str(e)}")

    def logout(self):
        """تسجيل الخروج"""
        reply = QMessageBox.question(self, "تأكيد", "هل تريد تسجيل الخروج؟",
                                   QMessageBox.Yes | QMessageBox.No)
        if reply == QMessageBox.Yes:
            self.close_all_windows()
            self.logout_signal.emit()
    
    def open_reports(self):
        """فتح نافذة التقارير"""
        try:
            self.reports_window = ReportsWindow(self.auth_manager.db)
            self.reports_window.show()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في فتح نافذة التقارير: {str(e)}")
    
    def open_settings(self):
        """فتح نافذة الإعدادات"""
        try:
            self.settings_window = SettingsWindow(self.auth_manager.db, self.auth_manager)
            self.settings_window.show()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في فتح نافذة الإعدادات: {str(e)}")
    
    def logout(self):
        """تسجيل الخروج"""
        reply = QMessageBox.question(
            self, 'تأكيد تسجيل الخروج',
            'هل تريد تسجيل الخروج؟',
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            self.auth_manager.logout()
            self.logout_signal.emit()
    
    def show_about(self):
        """إظهار معلومات التطبيق"""
        QMessageBox.about(self, "حول التطبيق", 
                         "نظام إدارة الطلاب\n"
                         "مستر أحمد عادل\n"
                         "الإصدار 1.0\n"
                         "تم التطوير بواسطة: مساعد الذكي")
    
    def closeEvent(self, event):
        """معالجة إغلاق النافذة"""
        self.logout()
        event.accept()
