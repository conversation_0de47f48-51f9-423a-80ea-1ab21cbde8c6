#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام الفيديوهات التعليمية والامتحانات المحمية
Protected Educational Videos and Exams System

المطور: م/ حسام أسامة
مصمم لـ: مستر أحمد عادل - معلم الجغرافيا والتاريخ
"""

import sys
import os
import json
import sqlite3
import hashlib
import time
from datetime import datetime, timedelta
import subprocess
import threading
import psutil
import win32gui
import win32con
import win32api
from cryptography.fernet import Fernet

from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, 
                            QHBoxLayout, QLabel, QPushButton, QLineEdit, 
                            QMessageBox, QFrame, QProgressBar, QTextEdit,
                            QComboBox, QSpinBox, QCheckBox, QListWidget,
                            QTabWidget, QT<PERSON>Widget, QTable<PERSON><PERSON>tI<PERSON>,
                            QFileDialog, QDialog, QFormLayout, QDialogButtonBox)
from PyQt5.QtCore import Qt, QTimer, QThread, pyqtSignal, QUrl
from PyQt5.QtGui import QFont, QPixmap, QIcon, QKeySequence
from PyQt5.QtMultimedia import QMediaPlayer, QMediaContent
from PyQt5.QtMultimediaWidgets import QVideoWidget

class SecurityManager:
    """مدير الأمان والحماية"""
    
    def __init__(self):
        self.blocked_processes = [
            'obs64.exe', 'obs32.exe',  # OBS Studio
            'bandicam.exe',  # Bandicam
            'camtasia.exe',  # Camtasia
            'fraps.exe',  # Fraps
            'snagit32.exe', 'snagit64.exe',  # Snagit
            'screenrec.exe',  # Screen Recorder
            'hypercam.exe',  # HyperCam
            'debut.exe',  # Debut Video Capture
            'flashback.exe',  # FlashBack
            'activepresneter.exe',  # ActivePresenter
        ]
        self.monitoring = False
        
    def start_monitoring(self):
        """بدء مراقبة العمليات المحظورة"""
        self.monitoring = True
        self.monitor_thread = threading.Thread(target=self._monitor_processes)
        self.monitor_thread.daemon = True
        self.monitor_thread.start()
        
    def stop_monitoring(self):
        """إيقاف المراقبة"""
        self.monitoring = False
        
    def _monitor_processes(self):
        """مراقبة العمليات المحظورة"""
        while self.monitoring:
            try:
                for proc in psutil.process_iter(['pid', 'name']):
                    if proc.info['name'].lower() in [p.lower() for p in self.blocked_processes]:
                        # إنهاء العملية المحظورة
                        proc.terminate()
                        self._show_security_warning(f"تم إيقاف برنامج التسجيل: {proc.info['name']}")
                        
                # فحص لقطات الشاشة
                self._check_screenshot_attempts()
                
            except Exception as e:
                pass
                
            time.sleep(1)  # فحص كل ثانية
            
    def _check_screenshot_attempts(self):
        """فحص محاولات لقطة الشاشة"""
        # فحص مفاتيح لقطة الشاشة
        if win32api.GetAsyncKeyState(win32con.VK_SNAPSHOT):
            self._show_security_warning("محاولة أخذ لقطة شاشة محظورة!")
            
        # فحص Alt+PrintScreen
        if (win32api.GetAsyncKeyState(win32con.VK_MENU) and 
            win32api.GetAsyncKeyState(win32con.VK_SNAPSHOT)):
            self._show_security_warning("محاولة أخذ لقطة شاشة محظورة!")
            
    def _show_security_warning(self, message):
        """عرض تحذير أمني"""
        QMessageBox.critical(None, "تحذير أمني", 
                           f"🚨 {message}\n\n"
                           "تم رصد محاولة انتهاك لحقوق الملكية الفكرية!\n"
                           "سيتم إغلاق النظام فوراً.")
        sys.exit(1)
        
    def enable_fullscreen_lock(self, window):
        """تفعيل قفل الشاشة الكاملة"""
        window.showFullScreen()
        window.setWindowFlags(Qt.WindowStaysOnTopHint | Qt.FramelessWindowHint)
        
        # منع Alt+Tab
        window.setFocusPolicy(Qt.StrongFocus)
        
        # منع Escape
        window.keyPressEvent = self._block_escape
        
    def _block_escape(self, event):
        """منع مفتاح Escape"""
        if event.key() == Qt.Key_Escape:
            event.ignore()
        else:
            super().keyPressEvent(event)

class VideoEncryption:
    """تشفير الفيديوهات"""
    
    def __init__(self):
        self.key = self._generate_key()
        self.cipher = Fernet(self.key)
        
    def _generate_key(self):
        """توليد مفتاح التشفير"""
        # مفتاح ثابت مرتبط بالجهاز
        machine_id = self._get_machine_id()
        key = hashlib.sha256(machine_id.encode()).digest()
        return Fernet.generate_key()  # في الإنتاج، استخدم machine_id
        
    def _get_machine_id(self):
        """الحصول على معرف الجهاز"""
        try:
            import uuid
            return str(uuid.getnode())
        except:
            return "default_machine_id"
            
    def encrypt_video(self, video_path, output_path):
        """تشفير الفيديو"""
        try:
            with open(video_path, 'rb') as file:
                video_data = file.read()
                
            encrypted_data = self.cipher.encrypt(video_data)
            
            with open(output_path, 'wb') as file:
                file.write(encrypted_data)
                
            return True
        except Exception as e:
            print(f"خطأ في تشفير الفيديو: {e}")
            return False
            
    def decrypt_video(self, encrypted_path, output_path):
        """فك تشفير الفيديو"""
        try:
            with open(encrypted_path, 'rb') as file:
                encrypted_data = file.read()
                
            video_data = self.cipher.decrypt(encrypted_data)
            
            with open(output_path, 'wb') as file:
                file.write(video_data)
                
            return True
        except Exception as e:
            print(f"خطأ في فك تشفير الفيديو: {e}")
            return False

class VideoPlayer(QWidget):
    """مشغل الفيديو المحمي"""
    
    video_finished = pyqtSignal()
    
    def __init__(self, video_info, student_info):
        super().__init__()
        self.video_info = video_info
        self.student_info = student_info
        self.security_manager = SecurityManager()
        self.video_encryption = VideoEncryption()
        self.watch_count = 0
        self.max_watches = 3
        
        self.init_ui()
        self.init_security()
        
    def init_ui(self):
        """إعداد واجهة المشغل"""
        self.setWindowTitle(f"فيديو: {self.video_info['title']}")
        self.setStyleSheet("""
            QWidget {
                background-color: black;
                color: white;
            }
            QLabel {
                font-size: 16px;
                font-weight: bold;
            }
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 10px;
                border-radius: 5px;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        
        layout = QVBoxLayout()
        
        # معلومات الفيديو
        info_frame = QFrame()
        info_layout = QHBoxLayout()
        
        title_label = QLabel(f"📹 {self.video_info['title']}")
        info_layout.addWidget(title_label)
        
        student_label = QLabel(f"👤 {self.student_info['name']}")
        info_layout.addWidget(student_label)
        
        watch_label = QLabel(f"👁️ المشاهدة: {self.watch_count + 1}/{self.max_watches}")
        self.watch_label = watch_label
        info_layout.addWidget(watch_label)
        
        info_frame.setLayout(info_layout)
        layout.addWidget(info_frame)
        
        # مشغل الفيديو
        self.video_widget = QVideoWidget()
        self.media_player = QMediaPlayer()
        self.media_player.setVideoOutput(self.video_widget)
        self.media_player.mediaStatusChanged.connect(self.handle_media_status)
        self.media_player.positionChanged.connect(self.update_progress)
        
        layout.addWidget(self.video_widget)
        
        # شريط التقدم
        self.progress_bar = QProgressBar()
        layout.addWidget(self.progress_bar)
        
        # أزرار التحكم
        controls_layout = QHBoxLayout()
        
        self.play_button = QPushButton("▶️ تشغيل")
        self.play_button.clicked.connect(self.play_pause)
        controls_layout.addWidget(self.play_button)
        
        self.stop_button = QPushButton("⏹️ إيقاف")
        self.stop_button.clicked.connect(self.stop)
        controls_layout.addWidget(self.stop_button)
        
        # منع التقديم والترجيع
        self.play_button.setEnabled(True)
        self.stop_button.setEnabled(True)
        
        layout.addLayout(controls_layout)
        
        # تحذير
        warning_label = QLabel("⚠️ تحذير: هذا المحتوى محمي بحقوق الملكية الفكرية\n"
                              "أي محاولة تسجيل أو نسخ ستؤدي لإغلاق النظام فوراً")
        warning_label.setAlignment(Qt.AlignCenter)
        warning_label.setStyleSheet("color: red; font-size: 14px; font-weight: bold;")
        layout.addWidget(warning_label)
        
        self.setLayout(layout)
        
    def init_security(self):
        """تهيئة الأمان"""
        # بدء مراقبة الأمان
        self.security_manager.start_monitoring()
        
        # قفل الشاشة الكاملة
        self.security_manager.enable_fullscreen_lock(self)
        
        # منع النقر بالزر الأيمن
        self.setContextMenuPolicy(Qt.NoContextMenu)
        
    def load_video(self):
        """تحميل الفيديو"""
        try:
            # فحص عدد المشاهدات
            if self.watch_count >= self.max_watches:
                QMessageBox.critical(self, "انتهت المشاهدات", 
                                   f"لقد استنفدت عدد المشاهدات المسموحة ({self.max_watches})")
                self.close()
                return
                
            # فك تشفير الفيديو مؤقتاً
            encrypted_path = self.video_info['file_path']
            temp_path = f"temp_video_{int(time.time())}.mp4"
            
            if self.video_encryption.decrypt_video(encrypted_path, temp_path):
                # تحميل الفيديو
                media_content = QMediaContent(QUrl.fromLocalFile(os.path.abspath(temp_path)))
                self.media_player.setMedia(media_content)
                
                # حذف الملف المؤقت بعد التحميل
                self.temp_video_path = temp_path
                
                # تحديث عداد المشاهدات
                self.watch_count += 1
                self.watch_label.setText(f"👁️ المشاهدة: {self.watch_count}/{self.max_watches}")
                
                # حفظ عداد المشاهدات
                self.save_watch_count()
                
            else:
                QMessageBox.critical(self, "خطأ", "فشل في تحميل الفيديو")
                
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تحميل الفيديو: {str(e)}")
            
    def save_watch_count(self):
        """حفظ عداد المشاهدات"""
        try:
            # حفظ في قاعدة البيانات أو ملف
            watch_data = {
                'student_id': self.student_info['id'],
                'video_id': self.video_info['id'],
                'watch_count': self.watch_count,
                'last_watch': datetime.now().isoformat()
            }
            
            # حفظ في ملف JSON مؤقتاً
            watch_file = f"watch_data_{self.student_info['id']}_{self.video_info['id']}.json"
            with open(watch_file, 'w', encoding='utf-8') as f:
                json.dump(watch_data, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            print(f"خطأ في حفظ عداد المشاهدات: {e}")
            
    def play_pause(self):
        """تشغيل/إيقاف مؤقت"""
        if self.media_player.state() == QMediaPlayer.PlayingState:
            self.media_player.pause()
            self.play_button.setText("▶️ تشغيل")
        else:
            self.media_player.play()
            self.play_button.setText("⏸️ إيقاف مؤقت")
            
    def stop(self):
        """إيقاف"""
        self.media_player.stop()
        self.play_button.setText("▶️ تشغيل")
        
    def handle_media_status(self, status):
        """التعامل مع حالة الوسائط"""
        if status == QMediaPlayer.EndOfMedia:
            # انتهى الفيديو
            self.video_finished.emit()
            self.cleanup_temp_files()
            
    def update_progress(self, position):
        """تحديث شريط التقدم"""
        duration = self.media_player.duration()
        if duration > 0:
            progress = (position / duration) * 100
            self.progress_bar.setValue(int(progress))
            
    def cleanup_temp_files(self):
        """تنظيف الملفات المؤقتة"""
        try:
            if hasattr(self, 'temp_video_path') and os.path.exists(self.temp_video_path):
                os.remove(self.temp_video_path)
        except Exception as e:
            print(f"خطأ في حذف الملف المؤقت: {e}")
            
    def closeEvent(self, event):
        """عند إغلاق النافذة"""
        self.security_manager.stop_monitoring()
        self.cleanup_temp_files()
        event.accept()
        
    def keyPressEvent(self, event):
        """منع مفاتيح معينة"""
        blocked_keys = [
            Qt.Key_Escape,  # منع Escape
            Qt.Key_F11,     # منع F11
            Qt.Key_Alt,     # منع Alt
            Qt.Key_Tab,     # منع Tab
            Qt.Key_Print,   # منع Print Screen
        ]
        
        if event.key() in blocked_keys:
            event.ignore()
            self.security_manager._show_security_warning("محاولة استخدام مفتاح محظور!")
        else:
            super().keyPressEvent(event)

class ExamSystem(QWidget):
    """نظام الامتحانات"""
    
    exam_finished = pyqtSignal(dict)
    
    def __init__(self, exam_info, student_info):
        super().__init__()
        self.exam_info = exam_info
        self.student_info = student_info
        self.questions = []
        self.current_question = 0
        self.answers = {}
        self.start_time = datetime.now()
        
        self.init_ui()
        self.load_questions()
        
    def init_ui(self):
        """إعداد واجهة الامتحان"""
        self.setWindowTitle(f"امتحان: {self.exam_info['title']}")
        self.setStyleSheet("""
            QWidget {
                background-color: #f8f9fa;
                font-family: 'Segoe UI';
            }
            QLabel {
                color: #2c3e50;
                font-size: 14px;
            }
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 10px;
                border-radius: 5px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
            QCheckBox, QRadioButton {
                font-size: 14px;
                padding: 5px;
            }
        """)
        
        layout = QVBoxLayout()
        
        # معلومات الامتحان
        header = QFrame()
        header.setStyleSheet("background-color: #2c3e50; color: white; padding: 15px; border-radius: 5px;")
        header_layout = QVBoxLayout()
        
        title_label = QLabel(f"📝 {self.exam_info['title']}")
        title_label.setStyleSheet("font-size: 18px; font-weight: bold;")
        header_layout.addWidget(title_label)
        
        info_label = QLabel(f"👤 الطالب: {self.student_info['name']} | "
                           f"📚 المادة: {self.exam_info['subject']} | "
                           f"⏱️ المدة: {self.exam_info['duration']} دقيقة")
        header_layout.addWidget(info_label)
        
        header.setLayout(header_layout)
        layout.addWidget(header)
        
        # منطقة الأسئلة
        self.questions_widget = QWidget()
        layout.addWidget(self.questions_widget)
        
        # أزرار التنقل
        nav_layout = QHBoxLayout()
        
        self.prev_button = QPushButton("⬅️ السابق")
        self.prev_button.clicked.connect(self.previous_question)
        nav_layout.addWidget(self.prev_button)
        
        self.question_label = QLabel("السؤال 1 من 10")
        self.question_label.setAlignment(Qt.AlignCenter)
        nav_layout.addWidget(self.question_label)
        
        self.next_button = QPushButton("➡️ التالي")
        self.next_button.clicked.connect(self.next_question)
        nav_layout.addWidget(self.next_button)
        
        layout.addLayout(nav_layout)
        
        # زر إنهاء الامتحان
        self.finish_button = QPushButton("✅ إنهاء الامتحان")
        self.finish_button.clicked.connect(self.finish_exam)
        layout.addWidget(self.finish_button)
        
        self.setLayout(layout)
        
    def load_questions(self):
        """تحميل الأسئلة"""
        # أسئلة تجريبية
        self.questions = [
            {
                'id': 1,
                'type': 'multiple_choice',
                'question': 'ما هي عاصمة مصر؟',
                'options': ['القاهرة', 'الإسكندرية', 'الجيزة', 'أسوان'],
                'correct_answer': 0
            },
            {
                'id': 2,
                'type': 'true_false',
                'question': 'نهر النيل هو أطول نهر في العالم.',
                'correct_answer': True
            },
            {
                'id': 3,
                'type': 'multiple_choice',
                'question': 'في أي قارة تقع مصر؟',
                'options': ['آسيا', 'أفريقيا', 'أوروبا', 'أمريكا'],
                'correct_answer': 1
            }
        ]
        
        self.show_question()
        
    def show_question(self):
        """عرض السؤال الحالي"""
        if self.current_question >= len(self.questions):
            return
            
        question = self.questions[self.current_question]
        
        # مسح المحتوى السابق
        for i in reversed(range(self.questions_widget.layout().count() if self.questions_widget.layout() else 0)):
            self.questions_widget.layout().itemAt(i).widget().setParent(None)
            
        if not self.questions_widget.layout():
            self.questions_widget.setLayout(QVBoxLayout())
            
        layout = self.questions_widget.layout()
        
        # نص السؤال
        question_text = QLabel(f"السؤال {self.current_question + 1}: {question['question']}")
        question_text.setStyleSheet("font-size: 16px; font-weight: bold; margin: 10px;")
        question_text.setWordWrap(True)
        layout.addWidget(question_text)
        
        # خيارات الإجابة
        if question['type'] == 'multiple_choice':
            self.show_multiple_choice(question, layout)
        elif question['type'] == 'true_false':
            self.show_true_false(question, layout)
            
        # تحديث التنقل
        self.update_navigation()
        
    def show_multiple_choice(self, question, layout):
        """عرض سؤال اختيار متعدد"""
        from PyQt5.QtWidgets import QRadioButton, QButtonGroup
        
        self.answer_group = QButtonGroup()
        
        for i, option in enumerate(question['options']):
            radio = QRadioButton(option)
            self.answer_group.addButton(radio, i)
            layout.addWidget(radio)
            
            # استرجاع الإجابة المحفوظة
            if self.current_question in self.answers:
                if self.answers[self.current_question] == i:
                    radio.setChecked(True)
                    
        self.answer_group.buttonClicked.connect(self.save_answer)
        
    def show_true_false(self, question, layout):
        """عرض سؤال صح/خطأ"""
        from PyQt5.QtWidgets import QRadioButton, QButtonGroup
        
        self.answer_group = QButtonGroup()
        
        true_radio = QRadioButton("صح")
        false_radio = QRadioButton("خطأ")
        
        self.answer_group.addButton(true_radio, 1)
        self.answer_group.addButton(false_radio, 0)
        
        layout.addWidget(true_radio)
        layout.addWidget(false_radio)
        
        # استرجاع الإجابة المحفوظة
        if self.current_question in self.answers:
            if self.answers[self.current_question]:
                true_radio.setChecked(True)
            else:
                false_radio.setChecked(True)
                
        self.answer_group.buttonClicked.connect(self.save_answer)
        
    def save_answer(self):
        """حفظ الإجابة"""
        if hasattr(self, 'answer_group'):
            checked_button = self.answer_group.checkedButton()
            if checked_button:
                answer_id = self.answer_group.id(checked_button)
                self.answers[self.current_question] = answer_id
                
    def previous_question(self):
        """السؤال السابق"""
        if self.current_question > 0:
            self.current_question -= 1
            self.show_question()
            
    def next_question(self):
        """السؤال التالي"""
        if self.current_question < len(self.questions) - 1:
            self.current_question += 1
            self.show_question()
            
    def update_navigation(self):
        """تحديث أزرار التنقل"""
        self.prev_button.setEnabled(self.current_question > 0)
        self.next_button.setEnabled(self.current_question < len(self.questions) - 1)
        self.question_label.setText(f"السؤال {self.current_question + 1} من {len(self.questions)}")
        
    def finish_exam(self):
        """إنهاء الامتحان"""
        # التأكد من الإجابة على جميع الأسئلة
        unanswered = []
        for i in range(len(self.questions)):
            if i not in self.answers:
                unanswered.append(i + 1)
                
        if unanswered:
            reply = QMessageBox.question(self, "أسئلة غير مجابة", 
                                       f"لم تجب على الأسئلة: {', '.join(map(str, unanswered))}\n"
                                       "هل تريد إنهاء الامتحان؟",
                                       QMessageBox.Yes | QMessageBox.No)
            if reply == QMessageBox.No:
                return
                
        # حساب النتيجة
        score = self.calculate_score()
        
        # إنهاء الامتحان
        end_time = datetime.now()
        duration = end_time - self.start_time
        
        result = {
            'student_info': self.student_info,
            'exam_info': self.exam_info,
            'answers': self.answers,
            'score': score,
            'total_questions': len(self.questions),
            'start_time': self.start_time.isoformat(),
            'end_time': end_time.isoformat(),
            'duration': str(duration)
        }
        
        self.exam_finished.emit(result)
        self.close()
        
    def calculate_score(self):
        """حساب النتيجة"""
        correct_answers = 0
        
        for i, question in enumerate(self.questions):
            if i in self.answers:
                user_answer = self.answers[i]
                correct_answer = question['correct_answer']
                
                if question['type'] == 'multiple_choice':
                    if user_answer == correct_answer:
                        correct_answers += 1
                elif question['type'] == 'true_false':
                    if (user_answer == 1) == correct_answer:
                        correct_answers += 1
                        
        return correct_answers

def main():
    """الدالة الرئيسية"""
    app = QApplication(sys.argv)
    
    # بيانات تجريبية
    video_info = {
        'id': 1,
        'title': 'الجغرافيا - الدرس الأول',
        'file_path': 'encrypted_video.enc',
        'subject': 'جغرافيا',
        'grade': 'الثاني الثانوي'
    }
    
    student_info = {
        'id': 1,
        'name': 'أحمد محمد',
        'code': 'ST001',
        'grade': 'الثاني الثانوي'
    }
    
    exam_info = {
        'id': 1,
        'title': 'امتحان الجغرافيا - الوحدة الأولى',
        'subject': 'جغرافيا',
        'duration': 30
    }
    
    # تشغيل نظام الامتحان (للاختبار)
    exam_window = ExamSystem(exam_info, student_info)
    exam_window.show()
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
