@echo off
chcp 65001 >nul
title بناء تطبيق مستقل - نظام إدارة الطلاب

cls
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                بناء تطبيق مستقل (EXE)                      ║
echo ║                نظام إدارة الطلاب                           ║
echo ║                مستر أحمد عادل                              ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 🔍 التحقق من متطلبات البناء...
echo.

REM التحقق من وجود Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ خطأ: Python غير مثبت
    echo.
    echo 📥 يرجى تحميل وتثبيت Python من:
    echo    https://python.org/downloads
    echo.
    echo 💡 تأكد من تحديد "Add Python to PATH" أثناء التثبيت
    echo.
    pause
    exit /b 1
)

echo ✅ Python متوفر
echo.

REM التحقق من pip
pip --version >nul 2>&1
if errorlevel 1 (
    echo ❌ خطأ: pip غير متوفر
    pause
    exit /b 1
)

echo ✅ pip متوفر
echo.

REM تثبيت PyInstaller
echo 📦 التحقق من PyInstaller...
python -c "import PyInstaller" 2>nul
if errorlevel 1 (
    echo 📥 تثبيت PyInstaller...
    pip install pyinstaller
    if errorlevel 1 (
        echo ❌ فشل في تثبيت PyInstaller
        pause
        exit /b 1
    )
    echo ✅ تم تثبيت PyInstaller
) else (
    echo ✅ PyInstaller موجود
)

echo.

REM تثبيت المتطلبات
echo 📋 تثبيت المتطلبات...
if exist "requirements.txt" (
    pip install -r requirements.txt --quiet
    echo ✅ تم تثبيت المتطلبات
) else (
    echo ⚠️ ملف requirements.txt غير موجود
)

echo.

REM إنشاء ملف spec
echo 📝 إنشاء ملف التكوين...

(
echo # -*- mode: python ; coding: utf-8 -*-
echo.
echo block_cipher = None
echo.
echo a = Analysis^(
echo     ['main.py'],
echo     pathex=[],
echo     binaries=[],
echo     datas=[
echo         ^('src', 'src'^),
echo         ^('data', 'data'^),
echo     ],
echo     hiddenimports=[
echo         'PyQt5.QtCore',
echo         'PyQt5.QtGui', 
echo         'PyQt5.QtWidgets',
echo         'sqlite3',
echo         'datetime',
echo         'pathlib',
echo         'hashlib',
echo     ],
echo     hookspath=[],
echo     runtime_hooks=[],
echo     excludes=[
echo         'tkinter',
echo         'matplotlib',
echo         'numpy',
echo     ],
echo     win_no_prefer_redirects=False,
echo     win_private_assemblies=False,
echo     cipher=block_cipher,
echo     noarchive=False,
echo ^)
echo.
echo pyz = PYZ^(a.pure, a.zipped_data, cipher=block_cipher^)
echo.
echo exe = EXE^(
echo     pyz,
echo     a.scripts,
echo     a.binaries,
echo     a.zipfiles,
echo     a.datas,
echo     [],
echo     name='StudentManagementSystem',
echo     debug=False,
echo     bootloader_ignore_signals=False,
echo     strip=False,
echo     upx=True,
echo     runtime_tmpdir=None,
echo     console=False,
echo     disable_windowed_traceback=False,
echo     target_arch=None,
echo ^)
) > app_standalone.spec

echo ✅ تم إنشاء ملف التكوين

echo.
echo 🏗️ بدء بناء ملف EXE...
echo هذا قد يستغرق عدة دقائق...
echo.

REM بناء التطبيق
pyinstaller --clean --noconfirm app_standalone.spec

REM التحقق من النجاح
if exist "dist\StudentManagementSystem.exe" (
    echo.
    echo ╔══════════════════════════════════════════════════════════════╗
    echo ║                      تم البناء بنجاح!                       ║
    echo ╚══════════════════════════════════════════════════════════════╝
    echo.
    
    REM حساب حجم الملف
    for %%A in ("dist\StudentManagementSystem.exe") do set "file_size=%%~zA"
    set /a "file_size_mb=%file_size% / 1048576"
    
    echo 📦 ملف EXE: dist\StudentManagementSystem.exe
    echo 📏 حجم الملف: %file_size_mb% ميجابايت تقريباً
    echo 📁 مجلد التوزيع: dist\
    echo.
    
    REM إنشاء ملف تعليمات
    (
    echo # نظام إدارة الطلاب - مستر أحمد عادل
    echo.
    echo ## التشغيل:
    echo 1. انقر مرتين على StudentManagementSystem.exe
    echo 2. استخدم admin/admin123 لتسجيل الدخول
    echo.
    echo ## المميزات:
    echo - إدارة الطلاب
    echo - تسجيل الحضور  
    echo - إدارة الدرجات
    echo - التقارير
    echo.
    echo تم التطوير لمستر أحمد عادل
    echo معلم الجغرافيا والتاريخ
    ) > "dist\README.txt"
    
    echo ✅ تم إنشاء ملف التعليمات
    echo.
    
    REM نسخ الملفات المهمة
    if exist "USER_GUIDE.md" copy "USER_GUIDE.md" "dist\" >nul
    if exist "data" xcopy "data" "dist\data\" /E /I /Q >nul
    
    echo 📋 تعليمات الاستخدام:
    echo.
    echo 1. انسخ مجلد dist بالكامل إلى أي مكان
    echo 2. انقر مرتين على StudentManagementSystem.exe
    echo 3. استخدم admin/admin123 لتسجيل الدخول
    echo 4. استمتع بالتطبيق!
    echo.
    echo 🎓 التطبيق جاهز للاستخدام بدون الحاجة لـ Python!
    echo.
    
    REM فتح مجلد التوزيع
    explorer "dist"
    
) else (
    echo.
    echo ❌ فشل في بناء ملف EXE
    echo.
    echo 🔍 تحقق من الأخطاء أعلاه
    echo 💡 تأكد من وجود جميع الملفات المطلوبة
    echo.
)

REM تنظيف الملفات المؤقتة
if exist "build" rmdir /s /q "build" >nul 2>&1
if exist "app_standalone.spec" del "app_standalone.spec" >nul 2>&1

echo.
pause
