@echo off
chcp 65001 >nul
title نظام إدارة الطلاب مع الربط بتليجرام - مستر أحمد عادل

cls
color 0D
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║            نظام إدارة الطلاب مع الربط بتليجرام              ║
echo ║                   مستر أحمد عادل                           ║
echo ║              معلم الجغرافيا والتاريخ                        ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 📱 النظام المتكامل مع تليجرام:
echo.
echo ✅ إدارة الطلاب مع QR Code تلقائي
echo ✅ حضور بالاسم والكود
echo ✅ إدارة الفيديوهات التعليمية المحمية
echo ✅ 📱 الربط مع تليجرام لرفع الفيديوهات
echo ✅ 🤖 بوت تليجرام للتحكم عن بُعد
echo ✅ 📤 رفع الفيديوهات مباشرة من تليجرام
echo ✅ 🔄 مزامنة تلقائية مع القناة/البوت
echo.

echo 🚀 تشغيل النظام مع تليجرام...
echo.

REM التحقق من Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير موجود
    echo 🏪 فتح Microsoft Store لتثبيت Python...
    start ms-windows-store://pdp/?ProductId=9NRWMJP3717K
    goto end
)

echo ✅ Python موجود
python --version

echo.
echo 📦 تثبيت المكتبات المطلوبة مع تليجرام...

REM تثبيت المكتبات الأساسية
python -m pip install --quiet PyQt5

REM تثبيت مكتبات QR Code
python -m pip install --quiet qrcode[pil] Pillow

REM تثبيت مكتبة تليجرام
echo 📱 تثبيت مكتبة تليجرام...
python -m pip install --quiet python-telegram-bot

REM تثبيت مكتبات إضافية
python -m pip install --quiet requests aiohttp

echo ✅ تم تثبيت جميع المكتبات

echo.
echo 🎯 تشغيل النظام مع تليجرام...
echo.

python main.py

:end
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                  النظام المتكامل مع تليجرام                 ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.
echo 🔐 بيانات تسجيل الدخول:
echo    👤 اسم المستخدم: admin
echo    🔑 كلمة المرور: admin123
echo.
echo 📱 إعداد تليجرام:
echo.
echo 1️⃣ إنشاء بوت تليجرام:
echo    🤖 ابحث عن @BotFather في تليجرام
echo    💬 أرسل /newbot
echo    📝 اتبع التعليمات
echo    🔑 احفظ التوكن المُعطى
echo.
echo 2️⃣ إنشاء قناة/مجموعة:
echo    📺 أنشئ قناة أو مجموعة جديدة
echo    🤖 أضف البوت كمشرف
echo    🔗 احفظ معرف القناة (مثال: @mychannel)
echo.
echo 3️⃣ ربط النظام بتليجرام:
echo    🎯 اذهب لـ "إدارة الفيديوهات"
echo    📱 تبويب "الربط بتليجرام"
echo    🔑 أدخل توكن البوت
echo    📺 أدخل معرف القناة
echo    🔗 اضغط "اختبار الاتصال"
echo    💾 اضغط "حفظ الإعدادات"
echo.
echo 📤 رفع الفيديوهات من تليجرام:
echo.
echo 1️⃣ أرسل الفيديو للقناة/البوت
echo 2️⃣ أضف وصف بالتنسيق التالي:
echo    العنوان: [عنوان الفيديو]
echo    المادة: [جغرافيا/تاريخ]
echo    الصف: [الصف الدراسي]
echo    الترم: [الترم الأول/الثاني]
echo.
echo 3️⃣ في النظام:
echo    📱 اذهب لـ "الربط بتليجرام"
echo    📥 اضغط "تحديث الفيديوهات من تليجرام"
echo    🔐 سيتم تشفير الفيديوهات تلقائياً
echo    ✅ ستظهر في قائمة الفيديوهات
echo.
echo 🔄 المزامنة التلقائية:
echo    ✅ فعّل "مزامنة تلقائية كل 5 دقائق"
echo    📥 سيتم جلب الفيديوهات الجديدة تلقائياً
echo    🔐 تشفير وحماية تلقائية
echo    📊 تحديث الإحصائيات
echo.
echo 🛡️ الحماية المتقدمة:
echo    🔒 تشفير الفيديوهات المجلبة من تليجرام
echo    👁️ حد أقصى 3 مشاهدات لكل فيديو
echo    🚫 منع تسجيل الشاشة والنسخ
echo    📝 امتحانات تلقائية بعد المشاهدة
echo    📊 تتبع مشاهدات الطلاب
echo.
echo 📊 المميزات الجديدة:
echo    📱 ربط مباشر مع تليجرام
echo    🤖 بوت ذكي لإدارة الفيديوهات
echo    📤 رفع سهل من الهاتف
echo    🔄 مزامنة تلقائية
echo    📋 سجل أنشطة مفصل
echo    ⚙️ إعدادات قابلة للحفظ
echo.
echo 🎯 نصائح مهمة:
echo    • احتفظ بتوكن البوت في مكان آمن
echo    • لا تشارك التوكن مع أحد
echo    • تأكد من إضافة البوت كمشرف للقناة
echo    • استخدم أسماء واضحة للفيديوهات
echo    • اتبع تنسيق الوصف بدقة
echo.
echo 🎓 تم التطوير بواسطة: م/ حسام أسامة
echo    مهندس برمجيات - مطور تطبيقات
echo    مصمم خصيصاً لمستر أحمد عادل
echo.
echo 🏆 أول نظام إدارة طلاب عربي مع ربط تليجرام!
echo.
pause
