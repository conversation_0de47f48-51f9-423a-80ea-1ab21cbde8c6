# دليل المستخدم - نظام إدارة الطلاب

## 📖 مقدمة

مرحباً بك في نظام إدارة الطلاب المصمم خصيصاً لمستر أحمد عادل. هذا الدليل سيساعدك على استخدام جميع مميزات التطبيق بفعالية.

## 🚀 البدء السريع

### تشغيل التطبيق
1. انقر مرتين على ملف `start.bat` (Windows)
2. أو شغل `python main.py` من سطر الأوامر

### تسجيل الدخول
- **اسم المستخدم:** `admin`
- **كلمة المرور:** `admin123`

## 🎓 إدارة الطلاب

### إضافة طالب جديد

1. **فتح نافذة إدارة الطلاب:**
   - من الشاشة الرئيسية، انقر على "إدارة الطلاب"

2. **ملء بيانات الطالب:**
   - انقر على "توليد كود جديد" للحصول على كود تلقائي
   - أدخل الاسم الرباعي للطالب
   - اختر النوع (ذكر/أنثى)
   - اختر المرحلة (إعدادي/ثانوي)
   - اختر الصف المناسب
   - أدخل الدرجات الحالية (اختياري)

3. **حفظ البيانات:**
   - انقر على "إضافة طالب"
   - ستظهر رسالة تأكيد عند النجاح

### تعديل بيانات طالب

1. **البحث عن الطالب:**
   - استخدم مربع البحث لكتابة اسم أو كود الطالب
   - أو تصفح القائمة مباشرة

2. **تحديد الطالب:**
   - انقر على الطالب في الجدول لتحديده
   - ستظهر بياناته في النموذج الجانبي

3. **تعديل البيانات:**
   - عدل المعلومات المطلوبة
   - انقر على "تحديث البيانات"

### حذف طالب

1. حدد الطالب من الجدول
2. انقر على "حذف الطالب"
3. أكد الحذف في النافذة المنبثقة

⚠️ **تحذير:** حذف الطالب سيحذف جميع بياناته بما في ذلك الحضور والدرجات

## 📅 تسجيل الحضور

### التسجيل السريع

1. **فتح نافذة الحضور:**
   - من الشاشة الرئيسية، انقر على "تسجيل الحضور"

2. **التسجيل بالكود:**
   - في تبويب "تسجيل سريع"
   - أدخل كود الطالب في المربع
   - اختر الحالة (حاضر/غائب/متأخر)
   - أضف ملاحظات إن أردت
   - اضغط Enter أو انقر "تسجيل حضور"

### إدارة الحضور اليومي

1. **عرض الحضور:**
   - انتقل لتبويب "إدارة الحضور"
   - اختر التاريخ المطلوب
   - ستظهر قائمة بجميع الطلاب وحالة حضورهم

2. **تعديل حالة الحضور:**
   - انقر مرتين على أي طالب في الجدول
   - غير الحالة في النافذة المنبثقة
   - أضف ملاحظات إن لزم الأمر

3. **العمليات المجمعة:**
   - "تسجيل الكل حاضر": لتسجيل جميع الطلاب كحاضرين
   - "تسجيل المحدد غائب": لتسجيل الطلاب المحددين كغائبين

### تقارير الحضور

1. **انتقل لتبويب "تقارير الحضور"**
2. **حدد الفترة الزمنية:**
   - تاريخ البداية والنهاية
   - المرحلة (اختياري)

3. **إنشاء التقرير:**
   - انقر على "إنشاء التقرير"
   - راجع الإحصائيات المعروضة

## 📊 إدارة الدرجات

### إضافة درجة جديدة

1. **فتح نافذة الدرجات:**
   - من الشاشة الرئيسية، انقر على "إدارة الدرجات"

2. **في تبويب "إدخال الدرجات":**
   - اختر الطالب من القائمة المنسدلة
   - اختر المادة (جغرافيا/تاريخ)
   - أدخل نوع الامتحان (مثال: امتحان شهري، واجب، مشاركة)
   - أدخل الدرجة المحصلة والدرجة الكاملة
   - اختر تاريخ الامتحان
   - أضف ملاحظات إن أردت

3. **حفظ الدرجة:**
   - انقر على "إضافة الدرجة"
   - سيتم تحديث متوسط الطالب تلقائياً

### عرض وتعديل الدرجات

1. **عرض درجات طالب معين:**
   - اختر الطالب من القائمة
   - ستظهر جميع درجاته في الجدول الجانبي

2. **تعديل درجة:**
   - انقر على الدرجة في الجدول لتحديدها
   - عدل البيانات في النموذج
   - انقر على "تحديث الدرجة"

3. **حذف درجة:**
   - حدد الدرجة من الجدول
   - انقر على "حذف الدرجة"

### عرض جميع الدرجات

1. **انتقل لتبويب "عرض الدرجات"**
2. **استخدم الفلاتر:**
   - المادة (الكل/جغرافيا/تاريخ)
   - الصف
   - نوع الامتحان

3. **تصدير البيانات:**
   - انقر على "تصدير لـ Excel" لحفظ الدرجات

## 📈 التقارير

### أنواع التقارير المتاحة

1. **تقرير الطلاب:**
   - إحصائيات شاملة عن الطلاب
   - تصنيف حسب المرحلة والصف

2. **تقرير الحضور:**
   - إحصائيات الحضور لفترة محددة
   - معدلات الحضور اليومية

3. **تقرير الدرجات:**
   - أداء الطلاب في المواد
   - قوائم أفضل الطلاب

4. **التقرير الشامل:**
   - يجمع جميع الإحصائيات في تقرير واحد

### إنشاء تقرير

1. **فتح نافذة التقارير:**
   - من الشاشة الرئيسية، انقر على "التقارير"

2. **اختيار نوع التقرير:**
   - انتقل للتبويب المناسب

3. **تحديد الإعدادات:**
   - اختر الفلاتر المطلوبة (المرحلة، التاريخ، المادة)
   - حدد الخيارات الإضافية

4. **إنشاء التقرير:**
   - انقر على زر "إنشاء التقرير"
   - انتظر حتى يكتمل التحميل

5. **تصدير أو طباعة:**
   - "تصدير PDF": لحفظ التقرير كملف PDF
   - "تصدير Excel": لحفظ البيانات في جدول بيانات
   - "طباعة": لطباعة التقرير مباشرة

## ⚙️ الإعدادات

### الإعدادات العامة

1. **فتح نافذة الإعدادات:**
   - من الشاشة الرئيسية، انقر على "الإعدادات"

2. **في تبويب "الإعدادات العامة":**
   - عدل اسم المدرسة
   - حدث العام الدراسي
   - اختر إعدادات التطبيق

### إعدادات المعلم

1. **في تبويب "إعدادات المعلم":**
   - عدل اسم المعلم
   - أضف البريد الإلكتروني والهاتف
   - حدد إعدادات المواد ودرجة النجاح

### إدارة قاعدة البيانات

1. **في تبويب "قاعدة البيانات":**
   - راجع معلومات قاعدة البيانات
   - أنشئ نسخة احتياطية
   - استعد من نسخة احتياطية سابقة
   - حسن أداء قاعدة البيانات

### الأمان

1. **في تبويب "الأمان":**
   - غير كلمة المرور
   - حدد إعدادات الأمان

## 🔧 حل المشاكل الشائعة

### التطبيق لا يبدأ
1. تأكد من تثبيت Python و PyQt5
2. شغل `python fix_imports.py`
3. تحقق من ملف `logs/` للأخطاء

### مشاكل قاعدة البيانات
1. أنشئ نسخة احتياطية من البيانات
2. احذف ملف `data/students.db`
3. أعد تشغيل التطبيق

### مشاكل الخطوط العربية
1. تأكد من تثبيت خطوط عربية على النظام
2. في الإعدادات، جرب خطوط مختلفة

### بطء في الأداء
1. في الإعدادات، انقر على "تحسين قاعدة البيانات"
2. انقر على "ضغط قاعدة البيانات"

## 💡 نصائح للاستخدام الأمثل

### تسجيل الحضور
- استخدم التسجيل السريع بالكود لتوفير الوقت
- سجل الحضور في بداية كل حصة
- راجع تقارير الحضور أسبوعياً

### إدارة الدرجات
- أدخل الدرجات فور الانتهاء من التصحيح
- استخدم أنواع امتحانات واضحة ومتسقة
- راجع إحصائيات الدرجات لتتبع أداء الطلاب

### النسخ الاحتياطي
- أنشئ نسخة احتياطية أسبوعياً على الأقل
- احفظ النسخ الاحتياطية في مكان آمن
- اختبر استعادة النسخ الاحتياطية دورياً

### التقارير
- أنشئ تقارير شهرية لمتابعة التقدم
- استخدم التقارير في اجتماعات أولياء الأمور
- احفظ التقارير المهمة كملفات PDF

## 📞 الحصول على المساعدة

إذا واجهت أي مشكلة أو كان لديك استفسار:

1. راجع هذا الدليل أولاً
2. تحقق من ملف `logs/` للأخطاء التقنية
3. شغل `python test_app.py` للتشخيص
4. تواصل مع مطور التطبيق

---

**نتمنى لك تجربة ممتعة ومفيدة مع نظام إدارة الطلاب! 🎓**
