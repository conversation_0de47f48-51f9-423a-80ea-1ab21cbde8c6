#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إصلاح مشاكل الاستيراد
Fix Import Issues

هذا الملف يصلح مشاكل الاستيراد الشائعة في التطبيق
"""

import sys
import os
from pathlib import Path

def fix_import_paths():
    """إصلاح مسارات الاستيراد"""
    project_root = Path(__file__).parent
    src_path = project_root / 'src'
    
    # إضافة المسارات إلى sys.path
    paths_to_add = [
        str(project_root),
        str(src_path),
        str(src_path / 'ui'),
        str(src_path / 'models'),
        str(src_path / 'database'),
        str(src_path / 'utils'),
        str(src_path / 'reports')
    ]
    
    for path in paths_to_add:
        if path not in sys.path:
            sys.path.insert(0, path)
    
    print("✅ تم إصلاح مسارات الاستيراد")

def check_missing_files():
    """التحقق من الملفات المفقودة"""
    project_root = Path(__file__).parent
    
    required_files = [
        'main.py',
        'src/__init__.py',
        'src/ui/__init__.py',
        'src/models/__init__.py',
        'src/database/__init__.py',
        'src/utils/__init__.py',
        'src/reports/__init__.py',
        'src/database/database_manager.py',
        'src/models/student.py',
        'src/models/attendance.py',
        'src/models/grades.py',
        'src/utils/auth.py',
        'src/ui/login_window.py',
        'src/ui/main_window.py'
    ]
    
    missing_files = []
    for file_path in required_files:
        full_path = project_root / file_path
        if not full_path.exists():
            missing_files.append(file_path)
    
    if missing_files:
        print("❌ الملفات المفقودة:")
        for file in missing_files:
            print(f"   - {file}")
        return False
    else:
        print("✅ جميع الملفات المطلوبة موجودة")
        return True

def fix_circular_imports():
    """إصلاح مشاكل الاستيراد الدائري"""
    print("🔄 فحص الاستيرادات الدائرية...")
    
    # قائمة بالملفات التي قد تحتوي على استيرادات دائرية
    files_to_check = [
        'src/ui/login_window.py',
        'src/ui/main_window.py',
        'src/models/student.py',
        'src/models/attendance.py',
        'src/models/grades.py'
    ]
    
    project_root = Path(__file__).parent
    issues_found = []
    
    for file_path in files_to_check:
        full_path = project_root / file_path
        if full_path.exists():
            try:
                with open(full_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # فحص الاستيرادات المشكوك فيها
                if 'from ..ui.main_window import MainWindow' in content and 'login_window' in file_path:
                    issues_found.append(f"{file_path}: استيراد دائري محتمل مع main_window")
                
            except Exception as e:
                print(f"⚠️ خطأ في فحص {file_path}: {e}")
    
    if issues_found:
        print("⚠️ مشاكل محتملة في الاستيراد:")
        for issue in issues_found:
            print(f"   - {issue}")
    else:
        print("✅ لا توجد مشاكل استيراد دائري واضحة")

def create_missing_init_files():
    """إنشاء ملفات __init__.py المفقودة"""
    project_root = Path(__file__).parent
    
    directories = [
        'src',
        'src/ui',
        'src/models',
        'src/database',
        'src/utils',
        'src/reports'
    ]
    
    for directory in directories:
        dir_path = project_root / directory
        init_file = dir_path / '__init__.py'
        
        if dir_path.exists() and not init_file.exists():
            try:
                with open(init_file, 'w', encoding='utf-8') as f:
                    f.write(f'# -*- coding: utf-8 -*-\n')
                    f.write(f'"""\n')
                    f.write(f'{directory.replace("/", " ").title()} Package\n')
                    f.write(f'"""\n')
                print(f"✅ تم إنشاء {init_file}")
            except Exception as e:
                print(f"❌ فشل في إنشاء {init_file}: {e}")

def fix_encoding_issues():
    """إصلاح مشاكل الترميز"""
    project_root = Path(__file__).parent
    
    python_files = list(project_root.rglob('*.py'))
    
    for file_path in python_files:
        try:
            # قراءة الملف
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # التحقق من وجود ترميز UTF-8
            if not content.startswith('# -*- coding: utf-8 -*-'):
                # إضافة ترميز UTF-8 في بداية الملف
                if content.startswith('#!'):
                    # إذا كان الملف يبدأ بـ shebang
                    lines = content.split('\n')
                    lines.insert(1, '# -*- coding: utf-8 -*-')
                    content = '\n'.join(lines)
                else:
                    content = '# -*- coding: utf-8 -*-\n' + content
                
                # كتابة الملف المحدث
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                
                print(f"✅ تم إصلاح ترميز {file_path}")
                
        except Exception as e:
            print(f"⚠️ خطأ في معالجة {file_path}: {e}")

def main():
    """الدالة الرئيسية"""
    print("🔧 بدء إصلاح مشاكل التطبيق...")
    print("=" * 50)
    
    # إصلاح مسارات الاستيراد
    fix_import_paths()
    
    # التحقق من الملفات المفقودة
    check_missing_files()
    
    # إنشاء ملفات __init__.py المفقودة
    create_missing_init_files()
    
    # فحص الاستيرادات الدائرية
    fix_circular_imports()
    
    # إصلاح مشاكل الترميز
    print("\n🔤 فحص ترميز الملفات...")
    fix_encoding_issues()
    
    print("\n" + "=" * 50)
    print("✅ تم الانتهاء من إصلاح المشاكل")
    
    # اختبار الاستيراد
    print("\n🧪 اختبار الاستيرادات...")
    try:
        from src.database.database_manager import DatabaseManager
        print("✅ DatabaseManager: نجح الاستيراد")
        
        from src.models.student import Student
        print("✅ Student: نجح الاستيراد")
        
        from src.models.attendance import Attendance
        print("✅ Attendance: نجح الاستيراد")
        
        from src.models.grades import Grades
        print("✅ Grades: نجح الاستيراد")
        
        from src.utils.auth import AuthManager
        print("✅ AuthManager: نجح الاستيراد")
        
        print("\n🎉 جميع الاستيرادات الأساسية تعمل بنجاح!")
        
    except ImportError as e:
        print(f"❌ فشل في الاستيراد: {e}")
        return False
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        print("\n⚠️ يرجى إصلاح المشاكل المذكورة أعلاه قبل تشغيل التطبيق")
        sys.exit(1)
    else:
        print("\n✅ التطبيق جاهز للتشغيل!")
        
        # سؤال المستخدم إذا كان يريد تشغيل التطبيق
        try:
            response = input("\nهل تريد تشغيل التطبيق الآن؟ (y/n): ")
            if response.lower() in ['y', 'yes', 'نعم', 'ن']:
                print("🚀 تشغيل التطبيق...")
                import subprocess
                subprocess.run([sys.executable, 'run.py'])
        except KeyboardInterrupt:
            print("\n👋 تم الإلغاء")
