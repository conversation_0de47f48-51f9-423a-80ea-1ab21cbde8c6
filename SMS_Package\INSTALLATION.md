# دليل التثبيت - نظام إدارة الطلاب

## 📋 المتطلبات الأساسية

### نظام التشغيل
- **Windows 10** أو أحدث (مُوصى به)
- **Windows 8.1** (مدعوم)
- **Linux** (Ubuntu 18.04+ أو توزيعات مماثلة)
- **macOS** (10.14+ مع بعض التعديلات)

### البرامج المطلوبة
- **Python 3.6** أو أحدث
- **pip** (مدير حزم Python)
- **Git** (اختياري للتطوير)

## 🚀 طرق التثبيت

### الطريقة الأولى: التثبيت السريع (Windows)

1. **تحميل الملفات:**
   - حمل ملف ZIP من المطور
   - استخرج الملفات في مجلد مناسب (مثل `C:\StudentManagement`)

2. **تشغيل التطبيق:**
   - انقر مرتين على ملف `start.bat`
   - سيتم تثبيت المتطلبات تلقائياً وتشغيل التطبيق

### الطريقة الثانية: التثبيت اليدوي

#### الخطوة 1: تثبيت Python

1. **تحميل Python:**
   - اذهب إلى [python.org](https://python.org)
   - حمل أحدث إصدار من Python 3

2. **تثبيت Python:**
   - شغل ملف التثبيت
   - ✅ **مهم:** تأكد من تحديد "Add Python to PATH"
   - اختر "Install Now"

3. **التحقق من التثبيت:**
   ```bash
   python --version
   pip --version
   ```

#### الخطوة 2: تحميل التطبيق

**من Git (إذا كان متوفراً):**
```bash
git clone [repository-url]
cd student-management-system
```

**أو تحميل ZIP:**
- حمل ملف ZIP من المطور
- استخرج في مجلد مناسب

#### الخطوة 3: تثبيت المتطلبات

1. **فتح سطر الأوامر:**
   - اضغط `Win + R`
   - اكتب `cmd` واضغط Enter

2. **الانتقال لمجلد التطبيق:**
   ```bash
   cd C:\path\to\student-management-system
   ```

3. **تثبيت المتطلبات:**
   ```bash
   pip install -r requirements.txt
   ```

#### الخطوة 4: تشغيل التطبيق

```bash
python main.py
```

### الطريقة الثالثة: البيئة الافتراضية (للمطورين)

1. **إنشاء بيئة افتراضية:**
   ```bash
   python -m venv venv
   ```

2. **تفعيل البيئة:**
   ```bash
   # Windows
   venv\Scripts\activate
   
   # Linux/Mac
   source venv/bin/activate
   ```

3. **تثبيت المتطلبات:**
   ```bash
   pip install -r requirements.txt
   ```

4. **تشغيل التطبيق:**
   ```bash
   python main.py
   ```

## 🔧 حل مشاكل التثبيت

### مشكلة: "Python غير معروف"

**السبب:** Python غير مضاف لمتغير PATH

**الحل:**
1. أعد تثبيت Python مع تحديد "Add to PATH"
2. أو أضف Python يدوياً للـ PATH:
   - ابحث عن "Environment Variables" في Windows
   - أضف مسار Python (مثل `C:\Python39\`)

### مشكلة: "pip غير معروف"

**الحل:**
```bash
python -m ensurepip --upgrade
```

### مشكلة: "فشل تثبيت PyQt5"

**الحل 1 - تحديث pip:**
```bash
python -m pip install --upgrade pip
pip install PyQt5
```

**الحل 2 - استخدام conda:**
```bash
conda install pyqt
```

**الحل 3 - تثبيت من wheel:**
```bash
pip install --only-binary=all PyQt5
```

### مشكلة: "خطأ في الأذونات"

**Windows:**
- شغل سطر الأوامر كمدير (Run as Administrator)

**Linux/Mac:**
```bash
sudo pip install -r requirements.txt
```

### مشكلة: "مكتبة مفقودة"

**تثبيت مكتبة محددة:**
```bash
pip install [library-name]
```

**إعادة تثبيت جميع المتطلبات:**
```bash
pip install -r requirements.txt --force-reinstall
```

## 🛠️ التحقق من التثبيت

### اختبار سريع

1. **شغل الاختبار التلقائي:**
   ```bash
   python test_app.py
   ```

2. **إصلاح المشاكل:**
   ```bash
   python fix_imports.py
   ```

### اختبار يدوي

1. **شغل التطبيق:**
   ```bash
   python main.py
   ```

2. **تسجيل الدخول:**
   - اسم المستخدم: `admin`
   - كلمة المرور: `admin123`

3. **اختبار الوظائف الأساسية:**
   - فتح نافذة إدارة الطلاب
   - إضافة طالب تجريبي
   - تسجيل حضور
   - إنشاء تقرير بسيط

## 📁 هيكل الملفات بعد التثبيت

```
student-management-system/
├── main.py                 # الملف الرئيسي
├── run.py                  # ملف التشغيل المحسن
├── start.bat              # ملف تشغيل Windows
├── requirements.txt       # المتطلبات
├── test_app.py           # ملف الاختبار
├── fix_imports.py        # إصلاح المشاكل
├── README.md             # ملف التوثيق
├── USER_GUIDE.md         # دليل المستخدم
├── INSTALLATION.md       # دليل التثبيت
├── src/                  # الكود المصدري
│   ├── __init__.py
│   ├── database/         # قاعدة البيانات
│   ├── models/           # نماذج البيانات
│   ├── ui/               # واجهات المستخدم
│   ├── utils/            # أدوات مساعدة
│   └── reports/          # نظام التقارير
├── data/                 # ملفات البيانات
│   └── students.db       # قاعدة البيانات الرئيسية
├── logs/                 # ملفات السجلات
├── backups/              # النسخ الاحتياطية
├── exports/              # الملفات المصدرة
└── assets/               # الموارد (أيقونات، صور)
```

## 🔄 التحديث

### تحديث التطبيق

1. **احفظ نسخة احتياطية:**
   - من داخل التطبيق: الإعدادات → قاعدة البيانات → إنشاء نسخة احتياطية

2. **حمل الإصدار الجديد:**
   - استبدل ملفات `src/` بالملفات الجديدة
   - احتفظ بمجلد `data/` (قاعدة البيانات)

3. **تحديث المتطلبات:**
   ```bash
   pip install -r requirements.txt --upgrade
   ```

### تحديث Python

1. **حمل إصدار أحدث من python.org**
2. **ثبت الإصدار الجديد**
3. **أعد تثبيت المتطلبات:**
   ```bash
   pip install -r requirements.txt
   ```

## 🚫 إلغاء التثبيت

### إزالة التطبيق

1. **احفظ نسخة احتياطية من البيانات**
2. **احذف مجلد التطبيق بالكامل**

### إزالة المتطلبات (اختياري)

```bash
pip uninstall -r requirements.txt -y
```

## 📞 الحصول على المساعدة

### قبل طلب المساعدة

1. **راجع هذا الدليل**
2. **شغل الاختبار التشخيصي:**
   ```bash
   python fix_imports.py
   python test_app.py
   ```
3. **تحقق من ملفات السجلات في مجلد `logs/`**

### معلومات مطلوبة للدعم

عند طلب المساعدة، يرجى تقديم:

1. **نظام التشغيل وإصداره**
2. **إصدار Python:**
   ```bash
   python --version
   ```
3. **رسالة الخطأ الكاملة**
4. **الخطوات التي أدت للمشكلة**
5. **لقطة شاشة إن أمكن**

### طرق التواصل

- **البريد الإلكتروني:** [developer-email]
- **الهاتف:** [phone-number]
- **الدعم الفني:** [support-hours]

---

**نتمنى لك تثبيت ناجح وتجربة ممتعة مع نظام إدارة الطلاب! 🎓**
