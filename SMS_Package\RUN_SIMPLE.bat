@echo off
title نظام إدارة الطلاب - مستر أحمد عادل

echo.
echo ========================================
echo    نظام إدارة الطلاب
echo    مستر أحمد عادل
echo ========================================
echo.

REM محاولة تشغيل التطبيق بطرق مختلفة

echo جاري تشغيل التطبيق...
echo.

REM الطريقة الأولى: تشغيل مباشر
if exist "main.py" (
    echo تشغيل التطبيق...
    python main.py
    goto end
)

REM الطريقة الثانية: تشغيل من مجلد src
if exist "src\main.py" (
    echo تشغيل التطبيق من مجلد src...
    cd src
    python main.py
    goto end
)

REM الطريقة الثالثة: البحث عن ملف EXE
if exist "StudentManagementSystem.exe" (
    echo تشغيل ملف EXE...
    StudentManagementSystem.exe
    goto end
)

if exist "dist\StudentManagementSystem.exe" (
    echo تشغيل ملف EXE من مجلد dist...
    dist\StudentManagementSystem.exe
    goto end
)

REM إذا لم يتم العثور على أي ملف
echo.
echo ❌ لم يتم العثور على ملفات التطبيق
echo.
echo 💡 تأكد من وجود أحد الملفات التالية:
echo    - main.py
echo    - src\main.py  
echo    - StudentManagementSystem.exe
echo    - dist\StudentManagementSystem.exe
echo.
echo 🔧 لبناء ملف EXE، شغل: build_standalone.bat
echo.

:end
echo.
echo بيانات تسجيل الدخول:
echo اسم المستخدم: admin
echo كلمة المرور: admin123
echo.
pause
