@echo off
title Student Management System - Quick Start

echo.
echo ========================================
echo   Student Management System
echo   Quick Start Guide
echo ========================================
echo.

echo Choose an option:
echo.
echo [1] Run Application (if Python installed)
echo [2] Build EXE File (recommended)
echo [3] Run EXE (if already built)
echo [4] Exit
echo.

set /p choice="Enter your choice (1-4): "

if "%choice%"=="1" goto run_python
if "%choice%"=="2" goto build_exe
if "%choice%"=="3" goto run_exe
if "%choice%"=="4" goto exit

echo Invalid choice!
goto end

:run_python
echo.
echo Running Python application...
if exist "main.py" (
    python main.py
) else (
    echo main.py not found!
)
goto end

:build_exe
echo.
echo Building EXE file...
call build_simple.bat
goto end

:run_exe
echo.
echo Running EXE application...
if exist "dist\StudentManagementSystem.exe" (
    dist\StudentManagementSystem.exe
) else if exist "StudentManagementSystem.exe" (
    StudentManagementSystem.exe
) else (
    echo EXE file not found! Please build it first.
)
goto end

:exit
exit

:end
echo.
echo Login credentials:
echo Username: admin
echo Password: admin123
echo.
pause
