@echo off
title Student Management System - Try All Methods

echo.
echo ========================================
echo   Student Management System
echo   Trying All Available Methods
echo ========================================
echo.

echo Method 1: Looking for EXE file...
if exist "StudentManagementSystem.exe" (
    echo Found EXE file! Starting...
    StudentManagementSystem.exe
    goto success
)

if exist "dist\StudentManagementSystem.exe" (
    echo Found EXE in dist folder! Starting...
    dist\StudentManagementSystem.exe
    goto success
)

echo EXE file not found.
echo.

echo Method 2: Trying Python...
python --version >nul 2>&1
if not errorlevel 1 (
    echo Python found! Starting application...
    if exist "main.py" (
        python main.py
        goto success
    ) else (
        echo main.py not found!
    )
) else (
    echo Python not found.
)

echo.
echo Method 3: Trying alternative Python paths...

REM Try common Python installation paths
set "PYTHON_PATHS=C:\Python39\python.exe;C:\Python38\python.exe;C:\Python37\python.exe;%LOCALAPPDATA%\Programs\Python\Python39\python.exe;%LOCALAPPDATA%\Programs\Python\Python38\python.exe;%APPDATA%\Local\Programs\Python\Python39\python.exe"

for %%p in (%PYTHON_PATHS%) do (
    if exist "%%p" (
        echo Found Python at: %%p
        if exist "main.py" (
            echo Starting with alternative Python...
            "%%p" main.py
            goto success
        )
    )
)

echo.
echo ========================================
echo   No working method found!
echo ========================================
echo.
echo Solutions:
echo.
echo Option A: Build EXE file
echo ------------------------
echo 1. Install Python from: https://python.org
echo 2. Run: build_simple.bat
echo 3. Use the generated EXE file
echo.
echo Option B: Install Python
echo ------------------------
echo 1. Go to: https://python.org/downloads
echo 2. Download Python 3.8 or newer
echo 3. Check "Add Python to PATH" during installation
echo 4. Run this file again
echo.
echo Option C: Manual Setup
echo ----------------------
echo 1. Install Python
echo 2. Open Command Prompt
echo 3. Run: pip install PyQt5
echo 4. Run: python main.py
echo.

goto end

:success
echo.
echo ========================================
echo   Application Started Successfully!
echo ========================================
echo.
echo Login credentials:
echo Username: admin
echo Password: admin123
echo.

:end
pause
