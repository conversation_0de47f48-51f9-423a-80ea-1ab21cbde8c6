@echo off
chcp 65001 >nul
title نظام إدارة الطلاب - مستر أحمد عادل

echo.
echo ========================================
echo    نظام إدارة الطلاب - مستر أحمد عادل
echo ========================================
echo.

REM التحقق من وجود Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ خطأ: Python غير مثبت على النظام
    echo يرجى تحميل وتثبيت Python من: https://python.org
    echo.
    pause
    exit /b 1
)

REM التحقق من وجود pip
pip --version >nul 2>&1
if errorlevel 1 (
    echo ❌ خطأ: pip غير متوفر
    echo.
    pause
    exit /b 1
)

REM التحقق من وجود ملف المتطلبات
if not exist "requirements.txt" (
    echo ❌ خطأ: ملف requirements.txt غير موجود
    echo.
    pause
    exit /b 1
)

echo 📋 التحقق من المكتبات المطلوبة...
pip install -r requirements.txt --quiet

if errorlevel 1 (
    echo ❌ فشل في تثبيت المكتبات المطلوبة
    echo يرجى التحقق من اتصال الإنترنت وإعادة المحاولة
    echo.
    pause
    exit /b 1
)

echo ✅ تم التحقق من المكتبات بنجاح
echo.

REM تشغيل التطبيق
echo 🚀 تشغيل التطبيق...
python run.py

REM في حالة حدوث خطأ
if errorlevel 1 (
    echo.
    echo ❌ حدث خطأ أثناء تشغيل التطبيق
    echo.
)

pause
