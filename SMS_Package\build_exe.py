#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
بناء ملف EXE للتطبيق
Build EXE file for the application

هذا الملف يحول التطبيق إلى ملف EXE قابل للتشغيل على Windows
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path

def check_pyinstaller():
    """التحقق من وجود PyInstaller"""
    try:
        import PyInstaller
        print("✅ PyInstaller موجود")
        return True
    except ImportError:
        print("❌ PyInstaller غير مثبت")
        print("يرجى تثبيته باستخدام: pip install pyinstaller")
        return False

def create_spec_file():
    """إنشاء ملف spec لـ PyInstaller"""
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['main.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('src', 'src'),
        ('data', 'data'),
        ('assets', 'assets'),
        ('README.md', '.'),
        ('USER_GUIDE.md', '.'),
        ('INSTALLATION.md', '.'),
    ],
    hiddenimports=[
        'PyQt5.QtCore',
        'PyQt5.QtGui', 
        'PyQt5.QtWidgets',
        'PyQt5.QtPrintSupport',
        'sqlite3',
        'datetime',
        'pathlib',
        'openpyxl',
        'reportlab',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='StudentManagementSystem',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='assets/icon.ico' if os.path.exists('assets/icon.ico') else None,
    version='version_info.txt' if os.path.exists('version_info.txt') else None,
)
'''
    
    with open('app.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print("✅ تم إنشاء ملف app.spec")

def create_version_info():
    """إنشاء ملف معلومات الإصدار"""
    version_info = '''# UTF-8
#
# For more details about fixed file info 'ffi' see:
# http://msdn.microsoft.com/en-us/library/ms646997.aspx
VSVersionInfo(
  ffi=FixedFileInfo(
    filevers=(1,0,0,0),
    prodvers=(1,0,0,0),
    mask=0x3f,
    flags=0x0,
    OS=0x40004,
    fileType=0x1,
    subtype=0x0,
    date=(0, 0)
    ),
  kids=[
    StringFileInfo(
      [
      StringTable(
        u'040904B0',
        [StringStruct(u'CompanyName', u'مستر أحمد عادل'),
        StringStruct(u'FileDescription', u'نظام إدارة الطلاب'),
        StringStruct(u'FileVersion', u'*******'),
        StringStruct(u'InternalName', u'StudentManagementSystem'),
        StringStruct(u'LegalCopyright', u'© 2024 مستر أحمد عادل'),
        StringStruct(u'OriginalFilename', u'StudentManagementSystem.exe'),
        StringStruct(u'ProductName', u'نظام إدارة الطلاب'),
        StringStruct(u'ProductVersion', u'*******')])
      ]), 
    VarFileInfo([VarStruct(u'Translation', [1033, 1200])])
  ]
)
'''
    
    with open('version_info.txt', 'w', encoding='utf-8') as f:
        f.write(version_info)
    
    print("✅ تم إنشاء ملف version_info.txt")

def create_icon():
    """إنشاء أيقونة افتراضية إذا لم تكن موجودة"""
    assets_dir = Path('assets')
    assets_dir.mkdir(exist_ok=True)
    
    icon_path = assets_dir / 'icon.ico'
    if not icon_path.exists():
        print("ℹ️ لم يتم العثور على أيقونة، سيتم استخدام الأيقونة الافتراضية")
        # يمكن إضافة كود لإنشاء أيقونة افتراضية هنا
    else:
        print("✅ تم العثور على ملف الأيقونة")

def prepare_build_environment():
    """تحضير بيئة البناء"""
    print("🔧 تحضير بيئة البناء...")
    
    # إنشاء المجلدات المطلوبة
    required_dirs = ['data', 'logs', 'backups', 'exports', 'assets']
    for dir_name in required_dirs:
        Path(dir_name).mkdir(exist_ok=True)
    
    # إنشاء ملفات فارغة إذا لم تكن موجودة
    data_dir = Path('data')
    if not (data_dir / 'students.db').exists():
        (data_dir / 'students.db').touch()
    
    print("✅ تم تحضير بيئة البناء")

def build_exe():
    """بناء ملف EXE"""
    print("🏗️ بدء بناء ملف EXE...")
    
    try:
        # تشغيل PyInstaller
        cmd = [
            'pyinstaller',
            '--clean',
            '--noconfirm',
            'app.spec'
        ]
        
        print(f"تشغيل الأمر: {' '.join(cmd)}")
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ تم بناء ملف EXE بنجاح!")
            return True
        else:
            print("❌ فشل في بناء ملف EXE")
            print("الخطأ:", result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ خطأ في بناء ملف EXE: {e}")
        return False

def create_installer_script():
    """إنشاء سكريبت تثبيت"""
    installer_content = '''@echo off
chcp 65001 >nul
title تثبيت نظام إدارة الطلاب

echo.
echo ========================================
echo    تثبيت نظام إدارة الطلاب
echo    مستر أحمد عادل
echo ========================================
echo.

REM إنشاء مجلد التثبيت
set INSTALL_DIR=%USERPROFILE%\\Desktop\\StudentManagementSystem
if not exist "%INSTALL_DIR%" mkdir "%INSTALL_DIR%"

echo 📁 نسخ الملفات...
copy "StudentManagementSystem.exe" "%INSTALL_DIR%\\"
if exist "data" xcopy "data" "%INSTALL_DIR%\\data\\" /E /I /Y
if exist "assets" xcopy "assets" "%INSTALL_DIR%\\assets\\" /E /I /Y
copy "README.md" "%INSTALL_DIR%\\" 2>nul
copy "USER_GUIDE.md" "%INSTALL_DIR%\\" 2>nul

echo 🔗 إنشاء اختصار على سطح المكتب...
set SHORTCUT_PATH=%USERPROFILE%\\Desktop\\نظام إدارة الطلاب.lnk
powershell "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%SHORTCUT_PATH%'); $Shortcut.TargetPath = '%INSTALL_DIR%\\StudentManagementSystem.exe'; $Shortcut.WorkingDirectory = '%INSTALL_DIR%'; $Shortcut.Save()"

echo.
echo ✅ تم التثبيت بنجاح!
echo 📍 مكان التثبيت: %INSTALL_DIR%
echo 🖥️ تم إنشاء اختصار على سطح المكتب
echo.
echo اضغط أي مفتاح لتشغيل التطبيق...
pause >nul

start "" "%INSTALL_DIR%\\StudentManagementSystem.exe"
'''
    
    with open('dist/installer.bat', 'w', encoding='utf-8') as f:
        f.write(installer_content)
    
    print("✅ تم إنشاء سكريبت التثبيت")

def cleanup_build_files():
    """تنظيف ملفات البناء"""
    print("🧹 تنظيف ملفات البناء...")
    
    # حذف المجلدات المؤقتة
    temp_dirs = ['build', '__pycache__']
    for dir_name in temp_dirs:
        if os.path.exists(dir_name):
            shutil.rmtree(dir_name)
            print(f"🗑️ تم حذف {dir_name}")
    
    # حذف الملفات المؤقتة
    temp_files = ['app.spec', 'version_info.txt']
    for file_name in temp_files:
        if os.path.exists(file_name):
            os.remove(file_name)
            print(f"🗑️ تم حذف {file_name}")

def create_distribution_package():
    """إنشاء حزمة التوزيع"""
    print("📦 إنشاء حزمة التوزيع...")
    
    dist_dir = Path('dist')
    if not dist_dir.exists():
        print("❌ مجلد dist غير موجود")
        return False
    
    exe_file = dist_dir / 'StudentManagementSystem.exe'
    if not exe_file.exists():
        print("❌ ملف EXE غير موجود")
        return False
    
    # نسخ الملفات المطلوبة
    files_to_copy = [
        'README.md',
        'USER_GUIDE.md', 
        'INSTALLATION.md'
    ]
    
    for file_name in files_to_copy:
        if os.path.exists(file_name):
            shutil.copy2(file_name, dist_dir)
    
    # نسخ المجلدات المطلوبة
    dirs_to_copy = ['data', 'assets']
    for dir_name in dirs_to_copy:
        if os.path.exists(dir_name):
            dest_dir = dist_dir / dir_name
            if dest_dir.exists():
                shutil.rmtree(dest_dir)
            shutil.copytree(dir_name, dest_dir)
    
    # إنشاء سكريبت التثبيت
    create_installer_script()
    
    print("✅ تم إنشاء حزمة التوزيع في مجلد dist")
    return True

def main():
    """الدالة الرئيسية"""
    print("🏗️ بناء ملف EXE لنظام إدارة الطلاب")
    print("=" * 50)
    
    # التحقق من PyInstaller
    if not check_pyinstaller():
        return False
    
    # تحضير البيئة
    prepare_build_environment()
    
    # إنشاء الملفات المطلوبة
    create_icon()
    create_version_info()
    create_spec_file()
    
    # بناء ملف EXE
    if not build_exe():
        return False
    
    # إنشاء حزمة التوزيع
    if not create_distribution_package():
        return False
    
    # تنظيف الملفات المؤقتة
    cleanup_build_files()
    
    print("\n" + "=" * 50)
    print("🎉 تم بناء التطبيق بنجاح!")
    print("📁 ملف EXE موجود في: dist/StudentManagementSystem.exe")
    print("📦 حزمة التوزيع جاهزة في مجلد: dist/")
    print("🔧 لتثبيت التطبيق، شغل: dist/installer.bat")
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        print("\n❌ فشل في بناء التطبيق")
        sys.exit(1)
    else:
        print("\n✅ تم بناء التطبيق بنجاح!")
        
        # سؤال المستخدم إذا كان يريد تشغيل التطبيق
        try:
            response = input("\nهل تريد تشغيل ملف EXE الآن؟ (y/n): ")
            if response.lower() in ['y', 'yes', 'نعم', 'ن']:
                exe_path = Path('dist') / 'StudentManagementSystem.exe'
                if exe_path.exists():
                    print("🚀 تشغيل التطبيق...")
                    subprocess.run([str(exe_path)])
                else:
                    print("❌ ملف EXE غير موجود")
        except KeyboardInterrupt:
            print("\n👋 تم الإلغاء")
