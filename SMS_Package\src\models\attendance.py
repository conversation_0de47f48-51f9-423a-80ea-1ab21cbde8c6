# -*- coding: utf-8 -*-
"""
نموذج الحضور
Attendance Model
"""

from datetime import datetime, date
from typing import Optional, List, Dict, Any
from ..database.database_manager import DatabaseManager

class Attendance:
    """نموذج بيانات الحضور"""
    
    def __init__(self, db_manager: DatabaseManager):
        self.db = db_manager
    
    def mark_attendance(self, student_id: int, attendance_date: date = None, 
                       status: str = "حاضر", notes: str = "") -> bool:
        """
        تسجيل حضور طالب
        
        Args:
            student_id: معرف الطالب
            attendance_date: تاريخ الحضور (افتراضي: اليوم)
            status: حالة الحضور (حاضر، غائب، متأخر)
            notes: ملاحظات
            
        Returns:
            True في حالة النجاح، False في حالة الفشل
        """
        try:
            if attendance_date is None:
                attendance_date = date.today()
            
            # التحقق من وجود تسجيل سابق لنفس اليوم
            existing = self.db.fetch_one("""
                SELECT id FROM attendance 
                WHERE student_id = ? AND attendance_date = ?
            """, (student_id, attendance_date))
            
            if existing:
                # تحديث التسجيل الموجود
                self.db.execute_query("""
                    UPDATE attendance 
                    SET status = ?, notes = ?
                    WHERE student_id = ? AND attendance_date = ?
                """, (status, notes, student_id, attendance_date))
            else:
                # إنشاء تسجيل جديد
                self.db.execute_query("""
                    INSERT INTO attendance (student_id, attendance_date, status, notes)
                    VALUES (?, ?, ?, ?)
                """, (student_id, attendance_date, status, notes))
            
            return True
            
        except Exception as e:
            print(f"خطأ في تسجيل الحضور: {e}")
            return False
    
    def mark_attendance_by_code(self, student_code: str, attendance_date: date = None,
                               status: str = "حاضر", notes: str = "") -> bool:
        """تسجيل حضور طالب بالكود"""
        # جلب معرف الطالب من الكود
        student = self.db.fetch_one(
            "SELECT id FROM students WHERE student_code = ?", 
            (student_code,)
        )
        
        if not student:
            print(f"لم يتم العثور على طالب بالكود: {student_code}")
            return False
        
        return self.mark_attendance(student['id'], attendance_date, status, notes)
    
    def get_student_attendance(self, student_id: int, 
                              start_date: date = None, 
                              end_date: date = None) -> List[Dict[str, Any]]:
        """جلب سجل حضور طالب معين"""
        query = """
            SELECT a.*, s.full_name, s.student_code
            FROM attendance a
            JOIN students s ON a.student_id = s.id
            WHERE a.student_id = ?
        """
        params = [student_id]
        
        if start_date:
            query += " AND a.attendance_date >= ?"
            params.append(start_date)
        
        if end_date:
            query += " AND a.attendance_date <= ?"
            params.append(end_date)
        
        query += " ORDER BY a.attendance_date DESC"
        
        return self.db.fetch_all(query, tuple(params))
    
    def get_daily_attendance(self, attendance_date: date = None) -> List[Dict[str, Any]]:
        """جلب حضور جميع الطلاب ليوم معين"""
        if attendance_date is None:
            attendance_date = date.today()
        
        return self.db.fetch_all("""
            SELECT s.id, s.student_code, s.full_name, s.grade, s.stage,
                   a.status, a.notes, a.attendance_date
            FROM students s
            LEFT JOIN attendance a ON s.id = a.student_id 
                AND a.attendance_date = ?
            ORDER BY s.grade, s.full_name
        """, (attendance_date,))
    
    def get_attendance_statistics(self, start_date: date = None, 
                                 end_date: date = None) -> Dict[str, Any]:
        """جلب إحصائيات الحضور"""
        if start_date is None:
            start_date = date.today().replace(day=1)  # بداية الشهر الحالي
        
        if end_date is None:
            end_date = date.today()
        
        stats = {}
        
        # إحصائيات عامة
        total_records = self.db.fetch_one("""
            SELECT COUNT(*) as count 
            FROM attendance 
            WHERE attendance_date BETWEEN ? AND ?
        """, (start_date, end_date))
        stats['total_records'] = total_records['count'] if total_records else 0
        
        # حسب الحالة
        status_stats = self.db.fetch_all("""
            SELECT status, COUNT(*) as count 
            FROM attendance 
            WHERE attendance_date BETWEEN ? AND ?
            GROUP BY status
        """, (start_date, end_date))
        stats['by_status'] = {row['status']: row['count'] for row in status_stats}
        
        # معدل الحضور اليومي
        daily_stats = self.db.fetch_all("""
            SELECT attendance_date, 
                   COUNT(*) as total,
                   SUM(CASE WHEN status = 'حاضر' THEN 1 ELSE 0 END) as present,
                   SUM(CASE WHEN status = 'غائب' THEN 1 ELSE 0 END) as absent,
                   SUM(CASE WHEN status = 'متأخر' THEN 1 ELSE 0 END) as late
            FROM attendance 
            WHERE attendance_date BETWEEN ? AND ?
            GROUP BY attendance_date
            ORDER BY attendance_date
        """, (start_date, end_date))
        stats['daily_stats'] = daily_stats
        
        # أكثر الطلاب حضوراً
        top_attendance = self.db.fetch_all("""
            SELECT s.full_name, s.student_code,
                   COUNT(*) as total_days,
                   SUM(CASE WHEN a.status = 'حاضر' THEN 1 ELSE 0 END) as present_days
            FROM students s
            JOIN attendance a ON s.id = a.student_id
            WHERE a.attendance_date BETWEEN ? AND ?
            GROUP BY s.id, s.full_name, s.student_code
            ORDER BY present_days DESC
            LIMIT 10
        """, (start_date, end_date))
        stats['top_attendance'] = top_attendance
        
        return stats
    
    def get_absent_students(self, attendance_date: date = None) -> List[Dict[str, Any]]:
        """جلب الطلاب الغائبين ليوم معين"""
        if attendance_date is None:
            attendance_date = date.today()
        
        return self.db.fetch_all("""
            SELECT s.id, s.student_code, s.full_name, s.grade, s.stage
            FROM students s
            LEFT JOIN attendance a ON s.id = a.student_id 
                AND a.attendance_date = ?
            WHERE a.status IS NULL OR a.status = 'غائب'
            ORDER BY s.grade, s.full_name
        """, (attendance_date,))
    
    def delete_attendance_record(self, student_id: int, attendance_date: date) -> bool:
        """حذف تسجيل حضور"""
        try:
            self.db.execute_query("""
                DELETE FROM attendance 
                WHERE student_id = ? AND attendance_date = ?
            """, (student_id, attendance_date))
            return True
        except Exception as e:
            print(f"خطأ في حذف تسجيل الحضور: {e}")
            return False
    
    def get_student_attendance_summary(self, student_id: int, 
                                     start_date: date = None, 
                                     end_date: date = None) -> Dict[str, Any]:
        """ملخص حضور طالب معين"""
        if start_date is None:
            start_date = date.today().replace(day=1)  # بداية الشهر
        
        if end_date is None:
            end_date = date.today()
        
        summary = self.db.fetch_one("""
            SELECT 
                COUNT(*) as total_days,
                SUM(CASE WHEN status = 'حاضر' THEN 1 ELSE 0 END) as present_days,
                SUM(CASE WHEN status = 'غائب' THEN 1 ELSE 0 END) as absent_days,
                SUM(CASE WHEN status = 'متأخر' THEN 1 ELSE 0 END) as late_days
            FROM attendance 
            WHERE student_id = ? AND attendance_date BETWEEN ? AND ?
        """, (student_id, start_date, end_date))
        
        if summary and summary['total_days'] > 0:
            summary['attendance_rate'] = (summary['present_days'] / summary['total_days']) * 100
        else:
            summary = {
                'total_days': 0,
                'present_days': 0,
                'absent_days': 0,
                'late_days': 0,
                'attendance_rate': 0
            }
        
        return summary
