#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إصلاح قاعدة البيانات
Database Fix Script
"""

import sqlite3
import os

def fix_database():
    """إصلاح قاعدة البيانات وإضافة الحقول المفقودة"""
    
    # مسار قاعدة البيانات
    db_path = os.path.join('data', 'students.db')
    
    # إنشاء مجلد البيانات إذا لم يكن موجوداً
    os.makedirs('data', exist_ok=True)
    
    try:
        # الاتصال بقاعدة البيانات
        conn = sqlite3.connect(db_path, check_same_thread=False)
        cursor = conn.cursor()
        
        print("🔧 بدء إصلاح قاعدة البيانات...")
        
        # التحقق من وجود جدول الطلاب
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='students'")
        if not cursor.fetchone():
            print("❌ جدول الطلاب غير موجود، سيتم إنشاؤه...")
            
            # إنشاء جدول الطلاب
            cursor.execute("""
                CREATE TABLE students (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    student_code TEXT UNIQUE NOT NULL,
                    full_name TEXT NOT NULL,
                    gender TEXT NOT NULL CHECK (gender IN ('ذكر', 'أنثى')),
                    stage TEXT NOT NULL CHECK (stage IN ('إعدادي', 'ثانوي')),
                    grade TEXT NOT NULL,
                    group_name TEXT DEFAULT 'لا توجد مجموعة',
                    phone TEXT,
                    parent_phone TEXT,
                    geography_score REAL DEFAULT 0,
                    history_score REAL DEFAULT 0,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            print("✅ تم إنشاء جدول الطلاب")
        
        # التحقق من الأعمدة الموجودة
        cursor.execute("PRAGMA table_info(students)")
        columns = [column[1] for column in cursor.fetchall()]
        print(f"📋 الأعمدة الموجودة: {columns}")
        
        # إضافة الأعمدة المفقودة
        if 'phone' not in columns:
            cursor.execute("ALTER TABLE students ADD COLUMN phone TEXT")
            print("✅ تم إضافة عمود رقم الهاتف")
        
        if 'parent_phone' not in columns:
            cursor.execute("ALTER TABLE students ADD COLUMN parent_phone TEXT")
            print("✅ تم إضافة عمود رقم ولي الأمر")
        
        if 'group_name' not in columns:
            cursor.execute("ALTER TABLE students ADD COLUMN group_name TEXT DEFAULT 'لا توجد مجموعة'")
            print("✅ تم إضافة عمود المجموعة")
        
        # التحقق من وجود جدول المدفوعات
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='monthly_payments'")
        if not cursor.fetchone():
            print("💰 إنشاء جدول المدفوعات...")
            
            cursor.execute("""
                CREATE TABLE monthly_payments (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    student_id INTEGER NOT NULL,
                    student_code TEXT NOT NULL,
                    student_name TEXT NOT NULL,
                    group_name TEXT,
                    geography_fee REAL DEFAULT 0,
                    history_fee REAL DEFAULT 0,
                    total_amount REAL DEFAULT 0,
                    payment_date DATE NOT NULL,
                    payment_time TIME NOT NULL,
                    month_year TEXT NOT NULL,
                    notes TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (student_id) REFERENCES students (id)
                )
            """)
            print("✅ تم إنشاء جدول المدفوعات")
        
        # التحقق من وجود جدول الإعدادات
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='settings'")
        if not cursor.fetchone():
            print("⚙️ إنشاء جدول الإعدادات...")
            
            cursor.execute("""
                CREATE TABLE settings (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    setting_key TEXT UNIQUE NOT NULL,
                    setting_value TEXT NOT NULL,
                    description TEXT,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # إضافة الإعدادات الافتراضية
            cursor.execute("INSERT OR REPLACE INTO settings (setting_key, setting_value) VALUES (?, ?)", 
                         ("default_geography_fee", "100.0"))
            cursor.execute("INSERT OR REPLACE INTO settings (setting_key, setting_value) VALUES (?, ?)", 
                         ("default_history_fee", "100.0"))
            
            print("✅ تم إنشاء جدول الإعدادات مع القيم الافتراضية")
        
        # التحقق من وجود جدول الحضور
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='attendance'")
        if not cursor.fetchone():
            print("📋 إنشاء جدول الحضور...")
            
            cursor.execute("""
                CREATE TABLE attendance (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    student_id INTEGER NOT NULL,
                    attendance_date DATE NOT NULL,
                    status TEXT NOT NULL CHECK (status IN ('حاضر', 'غائب', 'متأخر', 'مبرر')),
                    notes TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (student_id) REFERENCES students (id) ON DELETE CASCADE,
                    UNIQUE(student_id, attendance_date)
                )
            """)
            print("✅ تم إنشاء جدول الحضور")
        
        # التحقق من وجود جدول الدرجات
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='grades'")
        if not cursor.fetchone():
            print("📊 إنشاء جدول الدرجات...")
            
            cursor.execute("""
                CREATE TABLE grades (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    student_id INTEGER NOT NULL,
                    subject TEXT NOT NULL CHECK (subject IN ('جغرافيا', 'تاريخ')),
                    exam_type TEXT NOT NULL,
                    score REAL NOT NULL,
                    max_score REAL NOT NULL,
                    exam_date DATE,
                    notes TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (student_id) REFERENCES students (id) ON DELETE CASCADE
                )
            """)
            print("✅ تم إنشاء جدول الدرجات")
        
        # التحقق من وجود جدول المستخدمين
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='users'")
        if not cursor.fetchone():
            print("👤 إنشاء جدول المستخدمين...")
            
            cursor.execute("""
                CREATE TABLE users (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    username TEXT UNIQUE NOT NULL,
                    password_hash TEXT NOT NULL,
                    full_name TEXT NOT NULL,
                    role TEXT DEFAULT 'admin',
                    is_active BOOLEAN DEFAULT 1,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    last_login TIMESTAMP
                )
            """)
            print("✅ تم إنشاء جدول المستخدمين")
        
        # حفظ التغييرات
        conn.commit()
        
        # عرض إحصائيات الجداول
        print("\n📊 إحصائيات قاعدة البيانات:")
        
        tables = ['students', 'monthly_payments', 'attendance', 'grades', 'settings', 'users']
        for table in tables:
            cursor.execute(f"SELECT COUNT(*) FROM {table}")
            count = cursor.fetchone()[0]
            print(f"   📋 {table}: {count} سجل")
        
        print("\n✅ تم إصلاح قاعدة البيانات بنجاح!")
        print("🎉 يمكنك الآن تشغيل النظام بدون مشاكل")
        
    except Exception as e:
        print(f"❌ خطأ في إصلاح قاعدة البيانات: {e}")
        return False
    
    finally:
        if conn:
            conn.close()
    
    return True

if __name__ == "__main__":
    print("🔧 أداة إصلاح قاعدة البيانات")
    print("=" * 50)
    
    if fix_database():
        print("\n🎯 تم الإصلاح بنجاح!")
        print("يمكنك الآن تشغيل النظام باستخدام: RUN_FINAL_COMPLETE.bat")
    else:
        print("\n❌ فشل في الإصلاح!")
        print("يرجى التحقق من الأخطاء أعلاه")
    
    input("\nاضغط Enter للخروج...")
