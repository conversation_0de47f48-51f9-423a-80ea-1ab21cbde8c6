# 🎓 نظام إدارة الطلاب لمستر أحمد عادل

## 📋 نظرة عامة

نظام إدارة الطلاب هو تطبيق سطح مكتب شامل مصمم خصيصاً لمستر أحمد عادل لإدارة طلاب مادتي الجغرافيا والتاريخ في المراحل الإعدادية والثانوية. يوفر التطبيق حلولاً متكاملة لتتبع الحضور وإدارة الدرجات وإنشاء التقارير.

## ✨ المميزات الرئيسية

### 🎓 إدارة الطلاب
- ✅ إضافة وتعديل وحذف بيانات الطلاب
- ✅ تصنيف الطلاب حسب المرحلة والصف
- ✅ توليد أكواد طلاب تلقائياً
- ✅ البحث السريع والمتقدم
- ✅ إحصائيات شاملة

### 📅 تسجيل الحضور
- ✅ تسجيل حضور سريع بالكود
- ✅ تتبع الحضور والغياب والتأخير
- ✅ عرض الحضور اليومي والشهري
- ✅ إحصائيات الحضور المفصلة
- ✅ تقارير الحضور القابلة للتصدير

### 📊 إدارة الدرجات
- ✅ إدخال درجات الجغرافيا والتاريخ
- ✅ أنواع امتحانات متعددة
- ✅ حساب المتوسطات تلقائياً
- ✅ تتبع أداء الطلاب
- ✅ قوائم أفضل الطلاب

### 📈 التقارير والإحصائيات
- ✅ تقارير شاملة للطلاب والحضور والدرجات
- ✅ تصدير بصيغة PDF و Excel
- ✅ إحصائيات مرئية وتفاعلية
- ✅ تقارير قابلة للطباعة

### ⚙️ الإعدادات والأمان
- ✅ نظام مصادقة آمن
- ✅ إعدادات قابلة للتخصيص
- ✅ نسخ احتياطي للبيانات
- ✅ واجهة عربية بالكامل

## 🚀 التثبيت والتشغيل

### المتطلبات الأساسية
- **Windows 10** أو أحدث (مُوصى به)
- **Python 3.6+** (للتطوير أو التشغيل من المصدر)
- **4 جيجابايت RAM** كحد أدنى
- **500 ميجابايت** مساحة فارغة

### طرق التشغيل

#### 🎯 الطريقة الأولى: التشغيل السريع (مُوصى به)
```bash
# انقر مرتين على الملف
run_app.bat
```

#### 🐍 الطريقة الثانية: التشغيل من سطر الأوامر
```bash
# التشغيل العادي
python main.py

# التشغيل المحسن
python run.py

# التشغيل الشامل مع فحص كامل
python launch.py
```

#### 🔧 الطريقة الثالثة: إصلاح المشاكل أولاً
```bash
# إصلاح مشاكل الاستيراد
python fix_imports.py

# اختبار التطبيق
python test_app.py

# ثم التشغيل
python main.py
```

### 🔐 بيانات تسجيل الدخول الافتراضية
- **اسم المستخدم:** `admin`
- **كلمة المرور:** `admin123`

### 📦 تحويل إلى ملف EXE
```bash
# بناء ملف EXE
python build_exe.py

# أو استخدام ملف batch
build.bat
```

## 📖 الملفات المهمة

| الملف | الوصف |
|-------|--------|
| `run_app.bat` | **ملف التشغيل الرئيسي** - انقر مرتين لتشغيل التطبيق |
| `main.py` | الملف الرئيسي للتطبيق |
| `launch.py` | ملف التشغيل الشامل مع فحص كامل |
| `fix_imports.py` | إصلاح مشاكل الاستيراد |
| `test_app.py` | اختبار التطبيق |
| `build_exe.py` | تحويل التطبيق إلى EXE |
| `USER_GUIDE.md` | **دليل المستخدم الكامل** |
| `INSTALLATION.md` | دليل التثبيت المفصل |

## 🗂️ هيكل المشروع

```
student-management-system/
├── 🚀 run_app.bat           # ملف التشغيل الرئيسي
├── 🐍 main.py               # الملف الرئيسي
├── 🔧 launch.py             # التشغيل الشامل
├── 🛠️ fix_imports.py        # إصلاح المشاكل
├── 🧪 test_app.py           # اختبار التطبيق
├── 📦 build_exe.py          # بناء EXE
├── ⚙️ config.py             # ملف التكوين
├── 📋 requirements.txt      # المتطلبات
├── 📖 USER_GUIDE.md         # دليل المستخدم
├── 🔧 INSTALLATION.md       # دليل التثبيت
├── src/                     # الكود المصدري
│   ├── database/           # إدارة قاعدة البيانات
│   ├── models/             # نماذج البيانات
│   ├── ui/                 # واجهات المستخدم
│   ├── utils/              # أدوات مساعدة
│   └── reports/            # نظام التقارير
├── data/                   # ملفات البيانات
├── logs/                   # ملفات السجلات
├── backups/                # النسخ الاحتياطية
├── exports/                # الملفات المصدرة
└── assets/                 # الموارد (أيقونات، صور)
```

## 🎯 البدء السريع

### للمستخدمين الجدد:
1. **انقر مرتين على `run_app.bat`**
2. **اختر الخيار رقم 1 للتشغيل العادي**
3. **استخدم `admin` و `admin123` لتسجيل الدخول**
4. **راجع `USER_GUIDE.md` للحصول على دليل مفصل**

### في حالة وجود مشاكل:
1. **شغل `run_app.bat` واختر الخيار رقم 4 لإصلاح المشاكل**
2. **أو شغل `python fix_imports.py` مباشرة**
3. **راجع مجلد `logs/` للأخطاء التفصيلية**
4. **راجع `INSTALLATION.md` لحل مشاكل التثبيت**

## 👨‍🏫 معلومات المعلم

- **الاسم:** مستر أحمد عادل
- **المواد:** الجغرافيا والتاريخ
- **المراحل:** الإعدادية والثانوية

### 🎓 المراحل الدراسية المدعومة

**المرحلة الإعدادية:**
- أولى إعدادي
- ثانية إعدادي
- ثالثة إعدادي

**المرحلة الثانوية:**
- أولى ثانوي
- ثانية ثانوي
- ثالثة ثانوي

## 🛠️ التطوير والتخصيص

### إضافة مميزات جديدة:
```bash
# إنشاء فرع جديد
git checkout -b feature-name

# تطوير الميزة
# اختبار التغييرات
python test_app.py

# إرسال طلب دمج
```

### تخصيص الواجهة:
- عدل ملف `src/utils/styles.py` لتغيير الألوان والخطوط
- أضف أيقونات جديدة في مجلد `assets/`
- عدل ملف `config.py` للإعدادات العامة

## 🐛 حل المشاكل الشائعة

| المشكلة | الحل |
|---------|------|
| "PyQt5 غير مثبت" | `pip install PyQt5` |
| "خطأ في قاعدة البيانات" | شغل `python fix_imports.py` |
| "الخطوط العربية لا تظهر" | تأكد من تثبيت خطوط عربية |
| "التطبيق لا يبدأ" | شغل `run_app.bat` واختر الخيار 4 |

## 📞 الدعم والمساعدة

### للحصول على المساعدة:
1. **راجع `USER_GUIDE.md` أولاً**
2. **تحقق من ملف `logs/` للأخطاء**
3. **شغل `python test_app.py` للتشخيص**
4. **راجع `INSTALLATION.md` لمشاكل التثبيت**

### الإبلاغ عن مشاكل:
- صف المشكلة بالتفصيل
- أرفق لقطة شاشة إن أمكن
- اذكر نظام التشغيل وإصدار Python
- أرفق محتوى ملف السجلات

## 📄 الترخيص

هذا المشروع مطور خصيصاً لمستر أحمد عادل لاستخدامه في إدارة طلابه.

## 🙏 شكر وتقدير

- تم تطوير هذا النظام بواسطة مساعد الذكي
- شكر خاص لمستر أحمد عادل على الثقة والتعاون
- مكتبة PyQt5 لواجهة المستخدم الرائعة

---

**نتمنى لك تجربة ممتعة ومفيدة مع نظام إدارة الطلاب! 🎓**

*للبدء السريع: انقر مرتين على `run_app.bat`*
