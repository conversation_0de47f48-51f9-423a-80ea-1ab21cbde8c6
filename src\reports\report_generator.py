# -*- coding: utf-8 -*-
"""
مولد التقارير
Report Generator
"""

import os
from datetime import datetime, date
from typing import List, Dict, Any, Optional

class ReportGenerator:
    """مولد التقارير الأساسي"""
    
    def __init__(self, db_manager):
        self.db = db_manager
        from ..models.student import Student
        from ..models.attendance import Attendance
        from ..models.grades import Grades
        
        self.student_model = Student(db_manager)
        self.attendance_model = Attendance(db_manager)
        self.grades_model = Grades(db_manager)
    
    def generate_students_report(self, stage_filter: str = None, grade_filter: str = None) -> Dict[str, Any]:
        """إنشاء تقرير الطلاب"""
        students = self.student_model.get_all_students()
        
        # تطبيق الفلاتر
        if stage_filter and stage_filter != "الكل":
            students = [s for s in students if s['stage'] == stage_filter]
        
        if grade_filter and grade_filter != "الكل":
            students = [s for s in students if s['grade'] == grade_filter]
        
        # إحصائيات
        stats = self.student_model.get_student_statistics()
        
        report_data = {
            'title': 'تقرير الطلاب',
            'generated_at': datetime.now(),
            'filters': {
                'stage': stage_filter or "الكل",
                'grade': grade_filter or "الكل"
            },
            'students': students,
            'statistics': stats,
            'total_count': len(students)
        }
        
        return report_data
    
    def generate_attendance_report(self, start_date: date, end_date: date, 
                                 stage_filter: str = None) -> Dict[str, Any]:
        """إنشاء تقرير الحضور"""
        # جلب إحصائيات الحضور
        stats = self.attendance_model.get_attendance_statistics(start_date, end_date)
        
        # جلب الحضور اليومي
        daily_attendance = []
        current_date = start_date
        while current_date <= end_date:
            daily_data = self.attendance_model.get_daily_attendance(current_date)
            
            # تطبيق فلتر المرحلة
            if stage_filter and stage_filter != "الكل":
                daily_data = [d for d in daily_data if d.get('stage') == stage_filter]
            
            daily_summary = {
                'date': current_date,
                'total': len(daily_data),
                'present': sum(1 for d in daily_data if d.get('status') == 'حاضر'),
                'absent': sum(1 for d in daily_data if d.get('status') == 'غائب' or d.get('status') is None),
                'late': sum(1 for d in daily_data if d.get('status') == 'متأخر'),
                'details': daily_data
            }
            daily_attendance.append(daily_summary)
            
            # الانتقال لليوم التالي
            from datetime import timedelta
            current_date += timedelta(days=1)
        
        report_data = {
            'title': 'تقرير الحضور',
            'generated_at': datetime.now(),
            'period': {
                'start_date': start_date,
                'end_date': end_date
            },
            'filters': {
                'stage': stage_filter or "الكل"
            },
            'statistics': stats,
            'daily_attendance': daily_attendance
        }
        
        return report_data
    
    def generate_grades_report(self, subject_filter: str = None, 
                             grade_filter: str = None) -> Dict[str, Any]:
        """إنشاء تقرير الدرجات"""
        # جلب الدرجات
        if subject_filter and subject_filter != "الكل":
            grades = self.grades_model.get_subject_grades(subject_filter, grade_filter)
        else:
            geography_grades = self.grades_model.get_subject_grades("جغرافيا", grade_filter)
            history_grades = self.grades_model.get_subject_grades("تاريخ", grade_filter)
            grades = geography_grades + history_grades
        
        # إحصائيات الدرجات
        stats = self.grades_model.get_grade_statistics(subject_filter, grade_filter)
        
        # أفضل الطلاب
        top_students = {}
        if subject_filter and subject_filter != "الكل":
            top_students[subject_filter] = self.grades_model.get_top_students(subject_filter, 10)
        else:
            top_students['جغرافيا'] = self.grades_model.get_top_students('جغرافيا', 10)
            top_students['تاريخ'] = self.grades_model.get_top_students('تاريخ', 10)
        
        # الطلاب الذين يحتاجون مساعدة
        failing_students = {}
        if subject_filter and subject_filter != "الكل":
            failing_students[subject_filter] = self.grades_model.get_failing_students(subject_filter)
        else:
            failing_students['جغرافيا'] = self.grades_model.get_failing_students('جغرافيا')
            failing_students['تاريخ'] = self.grades_model.get_failing_students('تاريخ')
        
        report_data = {
            'title': 'تقرير الدرجات',
            'generated_at': datetime.now(),
            'filters': {
                'subject': subject_filter or "الكل",
                'grade': grade_filter or "الكل"
            },
            'grades': grades,
            'statistics': stats,
            'top_students': top_students,
            'failing_students': failing_students,
            'total_count': len(grades)
        }
        
        return report_data
    
    def generate_comprehensive_report(self, stage_filter: str = None) -> Dict[str, Any]:
        """إنشاء تقرير شامل"""
        # تقرير الطلاب
        students_data = self.generate_students_report(stage_filter)
        
        # تقرير الحضور (آخر 30 يوم)
        from datetime import timedelta
        end_date = date.today()
        start_date = end_date - timedelta(days=30)
        attendance_data = self.generate_attendance_report(start_date, end_date, stage_filter)
        
        # تقرير الدرجات
        grades_data = self.generate_grades_report(grade_filter=stage_filter)
        
        report_data = {
            'title': 'التقرير الشامل',
            'generated_at': datetime.now(),
            'filters': {
                'stage': stage_filter or "الكل"
            },
            'students': students_data,
            'attendance': attendance_data,
            'grades': grades_data
        }
        
        return report_data


class PDFReportGenerator(ReportGenerator):
    """مولد تقارير PDF"""
    
    def __init__(self, db_manager):
        super().__init__(db_manager)
    
    def export_to_pdf(self, report_data: Dict[str, Any], file_path: str) -> bool:
        """تصدير التقرير إلى PDF"""
        try:
            from reportlab.lib.pagesizes import A4
            from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle
            from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
            from reportlab.lib.units import inch
            from reportlab.lib import colors
            from reportlab.pdfbase import pdfmetrics
            from reportlab.pdfbase.ttfonts import TTFont
            
            # إنشاء المستند
            doc = SimpleDocTemplate(file_path, pagesize=A4)
            story = []
            
            # الأنماط
            styles = getSampleStyleSheet()
            title_style = ParagraphStyle(
                'CustomTitle',
                parent=styles['Heading1'],
                fontSize=18,
                spaceAfter=30,
                alignment=1  # وسط
            )
            
            # العنوان
            title = Paragraph(report_data['title'], title_style)
            story.append(title)
            story.append(Spacer(1, 12))
            
            # معلومات التقرير
            info_text = f"تاريخ الإنشاء: {report_data['generated_at'].strftime('%Y-%m-%d %H:%M')}"
            info = Paragraph(info_text, styles['Normal'])
            story.append(info)
            story.append(Spacer(1, 12))
            
            # محتوى التقرير حسب النوع
            if 'students' in report_data:
                self._add_students_content(story, report_data, styles)
            elif 'daily_attendance' in report_data:
                self._add_attendance_content(story, report_data, styles)
            elif 'grades' in report_data:
                self._add_grades_content(story, report_data, styles)
            
            # بناء المستند
            doc.build(story)
            return True
            
        except ImportError:
            print("مكتبة reportlab غير مثبتة")
            return False
        except Exception as e:
            print(f"خطأ في إنشاء PDF: {e}")
            return False
    
    def _add_students_content(self, story, report_data, styles):
        """إضافة محتوى تقرير الطلاب"""
        # الإحصائيات
        stats = report_data['statistics']
        stats_text = f"إجمالي الطلاب: {stats.get('total_students', 0)}"
        story.append(Paragraph(stats_text, styles['Normal']))
        story.append(Spacer(1, 12))
        
        # جدول الطلاب
        if report_data['students']:
            data = [['الكود', 'الاسم', 'النوع', 'المرحلة', 'الصف', 'جغرافيا', 'تاريخ']]
            
            for student in report_data['students'][:20]:  # أول 20 طالب
                data.append([
                    student['student_code'],
                    student['full_name'],
                    student['gender'],
                    student['stage'],
                    student['grade'],
                    f"{student['geography_score']:.1f}",
                    f"{student['history_score']:.1f}"
                ])
            
            table = Table(data)
            table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, 0), 10),
                ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                ('GRID', (0, 0), (-1, -1), 1, colors.black)
            ]))
            
            story.append(table)
    
    def _add_attendance_content(self, story, report_data, styles):
        """إضافة محتوى تقرير الحضور"""
        # الإحصائيات
        stats = report_data['statistics']
        stats_text = f"إجمالي السجلات: {stats.get('total_records', 0)}"
        story.append(Paragraph(stats_text, styles['Normal']))
        story.append(Spacer(1, 12))
    
    def _add_grades_content(self, story, report_data, styles):
        """إضافة محتوى تقرير الدرجات"""
        # الإحصائيات
        stats_text = f"إجمالي الدرجات: {report_data['total_count']}"
        story.append(Paragraph(stats_text, styles['Normal']))
        story.append(Spacer(1, 12))


class ExcelReportGenerator(ReportGenerator):
    """مولد تقارير Excel"""
    
    def __init__(self, db_manager):
        super().__init__(db_manager)
    
    def export_to_excel(self, report_data: Dict[str, Any], file_path: str) -> bool:
        """تصدير التقرير إلى Excel"""
        try:
            from openpyxl import Workbook
            from openpyxl.styles import Font, Alignment, PatternFill
            
            wb = Workbook()
            ws = wb.active
            ws.title = report_data['title']
            
            # العنوان
            ws['A1'] = report_data['title']
            ws['A1'].font = Font(size=16, bold=True)
            ws['A1'].alignment = Alignment(horizontal='center')
            
            # تاريخ الإنشاء
            ws['A2'] = f"تاريخ الإنشاء: {report_data['generated_at'].strftime('%Y-%m-%d %H:%M')}"
            
            # محتوى التقرير حسب النوع
            if 'students' in report_data:
                self._add_students_excel_content(ws, report_data)
            elif 'daily_attendance' in report_data:
                self._add_attendance_excel_content(ws, report_data)
            elif 'grades' in report_data:
                self._add_grades_excel_content(ws, report_data)
            
            wb.save(file_path)
            return True
            
        except ImportError:
            print("مكتبة openpyxl غير مثبتة")
            return False
        except Exception as e:
            print(f"خطأ في إنشاء Excel: {e}")
            return False
    
    def _add_students_excel_content(self, ws, report_data):
        """إضافة محتوى تقرير الطلاب لـ Excel"""
        # العناوين
        headers = ['الكود', 'الاسم', 'النوع', 'المرحلة', 'الصف', 'جغرافيا', 'تاريخ']
        for col, header in enumerate(headers, 1):
            ws.cell(row=4, column=col, value=header)
        
        # البيانات
        for row, student in enumerate(report_data['students'], 5):
            ws.cell(row=row, column=1, value=student['student_code'])
            ws.cell(row=row, column=2, value=student['full_name'])
            ws.cell(row=row, column=3, value=student['gender'])
            ws.cell(row=row, column=4, value=student['stage'])
            ws.cell(row=row, column=5, value=student['grade'])
            ws.cell(row=row, column=6, value=student['geography_score'])
            ws.cell(row=row, column=7, value=student['history_score'])
    
    def _add_attendance_excel_content(self, ws, report_data):
        """إضافة محتوى تقرير الحضور لـ Excel"""
        # العناوين
        headers = ['التاريخ', 'إجمالي', 'حاضر', 'غائب', 'متأخر', 'معدل الحضور']
        for col, header in enumerate(headers, 1):
            ws.cell(row=4, column=col, value=header)
        
        # البيانات
        for row, daily in enumerate(report_data['daily_attendance'], 5):
            ws.cell(row=row, column=1, value=str(daily['date']))
            ws.cell(row=row, column=2, value=daily['total'])
            ws.cell(row=row, column=3, value=daily['present'])
            ws.cell(row=row, column=4, value=daily['absent'])
            ws.cell(row=row, column=5, value=daily['late'])
            
            if daily['total'] > 0:
                rate = (daily['present'] + daily['late']) / daily['total'] * 100
                ws.cell(row=row, column=6, value=f"{rate:.1f}%")
    
    def _add_grades_excel_content(self, ws, report_data):
        """إضافة محتوى تقرير الدرجات لـ Excel"""
        # العناوين
        headers = ['الكود', 'الاسم', 'الصف', 'المادة', 'نوع الامتحان', 'الدرجة', 'من', 'النسبة']
        for col, header in enumerate(headers, 1):
            ws.cell(row=4, column=col, value=header)
        
        # البيانات
        for row, grade in enumerate(report_data['grades'], 5):
            ws.cell(row=row, column=1, value=grade['student_code'])
            ws.cell(row=row, column=2, value=grade['full_name'])
            ws.cell(row=row, column=3, value=grade['grade'])
            ws.cell(row=row, column=4, value=grade['subject'])
            ws.cell(row=row, column=5, value=grade['exam_type'])
            ws.cell(row=row, column=6, value=grade['score'])
            ws.cell(row=row, column=7, value=grade['max_score'])
            
            percentage = (grade['score'] / grade['max_score']) * 100
            ws.cell(row=row, column=8, value=f"{percentage:.1f}%")
