# -*- coding: utf-8 -*-
"""
أنماط التطبيق
Application Styles
"""

# الألوان الأساسية
COLORS = {
    'primary': '#2c3e50',
    'secondary': '#34495e',
    'success': '#27ae60',
    'warning': '#f39c12',
    'danger': '#e74c3c',
    'info': '#3498db',
    'light': '#ecf0f1',
    'dark': '#2c3e50',
    'white': '#ffffff',
    'gray': '#bdc3c7',
    'gray_light': '#f8f9fa',
    'gray_dark': '#7f8c8d'
}

# الخطوط
FONTS = {
    'primary': 'Arial',
    'secondary': 'Tahoma',
    'size_small': '10px',
    'size_normal': '12px',
    'size_large': '14px',
    'size_xlarge': '16px',
    'size_title': '18px'
}

def get_main_style():
    """الحصول على الأنماط الرئيسية للتطبيق"""
    return f"""
    /* الأنماط العامة */
    QWidget {{
        font-family: '{FONTS['primary']}', sans-serif;
        font-size: {FONTS['size_normal']};
        color: {COLORS['dark']};
        background-color: transparent;
    }}

    /* النوافذ الرئيسية */
    QMainWindow {{
        background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #f0f8ff, stop:0.5 #e6f3ff, stop:1 #ddeeff);
    }}

    /* العناوين */
    QLabel[objectName="title"] {{
        font-size: {FONTS['size_title']};
        font-weight: bold;
        color: {COLORS['primary']};
        padding: 10px;
    }}

    QLabel[objectName="subtitle"] {{
        font-size: {FONTS['size_large']};
        color: {COLORS['secondary']};
        padding: 5px;
    }}

    /* الأزرار */
    QPushButton {{
        background-color: {COLORS['info']};
        color: {COLORS['white']};
        border: none;
        padding: 10px 20px;
        border-radius: 6px;
        font-weight: bold;
        font-size: {FONTS['size_normal']};
        min-height: 20px;
    }}

    QPushButton:hover {{
        background-color: #2980b9;
        transform: translateY(-1px);
    }}

    QPushButton:pressed {{
        background-color: #21618c;
        transform: translateY(1px);
    }}

    QPushButton:disabled {{
        background-color: {COLORS['gray']};
        color: {COLORS['gray_dark']};
    }}

    /* أزرار النجاح */
    QPushButton[objectName="successButton"] {{
        background-color: {COLORS['success']};
    }}

    QPushButton[objectName="successButton"]:hover {{
        background-color: #229954;
    }}

    /* أزرار التحذير */
    QPushButton[objectName="warningButton"] {{
        background-color: {COLORS['warning']};
    }}

    QPushButton[objectName="warningButton"]:hover {{
        background-color: #e67e22;
    }}

    /* أزرار الخطر */
    QPushButton[objectName="dangerButton"] {{
        background-color: {COLORS['danger']};
    }}

    QPushButton[objectName="dangerButton"]:hover {{
        background-color: #c0392b;
    }}

    /* حقول الإدخال */
    QLineEdit, QTextEdit {{
        border: 2px solid {COLORS['gray']};
        border-radius: 5px;
        padding: 8px;
        background-color: {COLORS['white']};
        font-size: {FONTS['size_normal']};
    }}

    QLineEdit:focus, QTextEdit:focus {{
        border-color: {COLORS['info']};
        background-color: #f8f9ff;
    }}

    QLineEdit:disabled, QTextEdit:disabled {{
        background-color: {COLORS['gray_light']};
        color: {COLORS['gray_dark']};
    }}

    /* القوائم المنسدلة */
    QComboBox {{
        border: 2px solid {COLORS['gray']};
        border-radius: 5px;
        padding: 8px;
        background-color: {COLORS['white']};
        font-size: {FONTS['size_normal']};
        min-height: 20px;
    }}

    QComboBox:focus {{
        border-color: {COLORS['info']};
    }}

    QComboBox::drop-down {{
        border: none;
        width: 30px;
    }}

    QComboBox::down-arrow {{
        image: none;
        border-left: 5px solid transparent;
        border-right: 5px solid transparent;
        border-top: 5px solid {COLORS['gray_dark']};
        margin-right: 10px;
    }}

    /* الجداول */
    QTableWidget {{
        gridline-color: {COLORS['gray']};
        background-color: {COLORS['white']};
        alternate-background-color: {COLORS['gray_light']};
        border: 1px solid {COLORS['gray']};
        border-radius: 5px;
    }}

    QTableWidget::item {{
        padding: 8px;
        border: none;
    }}

    QTableWidget::item:selected {{
        background-color: {COLORS['info']};
        color: {COLORS['white']};
    }}

    QTableWidget::item:hover {{
        background-color: #e3f2fd;
    }}

    QHeaderView::section {{
        background-color: {COLORS['primary']};
        color: {COLORS['white']};
        padding: 10px;
        border: none;
        font-weight: bold;
        font-size: {FONTS['size_normal']};
    }}

    QHeaderView::section:hover {{
        background-color: {COLORS['secondary']};
    }}

    /* المجموعات */
    QGroupBox {{
        font-weight: bold;
        font-size: {FONTS['size_normal']};
        border: 2px solid {COLORS['gray']};
        border-radius: 8px;
        margin-top: 1ex;
        padding-top: 15px;
        background-color: {COLORS['white']};
    }}

    QGroupBox::title {{
        subcontrol-origin: margin;
        left: 15px;
        padding: 0 10px 0 10px;
        color: {COLORS['primary']};
        background-color: {COLORS['white']};
    }}

    /* التبويبات */
    QTabWidget::pane {{
        border: 2px solid {COLORS['gray']};
        border-radius: 5px;
        background-color: {COLORS['white']};
        top: -1px;
    }}

    QTabBar::tab {{
        background-color: {COLORS['gray_light']};
        color: {COLORS['dark']};
        padding: 10px 20px;
        margin-right: 2px;
        border-top-left-radius: 5px;
        border-top-right-radius: 5px;
        font-weight: bold;
    }}

    QTabBar::tab:selected {{
        background-color: {COLORS['white']};
        border-bottom: 3px solid {COLORS['info']};
    }}

    QTabBar::tab:hover {{
        background-color: #d5dbdb;
    }}

    /* شريط التقدم */
    QProgressBar {{
        border: 2px solid {COLORS['gray']};
        border-radius: 5px;
        text-align: center;
        font-weight: bold;
        background-color: {COLORS['gray_light']};
    }}

    QProgressBar::chunk {{
        background-color: {COLORS['success']};
        border-radius: 3px;
    }}

    /* خانات الاختيار */
    QCheckBox {{
        font-size: {FONTS['size_normal']};
        color: {COLORS['dark']};
    }}

    QCheckBox::indicator {{
        width: 18px;
        height: 18px;
        border: 2px solid {COLORS['gray']};
        border-radius: 3px;
        background-color: {COLORS['white']};
    }}

    QCheckBox::indicator:checked {{
        background-color: {COLORS['success']};
        border-color: {COLORS['success']};
    }}

    QCheckBox::indicator:hover {{
        border-color: {COLORS['info']};
    }}

    /* أزرار الراديو */
    QRadioButton {{
        font-size: {FONTS['size_normal']};
        color: {COLORS['dark']};
    }}

    QRadioButton::indicator {{
        width: 18px;
        height: 18px;
        border: 2px solid {COLORS['gray']};
        border-radius: 9px;
        background-color: {COLORS['white']};
    }}

    QRadioButton::indicator:checked {{
        background-color: {COLORS['info']};
        border-color: {COLORS['info']};
    }}

    /* مربعات الأرقام */
    QSpinBox, QDoubleSpinBox {{
        border: 2px solid {COLORS['gray']};
        border-radius: 5px;
        padding: 8px;
        background-color: {COLORS['white']};
        font-size: {FONTS['size_normal']};
    }}

    QSpinBox:focus, QDoubleSpinBox:focus {{
        border-color: {COLORS['info']};
    }}

    /* اختيار التاريخ */
    QDateEdit {{
        border: 2px solid {COLORS['gray']};
        border-radius: 5px;
        padding: 8px;
        background-color: {COLORS['white']};
        font-size: {FONTS['size_normal']};
    }}

    QDateEdit:focus {{
        border-color: {COLORS['info']};
    }}

    /* شريط الحالة */
    QStatusBar {{
        background-color: {COLORS['primary']};
        color: {COLORS['white']};
        font-weight: bold;
        padding: 5px;
    }}

    /* شريط القوائم */
    QMenuBar {{
        background-color: {COLORS['primary']};
        color: {COLORS['white']};
        font-weight: bold;
    }}

    QMenuBar::item {{
        background-color: transparent;
        padding: 8px 16px;
    }}

    QMenuBar::item:selected {{
        background-color: {COLORS['secondary']};
        border-radius: 3px;
    }}

    QMenu {{
        background-color: {COLORS['white']};
        border: 1px solid {COLORS['gray']};
        border-radius: 5px;
    }}

    QMenu::item {{
        padding: 8px 20px;
        color: {COLORS['dark']};
    }}

    QMenu::item:selected {{
        background-color: {COLORS['info']};
        color: {COLORS['white']};
    }}

    /* أشرطة التمرير */
    QScrollBar:vertical {{
        background-color: {COLORS['gray_light']};
        width: 12px;
        border-radius: 6px;
    }}

    QScrollBar::handle:vertical {{
        background-color: {COLORS['gray']};
        border-radius: 6px;
        min-height: 20px;
    }}

    QScrollBar::handle:vertical:hover {{
        background-color: {COLORS['gray_dark']};
    }}

    QScrollBar:horizontal {{
        background-color: {COLORS['gray_light']};
        height: 12px;
        border-radius: 6px;
    }}

    QScrollBar::handle:horizontal {{
        background-color: {COLORS['gray']};
        border-radius: 6px;
        min-width: 20px;
    }}

    QScrollBar::handle:horizontal:hover {{
        background-color: {COLORS['gray_dark']};
    }}
    """

def get_login_style():
    """أنماط نافذة تسجيل الدخول"""
    return f"""
    {get_main_style()}

    /* نافذة تسجيل الدخول */
    QWidget[objectName="loginWindow"] {{
        background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 {COLORS['info']}, stop:1 {COLORS['primary']});
    }}

    QFrame[objectName="loginFrame"] {{
        background-color: {COLORS['white']};
        border: none;
        border-radius: 15px;
        padding: 30px;
    }}

    QLabel[objectName="title"] {{
        font-size: 24px;
        font-weight: bold;
        color: {COLORS['primary']};
        margin: 15px 0;
    }}

    QLabel[objectName="teacher"] {{
        font-size: 16px;
        color: {COLORS['secondary']};
        margin-bottom: 15px;
    }}

    QPushButton[objectName="loginButton"] {{
        background-color: {COLORS['success']};
        font-size: 16px;
        padding: 12px;
        border-radius: 8px;
    }}

    QPushButton[objectName="loginButton"]:hover {{
        background-color: #229954;
        transform: scale(1.02);
    }}

    QPushButton[objectName="exitButton"] {{
        background-color: {COLORS['danger']};
        font-size: 16px;
        padding: 12px;
        border-radius: 8px;
    }}

    QPushButton[objectName="exitButton"]:hover {{
        background-color: #c0392b;
        transform: scale(1.02);
    }}

    QLabel[objectName="info"] {{
        font-size: 11px;
        color: {COLORS['gray_dark']};
        background-color: {COLORS['gray_light']};
        padding: 15px;
        border-radius: 8px;
        border: 1px solid {COLORS['gray']};
    }}
    """

def get_dashboard_style():
    """أنماط الشاشة الرئيسية"""
    return f"""
    {get_main_style()}

    /* الشاشة الرئيسية */
    QFrame[objectName="welcomeFrame"] {{
        background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 {COLORS['primary']}, stop:1 {COLORS['secondary']});
        border-radius: 12px;
        padding: 25px;
        margin: 15px;
    }}

    QLabel[objectName="welcomeLabel"] {{
        color: {COLORS['white']};
        font-size: 28px;
        font-weight: bold;
        margin: 0;
    }}

    QLabel[objectName="dateLabel"] {{
        color: {COLORS['gray']};
        font-size: 16px;
        margin: 5px 0;
    }}

    /* بطاقات الإحصائيات */
    QFrame[objectName="statWidget"] {{
        border-radius: 12px;
        padding: 25px;
        margin: 8px;
        border: none;
    }}

    QLabel[objectName="statValue"] {{
        color: {COLORS['white']};
        font-size: 32px;
        font-weight: bold;
        margin: 0;
    }}

    QLabel[objectName="statTitle"] {{
        color: {COLORS['white']};
        font-size: 14px;
        font-weight: bold;
        margin: 5px 0;
    }}

    /* الأزرار الرئيسية */
    QPushButton[objectName="mainButton"] {{
        background-color: {COLORS['white']};
        border: 3px solid {COLORS['gray']};
        border-radius: 15px;
        padding: 25px;
        min-height: 120px;
        min-width: 200px;
    }}

    QPushButton[objectName="mainButton"]:hover {{
        border-color: {COLORS['info']};
        background-color: #f8f9ff;
        transform: translateY(-3px);
    }}

    QPushButton[objectName="mainButton"]:pressed {{
        background-color: {COLORS['gray_light']};
        transform: translateY(1px);
    }}

    QLabel[objectName="buttonTitle"] {{
        font-size: 18px;
        font-weight: bold;
        color: {COLORS['primary']};
        margin: 0;
    }}

    QLabel[objectName="buttonDesc"] {{
        font-size: 13px;
        color: {COLORS['gray_dark']};
        margin: 5px 0;
    }}
    """

def get_form_style():
    """أنماط النماذج"""
    return f"""
    {get_main_style()}

    /* النماذج */
    QFormLayout {{
        spacing: 15px;
    }}

    QFormLayout QLabel {{
        font-weight: bold;
        color: {COLORS['primary']};
        min-width: 120px;
    }}

    /* حقول الإدخال في النماذج */
    QLineEdit, QComboBox, QSpinBox, QDoubleSpinBox, QDateEdit {{
        min-height: 35px;
        font-size: {FONTS['size_normal']};
    }}

    /* أزرار النماذج */
    QPushButton {{
        min-height: 35px;
        min-width: 120px;
        font-size: {FONTS['size_normal']};
    }}
    """

def get_table_style():
    """أنماط الجداول المحسنة"""
    return f"""
    {get_main_style()}

    /* الجداول المحسنة */
    QTableWidget {{
        gridline-color: #e0e0e0;
        background-color: {COLORS['white']};
        alternate-background-color: #f9f9f9;
        border: 2px solid {COLORS['gray']};
        border-radius: 8px;
        font-size: {FONTS['size_normal']};
    }}

    QTableWidget::item {{
        padding: 12px 8px;
        border: none;
    }}

    QTableWidget::item:selected {{
        background-color: {COLORS['info']};
        color: {COLORS['white']};
    }}

    QTableWidget::item:hover {{
        background-color: #e3f2fd;
    }}

    QHeaderView::section {{
        background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {COLORS['primary']}, stop:1 {COLORS['secondary']});
        color: {COLORS['white']};
        padding: 12px 8px;
        border: none;
        font-weight: bold;
        font-size: {FONTS['size_normal']};
    }}

    QHeaderView::section:hover {{
        background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {COLORS['secondary']}, stop:1 {COLORS['primary']});
    }}
    """

def apply_animations():
    """تطبيق التأثيرات المتحركة"""
    return """
    /* تأثيرات الانتقال */
    QPushButton {
        transition: all 0.3s ease;
    }

    QPushButton:hover {
        transition: all 0.2s ease;
    }

    QLineEdit:focus, QComboBox:focus {
        transition: border-color 0.3s ease;
    }

    QTableWidget::item:hover {
        transition: background-color 0.2s ease;
    }
    """

def get_arabic_font_style():
    """أنماط الخطوط العربية"""
    return f"""
    /* دعم الخطوط العربية */
    QWidget {{
        font-family: 'Segoe UI', 'Tahoma', 'Arial', sans-serif;
        direction: rtl;
    }}

    QLineEdit, QTextEdit {{
        text-align: right;
        direction: rtl;
    }}

    QComboBox {{
        text-align: right;
        direction: rtl;
    }}

    QTableWidget {{
        direction: rtl;
    }}

    QLabel {{
        direction: rtl;
        text-align: right;
    }}
    """