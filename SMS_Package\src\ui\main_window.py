# -*- coding: utf-8 -*-
"""
النافذة الرئيسية
Main Window
"""

from PyQt5.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, 
                            QLabel, QPushButton, QFrame, QMenuBar, QMenu,
                            QAction, QStatusBar, QMessageBox, QGridLayout)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont, QIcon

from ..utils.auth import AuthManager
from ..models.student import Student
from ..models.attendance import Attendance
from ..models.grades import Grades
from .students_window import StudentsWindow
from .attendance_window import AttendanceWindow
from .grades_window import GradesWindow
from .reports_window import ReportsWindow
from .settings_window import SettingsWindow
from ..utils.styles import get_dashboard_style, get_arabic_font_style

class MainWindow(QMainWindow):
    """النافذة الرئيسية للتطبيق"""
    
    # إشارة تسجيل الخروج
    logout_signal = pyqtSignal()
    
    def __init__(self, auth_manager: AuthManager):
        super().__init__()
        self.auth_manager = auth_manager

        # تهيئة النماذج
        self.student_model = Student(auth_manager.db)
        self.attendance_model = Attendance(auth_manager.db)
        self.grades_model = Grades(auth_manager.db)

        self.init_ui()
        self.setup_menu()
        self.setup_status_bar()
        self.setup_styles()
        self.load_statistics()
    
    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        self.setWindowTitle("نظام إدارة الطلاب - مستر أحمد عادل")
        self.setGeometry(100, 100, 1200, 800)
        
        # الويدجت المركزي
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout()

        # إضافة شريط التنقل
        nav_bar = self.create_navigation_bar()
        main_layout.addWidget(nav_bar)

        # إطار الترحيب
        welcome_frame = self.create_welcome_frame()
        main_layout.addWidget(welcome_frame)
        
        # إطار الإحصائيات السريعة
        stats_frame = self.create_stats_frame()
        main_layout.addWidget(stats_frame)
        
        # إطار الأزرار الرئيسية
        buttons_frame = self.create_main_buttons_frame()
        main_layout.addWidget(buttons_frame)
        
        main_layout.addStretch()
        central_widget.setLayout(main_layout)

    def create_navigation_bar(self):
        """إنشاء شريط التنقل"""
        nav_frame = QFrame()
        nav_frame.setObjectName("navigationBar")
        nav_frame.setStyleSheet("""
            QFrame#navigationBar {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                            stop:0 #2c3e50, stop:1 #34495e);
                border: none;
                border-radius: 8px;
                padding: 10px;
                margin: 5px;
            }
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 12px 20px;
                border-radius: 6px;
                font-size: 14px;
                font-weight: bold;
                margin: 0 5px;
                min-width: 120px;
            }
            QPushButton:hover {
                background-color: #2980b9;
                transform: translateY(-2px);
            }
            QPushButton:pressed {
                background-color: #21618c;
                transform: translateY(1px);
            }
            QPushButton#activeButton {
                background-color: #27ae60;
            }
            QPushButton#activeButton:hover {
                background-color: #229954;
            }
        """)

        layout = QHBoxLayout()
        layout.setSpacing(10)

        # أزرار التنقل
        self.nav_buttons = {}

        # زر الرئيسية
        home_btn = QPushButton("🏠 الرئيسية")
        home_btn.setObjectName("activeButton")
        home_btn.clicked.connect(self.show_dashboard)
        self.nav_buttons['home'] = home_btn
        layout.addWidget(home_btn)

        # زر إدارة الطلاب
        students_btn = QPushButton("👥 إدارة الطلاب")
        students_btn.clicked.connect(self.open_students_window)
        self.nav_buttons['students'] = students_btn
        layout.addWidget(students_btn)

        # زر تسجيل الحضور
        attendance_btn = QPushButton("📋 تسجيل الحضور")
        attendance_btn.clicked.connect(self.open_attendance_window)
        self.nav_buttons['attendance'] = attendance_btn
        layout.addWidget(attendance_btn)

        # زر إدارة الدرجات
        grades_btn = QPushButton("📊 إدارة الدرجات")
        grades_btn.clicked.connect(self.open_grades_window)
        self.nav_buttons['grades'] = grades_btn
        layout.addWidget(grades_btn)

        # زر التقارير
        reports_btn = QPushButton("📈 التقارير")
        reports_btn.clicked.connect(self.open_reports_window)
        self.nav_buttons['reports'] = reports_btn
        layout.addWidget(reports_btn)

        # زر الإعدادات
        settings_btn = QPushButton("⚙️ الإعدادات")
        settings_btn.clicked.connect(self.open_settings_window)
        self.nav_buttons['settings'] = settings_btn
        layout.addWidget(settings_btn)

        # مساحة فارغة
        layout.addStretch()

        # زر تسجيل الخروج
        logout_btn = QPushButton("🚪 تسجيل الخروج")
        logout_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        logout_btn.clicked.connect(self.logout)
        layout.addWidget(logout_btn)

        nav_frame.setLayout(layout)
        return nav_frame

    def set_active_button(self, button_name):
        """تعيين الزر النشط"""
        for name, button in self.nav_buttons.items():
            if name == button_name:
                button.setObjectName("activeButton")
            else:
                button.setObjectName("")
            button.style().unpolish(button)
            button.style().polish(button)

    def show_dashboard(self):
        """إظهار لوحة التحكم"""
        self.set_active_button('home')
        # إغلاق النوافذ المفتوحة
        self.close_all_windows()

    def close_all_windows(self):
        """إغلاق جميع النوافذ المفتوحة"""
        if hasattr(self, 'students_window') and self.students_window:
            self.students_window.close()
            self.students_window = None
        if hasattr(self, 'attendance_window') and self.attendance_window:
            self.attendance_window.close()
            self.attendance_window = None
        if hasattr(self, 'grades_window') and self.grades_window:
            self.grades_window.close()
            self.grades_window = None
        if hasattr(self, 'reports_window') and self.reports_window:
            self.reports_window.close()
            self.reports_window = None
        if hasattr(self, 'settings_window') and self.settings_window:
            self.settings_window.close()
            self.settings_window = None

    def create_welcome_frame(self):
        """إنشاء إطار الترحيب"""
        frame = QFrame()
        frame.setObjectName("welcomeFrame")
        layout = QVBoxLayout()
        
        # رسالة الترحيب
        user = self.auth_manager.get_current_user()
        welcome_label = QLabel(f"مرحباً {user['full_name']}")
        welcome_label.setAlignment(Qt.AlignCenter)
        welcome_label.setObjectName("welcomeLabel")
        
        # تاريخ اليوم
        from datetime import datetime
        today = datetime.now().strftime("%Y-%m-%d")
        date_label = QLabel(f"التاريخ: {today}")
        date_label.setAlignment(Qt.AlignCenter)
        date_label.setObjectName("dateLabel")
        
        layout.addWidget(welcome_label)
        layout.addWidget(date_label)
        
        frame.setLayout(layout)
        return frame
    
    def create_stats_frame(self):
        """إنشاء إطار الإحصائيات السريعة"""
        frame = QFrame()
        frame.setObjectName("statsFrame")
        self.stats_layout = QGridLayout()

        # إحصائيات افتراضية (سيتم تحديثها)
        self.stats_widgets = {}
        stats = [
            ("إجمالي الطلاب", "0", "#3498db"),
            ("الحاضرين اليوم", "0", "#27ae60"),
            ("الغائبين اليوم", "0", "#e74c3c"),
            ("متوسط الدرجات", "0%", "#f39c12")
        ]

        for i, (title, value, color) in enumerate(stats):
            stat_widget = self.create_stat_widget(title, value, color)
            self.stats_widgets[title] = stat_widget
            self.stats_layout.addWidget(stat_widget, 0, i)

        frame.setLayout(self.stats_layout)
        return frame
    
    def create_stat_widget(self, title, value, color):
        """إنشاء ويدجت إحصائية"""
        widget = QFrame()
        widget.setObjectName("statWidget")
        widget.setStyleSheet(f"""
            #statWidget {{
                background-color: {color};
                border-radius: 10px;
                padding: 20px;
                margin: 5px;
            }}
        """)
        
        layout = QVBoxLayout()
        
        value_label = QLabel(value)
        value_label.setAlignment(Qt.AlignCenter)
        value_label.setObjectName("statValue")
        
        title_label = QLabel(title)
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setObjectName("statTitle")
        
        layout.addWidget(value_label)
        layout.addWidget(title_label)
        
        widget.setLayout(layout)
        return widget
    
    def create_main_buttons_frame(self):
        """إنشاء إطار الأزرار الرئيسية"""
        frame = QFrame()
        layout = QGridLayout()
        layout.setSpacing(20)
        
        # أزرار الوظائف الرئيسية
        buttons = [
            ("إدارة الطلاب", "إضافة وتعديل وحذف الطلاب", self.open_students_management),
            ("تسجيل الحضور", "تسجيل حضور وغياب الطلاب", self.open_attendance),
            ("إدارة الدرجات", "إدخال وتعديل درجات الطلاب", self.open_grades_management),
            ("التقارير", "إنشاء وطباعة التقارير", self.open_reports),
            ("الإعدادات", "إعدادات التطبيق العامة", self.open_settings),
            ("تسجيل الخروج", "الخروج من التطبيق", self.logout)
        ]
        
        for i, (title, description, callback) in enumerate(buttons):
            button = self.create_main_button(title, description, callback)
            row = i // 3
            col = i % 3
            layout.addWidget(button, row, col)
        
        frame.setLayout(layout)
        return frame
    
    def create_main_button(self, title, description, callback):
        """إنشاء زر رئيسي"""
        button = QPushButton()
        button.setObjectName("mainButton")
        button.clicked.connect(callback)
        
        layout = QVBoxLayout()
        
        title_label = QLabel(title)
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setObjectName("buttonTitle")
        
        desc_label = QLabel(description)
        desc_label.setAlignment(Qt.AlignCenter)
        desc_label.setObjectName("buttonDesc")
        desc_label.setWordWrap(True)
        
        layout.addWidget(title_label)
        layout.addWidget(desc_label)
        
        button.setLayout(layout)
        return button
    
    def setup_menu(self):
        """إعداد شريط القوائم"""
        menubar = self.menuBar()
        
        # قائمة الملف
        file_menu = menubar.addMenu('ملف')
        
        logout_action = QAction('تسجيل الخروج', self)
        logout_action.triggered.connect(self.logout)
        file_menu.addAction(logout_action)
        
        file_menu.addSeparator()
        
        exit_action = QAction('خروج', self)
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        # قائمة الطلاب
        students_menu = menubar.addMenu('الطلاب')
        
        manage_students_action = QAction('إدارة الطلاب', self)
        manage_students_action.triggered.connect(self.open_students_management)
        students_menu.addAction(manage_students_action)
        
        attendance_action = QAction('تسجيل الحضور', self)
        attendance_action.triggered.connect(self.open_attendance)
        students_menu.addAction(attendance_action)
        
        grades_action = QAction('إدارة الدرجات', self)
        grades_action.triggered.connect(self.open_grades_management)
        students_menu.addAction(grades_action)
        
        # قائمة التقارير
        reports_menu = menubar.addMenu('التقارير')
        
        reports_action = QAction('إنشاء التقارير', self)
        reports_action.triggered.connect(self.open_reports)
        reports_menu.addAction(reports_action)
        
        # قائمة المساعدة
        help_menu = menubar.addMenu('مساعدة')
        
        about_action = QAction('حول التطبيق', self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)
    
    def setup_status_bar(self):
        """إعداد شريط الحالة"""
        status_bar = self.statusBar()
        
        user = self.auth_manager.get_current_user()
        status_bar.showMessage(f"مسجل الدخول: {user['full_name']} | نظام إدارة الطلاب v1.0")
    
    def setup_styles(self):
        """تطبيق الأنماط"""
        # تطبيق الأنماط الجديدة
        style = get_dashboard_style() + get_arabic_font_style()
        self.setStyleSheet(style)

    def load_statistics(self):
        """تحميل الإحصائيات الحقيقية"""
        try:
            # إحصائيات الطلاب
            student_stats = self.student_model.get_student_statistics()
            total_students = student_stats.get('total_students', 0)

            # إحصائيات الحضور لليوم
            from datetime import date
            today = date.today()
            daily_attendance = self.attendance_model.get_daily_attendance(today)

            present_count = sum(1 for record in daily_attendance if record.get('status') == 'حاضر')
            absent_count = sum(1 for record in daily_attendance if record.get('status') == 'غائب' or record.get('status') is None)

            # متوسط الدرجات
            all_students = self.student_model.get_all_students()
            if all_students:
                total_geography = sum(s.get('geography_score', 0) for s in all_students)
                total_history = sum(s.get('history_score', 0) for s in all_students)
                avg_score = (total_geography + total_history) / (2 * len(all_students))
            else:
                avg_score = 0

            # تحديث الويدجتات
            self.update_stat_widget("إجمالي الطلاب", str(total_students))
            self.update_stat_widget("الحاضرين اليوم", str(present_count))
            self.update_stat_widget("الغائبين اليوم", str(absent_count))
            self.update_stat_widget("متوسط الدرجات", f"{avg_score:.1f}%")

        except Exception as e:
            print(f"خطأ في تحميل الإحصائيات: {e}")

    def update_stat_widget(self, title, value):
        """تحديث قيمة ويدجت إحصائية"""
        if title in self.stats_widgets:
            widget = self.stats_widgets[title]
            layout = widget.layout()
            if layout and layout.count() > 0:
                value_label = layout.itemAt(0).widget()
                if value_label:
                    value_label.setText(value)

    # دوال معالجة الأحداث (ستتم إضافة التفاصيل لاحقاً)
    def open_students_window(self):
        """فتح نافذة إدارة الطلاب"""
        try:
            self.set_active_button('students')
            self.close_all_windows()
            self.students_window = StudentsWindow(self.auth_manager.db)
            self.students_window.show()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في فتح نافذة إدارة الطلاب: {str(e)}")

    def open_students_management(self):
        """فتح نافذة إدارة الطلاب (للتوافق مع الكود القديم)"""
        self.open_students_window()
    
    def open_attendance_window(self):
        """فتح نافذة تسجيل الحضور"""
        try:
            self.set_active_button('attendance')
            self.close_all_windows()
            self.attendance_window = AttendanceWindow(self.auth_manager.db)
            self.attendance_window.show()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في فتح نافذة تسجيل الحضور: {str(e)}")

    def open_attendance(self):
        """فتح نافذة تسجيل الحضور (للتوافق مع الكود القديم)"""
        self.open_attendance_window()
    
    def open_grades_window(self):
        """فتح نافذة إدارة الدرجات"""
        try:
            self.set_active_button('grades')
            self.close_all_windows()
            self.grades_window = GradesWindow(self.auth_manager.db)
            self.grades_window.show()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في فتح نافذة إدارة الدرجات: {str(e)}")

    def open_grades_management(self):
        """فتح نافذة إدارة الدرجات (للتوافق مع الكود القديم)"""
        self.open_grades_window()

    def open_reports_window(self):
        """فتح نافذة التقارير"""
        try:
            self.set_active_button('reports')
            self.close_all_windows()
            self.reports_window = ReportsWindow(self.auth_manager.db)
            self.reports_window.show()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في فتح نافذة التقارير: {str(e)}")

    def open_settings_window(self):
        """فتح نافذة الإعدادات"""
        try:
            self.set_active_button('settings')
            self.close_all_windows()
            self.settings_window = SettingsWindow(self.auth_manager.db)
            self.settings_window.show()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في فتح نافذة الإعدادات: {str(e)}")

    def logout(self):
        """تسجيل الخروج"""
        reply = QMessageBox.question(self, "تأكيد", "هل تريد تسجيل الخروج؟",
                                   QMessageBox.Yes | QMessageBox.No)
        if reply == QMessageBox.Yes:
            self.close_all_windows()
            self.logout_signal.emit()
    
    def open_reports(self):
        """فتح نافذة التقارير"""
        try:
            self.reports_window = ReportsWindow(self.auth_manager.db)
            self.reports_window.show()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في فتح نافذة التقارير: {str(e)}")
    
    def open_settings(self):
        """فتح نافذة الإعدادات"""
        try:
            self.settings_window = SettingsWindow(self.auth_manager.db, self.auth_manager)
            self.settings_window.show()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في فتح نافذة الإعدادات: {str(e)}")
    
    def logout(self):
        """تسجيل الخروج"""
        reply = QMessageBox.question(
            self, 'تأكيد تسجيل الخروج',
            'هل تريد تسجيل الخروج؟',
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            self.auth_manager.logout()
            self.logout_signal.emit()
    
    def show_about(self):
        """إظهار معلومات التطبيق"""
        QMessageBox.about(self, "حول التطبيق", 
                         "نظام إدارة الطلاب\n"
                         "مستر أحمد عادل\n"
                         "الإصدار 1.0\n"
                         "تم التطوير بواسطة: مساعد الذكي")
    
    def closeEvent(self, event):
        """معالجة إغلاق النافذة"""
        self.logout()
        event.accept()
