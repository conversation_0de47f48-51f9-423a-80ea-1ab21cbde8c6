#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
بناء تطبيق مستقل (EXE)
Build Standalone Application (EXE)

هذا الملف ينشئ ملف EXE يعمل بدون الحاجة لـ Python
"""

import os
import sys
import subprocess
from pathlib import Path

def check_pyinstaller():
    """التحقق من وجود PyInstaller وتثبيته إذا لم يكن موجوداً"""
    try:
        import PyInstaller
        print("✅ PyInstaller موجود")
        return True
    except ImportError:
        print("📦 تثبيت PyInstaller...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])
            print("✅ تم تثبيت PyInstaller بنجاح")
            return True
        except subprocess.CalledProcessError:
            print("❌ فشل في تثبيت PyInstaller")
            return False

def create_spec_file():
    """إنشاء ملف spec محسن"""
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['main.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('src', 'src'),
        ('data', 'data'),
        ('assets', 'assets'),
    ],
    hiddenimports=[
        'PyQt5.QtCore',
        'PyQt5.QtGui', 
        'PyQt5.QtWidgets',
        'PyQt5.QtPrintSupport',
        'sqlite3',
        'datetime',
        'pathlib',
        'hashlib',
        'logging',
        'json',
        'csv',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        'tkinter',
        'matplotlib',
        'numpy',
        'pandas',
        'scipy',
        'PIL',
        'cv2',
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='StudentManagementSystem',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='assets/icon.ico' if os.path.exists('assets/icon.ico') else None,
)
'''
    
    with open('standalone.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print("✅ تم إنشاء ملف standalone.spec")

def build_exe():
    """بناء ملف EXE"""
    print("🏗️ بدء بناء ملف EXE...")
    
    try:
        # تشغيل PyInstaller
        cmd = [
            'pyinstaller',
            '--clean',
            '--noconfirm',
            'standalone.spec'
        ]
        
        print(f"تشغيل: {' '.join(cmd)}")
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ تم بناء ملف EXE بنجاح!")
            return True
        else:
            print("❌ فشل في بناء ملف EXE")
            print("الخطأ:", result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ خطأ في بناء ملف EXE: {e}")
        return False

def create_installer():
    """إنشاء ملف تثبيت"""
    installer_content = '''@echo off
chcp 65001 >nul
title تثبيت نظام إدارة الطلاب

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    تثبيت نظام إدارة الطلاب                  ║
echo ║                   مستر أحمد عادل                           ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

REM إنشاء مجلد التثبيت
set "INSTALL_DIR=%USERPROFILE%\\Desktop\\نظام إدارة الطلاب"
if not exist "%INSTALL_DIR%" mkdir "%INSTALL_DIR%"

echo 📁 نسخ الملفات إلى: %INSTALL_DIR%
echo.

REM نسخ ملف EXE
if exist "StudentManagementSystem.exe" (
    copy "StudentManagementSystem.exe" "%INSTALL_DIR%\\" >nul
    echo ✅ تم نسخ التطبيق الرئيسي
) else (
    echo ❌ ملف التطبيق غير موجود
    pause
    exit /b 1
)

REM نسخ الملفات المساعدة
if exist "README_FIRST.txt" copy "README_FIRST.txt" "%INSTALL_DIR%\\" >nul
if exist "USER_GUIDE.md" copy "USER_GUIDE.md" "%INSTALL_DIR%\\" >nul

REM إنشاء المجلدات المطلوبة
mkdir "%INSTALL_DIR%\\data" >nul 2>&1
mkdir "%INSTALL_DIR%\\logs" >nul 2>&1
mkdir "%INSTALL_DIR%\\backups" >nul 2>&1
mkdir "%INSTALL_DIR%\\exports" >nul 2>&1

echo ✅ تم إنشاء المجلدات المطلوبة

echo.
echo 🔗 إنشاء اختصار على سطح المكتب...

REM إنشاء اختصار
set "SHORTCUT_PATH=%USERPROFILE%\\Desktop\\نظام إدارة الطلاب.lnk"
powershell "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%SHORTCUT_PATH%'); $Shortcut.TargetPath = '%INSTALL_DIR%\\StudentManagementSystem.exe'; $Shortcut.WorkingDirectory = '%INSTALL_DIR%'; $Shortcut.Description = 'نظام إدارة الطلاب لمستر أحمد عادل'; $Shortcut.Save()" >nul 2>&1

if exist "%SHORTCUT_PATH%" (
    echo ✅ تم إنشاء اختصار على سطح المكتب
) else (
    echo ⚠️ لم يتم إنشاء الاختصار
)

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                      تم التثبيت بنجاح!                      ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.
echo 📍 مكان التثبيت: %INSTALL_DIR%
echo 🖥️ اختصار سطح المكتب: نظام إدارة الطلاب
echo.
echo 🔐 بيانات تسجيل الدخول:
echo    اسم المستخدم: admin
echo    كلمة المرور: admin123
echo.
echo 📖 للمساعدة: راجع ملف USER_GUIDE.md
echo.

set /p choice="هل تريد تشغيل التطبيق الآن؟ (y/n): "
if /i "%choice%"=="y" (
    start "" "%INSTALL_DIR%\\StudentManagementSystem.exe"
) else if /i "%choice%"=="yes" (
    start "" "%INSTALL_DIR%\\StudentManagementSystem.exe"
) else if /i "%choice%"=="ن" (
    start "" "%INSTALL_DIR%\\StudentManagementSystem.exe"
) else if /i "%choice%"=="نعم" (
    start "" "%INSTALL_DIR%\\StudentManagementSystem.exe"
)

echo.
echo شكراً لاستخدام نظام إدارة الطلاب! 🎓
pause
'''
    
    with open('dist/installer.bat', 'w', encoding='utf-8') as f:
        f.write(installer_content)
    
    print("✅ تم إنشاء ملف التثبيت")

def create_readme():
    """إنشاء ملف README للتوزيع"""
    readme_content = '''# نظام إدارة الطلاب - مستر أحمد عادل

## 🚀 التشغيل السريع

### الطريقة الأولى (مُوصى بها):
1. شغل installer.bat لتثبيت التطبيق
2. انقر على اختصار "نظام إدارة الطلاب" على سطح المكتب

### الطريقة الثانية:
1. انقر مرتين على StudentManagementSystem.exe
2. استخدم admin/admin123 لتسجيل الدخول

## 🔐 بيانات تسجيل الدخول
- اسم المستخدم: admin
- كلمة المرور: admin123

## 🎓 المميزات
- إدارة شاملة للطلاب
- تسجيل الحضور السريع
- إدارة درجات الجغرافيا والتاريخ
- تقارير احترافية
- واجهة عربية عصرية

## 📞 الدعم
في حالة وجود مشاكل، راجع ملف USER_GUIDE.md

---
تم التطوير خصيصاً لمستر أحمد عادل
معلم الجغرافيا والتاريخ
'''
    
    with open('dist/README_FIRST.txt', 'w', encoding='utf-8') as f:
        f.write(readme_content)
    
    print("✅ تم إنشاء ملف README_FIRST.txt")

def main():
    """الدالة الرئيسية"""
    print("🏗️ بناء تطبيق مستقل - نظام إدارة الطلاب")
    print("=" * 60)
    
    # التحقق من PyInstaller
    if not check_pyinstaller():
        input("اضغط Enter للخروج...")
        return False
    
    # إنشاء ملف spec
    create_spec_file()
    
    # بناء ملف EXE
    if not build_exe():
        input("اضغط Enter للخروج...")
        return False
    
    # التحقق من إنشاء الملف
    exe_path = Path('dist/StudentManagementSystem.exe')
    if exe_path.exists():
        # إنشاء ملفات التوزيع
        create_installer()
        create_readme()
        
        # نسخ الملفات المهمة
        import shutil
        files_to_copy = ['USER_GUIDE.md', 'INSTALLATION.md']
        for file_name in files_to_copy:
            if Path(file_name).exists():
                shutil.copy2(file_name, 'dist/')
        
        file_size = exe_path.stat().st_size / (1024 * 1024)
        
        print("\n" + "=" * 60)
        print("🎉 تم بناء التطبيق المستقل بنجاح!")
        print("=" * 60)
        print(f"📦 ملف EXE: {exe_path}")
        print(f"📏 حجم الملف: {file_size:.1f} ميجابايت")
        print(f"📁 مجلد التوزيع: dist/")
        print()
        print("📋 محتويات مجلد التوزيع:")
        print("  - StudentManagementSystem.exe (التطبيق الرئيسي)")
        print("  - installer.bat (ملف التثبيت)")
        print("  - README_FIRST.txt (تعليمات التشغيل)")
        print("  - USER_GUIDE.md (دليل المستخدم)")
        print()
        print("🚀 طريقة الاستخدام:")
        print("1. انسخ مجلد dist بالكامل")
        print("2. شغل installer.bat لتثبيت التطبيق")
        print("3. أو شغل StudentManagementSystem.exe مباشرة")
        print("4. استخدم admin/admin123 لتسجيل الدخول")
        print()
        print("🎓 التطبيق جاهز للتوزيع والاستخدام!")
        
        # فتح مجلد التوزيع
        try:
            import subprocess
            subprocess.run(['explorer', 'dist'], check=False)
        except:
            pass
            
        return True
    else:
        print("❌ فشل في إنشاء ملف EXE")
        return False

if __name__ == "__main__":
    success = main()
    if not success:
        print("\n❌ فشل في بناء التطبيق")
    
    input("\nاضغط Enter للخروج...")
