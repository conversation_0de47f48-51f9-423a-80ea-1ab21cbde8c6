@echo off
chcp 65001 >nul
title نظام إدارة الطلاب - مستر أحمد عادل

cls
color 0A
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    نظام إدارة الطلاب                        ║
echo ║                   مستر أحمد عادل                           ║
echo ║              معلم الجغرافيا والتاريخ                        ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 🚀 تشغيل النظام الكامل...
echo.

REM التحقق من Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير موجود
    goto error
)

echo ✅ Python موجود
python --version

echo.
echo 📦 التحقق من المكتبات وتثبيتها...

REM تثبيت جميع المكتبات المطلوبة
python -m pip install --quiet PyQt5 qrcode[pil] pyzbar cryptography opencv-python Pillow

echo ✅ تم تثبيت جميع المكتبات

echo.
echo 🎯 تشغيل النظام...
echo.

REM تشغيل النظام
python main.py

if not errorlevel 1 goto success

echo ❌ فشل في تشغيل النظام من المجلد الحالي
echo 🔄 محاولة من مجلد SMS_Package...

if exist "SMS_Package\main.py" (
    cd SMS_Package
    python main.py
    if not errorlevel 1 goto success
)

goto error

:success
echo.
echo ✅ تم تشغيل النظام بنجاح!
goto end

:error
echo.
echo ❌ فشل في تشغيل النظام
echo.
echo 🔧 تأكد من:
echo 1. وجود ملفات النظام
echo 2. تثبيت Python بشكل صحيح
echo 3. الاتصال بالإنترنت لتثبيت المكتبات
echo.

:end
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                      معلومات النظام                         ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.
echo 🔐 بيانات تسجيل الدخول:
echo    👤 اسم المستخدم: admin
echo    🔑 كلمة المرور: admin123
echo.
echo 🏠 المميزات المتاحة:
echo    📊 الصفحة الرئيسية مع الإحصائيات
echo    👥 إدارة الطلاب الكاملة
echo    📋 تسجيل الحضور مع الرسائل
echo    📊 إدارة الدرجات مع الإشعارات
echo    📈 التقارير PDF و Excel
echo    📱 نظام QR Code للحضور الذكي
echo    ⚙️ الإعدادات والتخصيص
echo.
echo 🎓 تم التطوير بواسطة: م/ حسام أسامة
echo    مهندس برمجيات - مطور تطبيقات
echo.
pause
