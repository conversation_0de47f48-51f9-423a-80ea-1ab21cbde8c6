#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تطبيق إدارة الطلاب لمستر أحمد عادل
Student Management System for Mr. <PERSON>

المطور: مساعد الذكي
التاريخ: 2025
الإصدار: 1.0
"""

import sys
import os
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont

# إضافة مسار المجلدات للاستيراد
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.ui.login_window import LoginWindow
from src.database.database_manager import DatabaseManager

def main():
    """الدالة الرئيسية لتشغيل التطبيق"""
    try:
        app = QApplication(sys.argv)

        # إعداد خصائص التطبيق
        app.setApplicationName("نظام إدارة الطلاب")
        app.setApplicationVersion("1.0")
        app.setOrganizationName("مستر أحمد عادل")

        # إعداد الخط العربي
        font = QFont("Segoe UI", 10)
        app.setFont(font)

        # إعداد اتجاه النص للعربية
        app.setLayoutDirection(Qt.RightToLeft)

        # تهيئة قاعدة البيانات
        print("📊 تهيئة قاعدة البيانات...")
        db_manager = DatabaseManager()
        db_manager.initialize_database()

        # إنشاء وعرض نافذة تسجيل الدخول
        print("🔐 فتح نافذة تسجيل الدخول...")
        login_window = LoginWindow()
        login_window.show()

        print("✅ تم تشغيل التطبيق بنجاح!")

        # تشغيل التطبيق
        sys.exit(app.exec_())

    except Exception as e:
        print(f"❌ خطأ في تشغيل التطبيق: {e}")
        import traceback
        traceback.print_exc()
        input("اضغط Enter للخروج...")

if __name__ == "__main__":
    main()
