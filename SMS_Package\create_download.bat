@echo off
title Creating Student Management System Download Package

echo.
echo ========================================
echo   Creating Download Package
echo   Student Management System
echo   For Mr. <PERSON> Adel
echo ========================================
echo.

REM Create timestamp for filename
for /f "tokens=2 delims==" %%a in ('wmic OS Get localdatetime /value') do set "dt=%%a"
set "YYYY=%dt:~0,4%" & set "MM=%dt:~4,2%" & set "DD=%dt:~6,2%"
set "HH=%dt:~8,2%" & set "Min=%dt:~10,2%"
set "ZIP_NAME=StudentManagementSystem_%YYYY%%MM%%DD%_%HH%%Min%.zip"

echo Creating: %ZIP_NAME%
echo.

REM Create temporary directory
set "TEMP_DIR=SMS_Package"
if exist "%TEMP_DIR%" rmdir /s /q "%TEMP_DIR%"
mkdir "%TEMP_DIR%"

echo Copying files to package directory...
echo.

REM Copy all Python files
echo Python files:
for %%f in (*.py) do (
    copy "%%f" "%TEMP_DIR%\" >nul 2>&1
    if exist "%TEMP_DIR%\%%f" (
        echo   [OK] %%f
    ) else (
        echo   [SKIP] %%f
    )
)

echo.
echo Batch files:
REM Copy all batch files
for %%f in (*.bat) do (
    copy "%%f" "%TEMP_DIR%\" >nul 2>&1
    if exist "%TEMP_DIR%\%%f" (
        echo   [OK] %%f
    ) else (
        echo   [SKIP] %%f
    )
)

echo.
echo Documentation files:
REM Copy all markdown files
for %%f in (*.md) do (
    copy "%%f" "%TEMP_DIR%\" >nul 2>&1
    if exist "%TEMP_DIR%\%%f" (
        echo   [OK] %%f
    ) else (
        echo   [SKIP] %%f
    )
)

echo.
echo Other files:
REM Copy requirements.txt
if exist "requirements.txt" (
    copy "requirements.txt" "%TEMP_DIR%\" >nul 2>&1
    echo   [OK] requirements.txt
)

echo.
echo Directories:
REM Copy directories
if exist "src" (
    xcopy "src" "%TEMP_DIR%\src\" /E /I /Q >nul 2>&1
    echo   [OK] src/
)

if exist "data" (
    xcopy "data" "%TEMP_DIR%\data\" /E /I /Q >nul 2>&1
    echo   [OK] data/
)

if exist "assets" (
    xcopy "assets" "%TEMP_DIR%\assets\" /E /I /Q >nul 2>&1
    echo   [OK] assets/
)

echo.
echo Creating empty directories:
REM Create empty directories
mkdir "%TEMP_DIR%\logs" >nul 2>&1
echo. > "%TEMP_DIR%\logs\.gitkeep"
echo   [OK] logs/

mkdir "%TEMP_DIR%\backups" >nul 2>&1
echo. > "%TEMP_DIR%\backups\.gitkeep"
echo   [OK] backups/

mkdir "%TEMP_DIR%\exports" >nul 2>&1
echo. > "%TEMP_DIR%\exports\.gitkeep"
echo   [OK] exports/

mkdir "%TEMP_DIR%\temp" >nul 2>&1
echo. > "%TEMP_DIR%\temp\.gitkeep"
echo   [OK] temp/

echo.
echo Creating project information file...

REM Create project info file
(
echo # Student Management System - نظام إدارة الطلاب
echo ## For Mr. Ahmed Adel - لمستر أحمد عادل
echo.
echo **Created:** %date% %time%
echo **Version:** 1.0.0
echo.
echo ## Quick Start:
echo 1. Double-click run_app.bat
echo 2. Choose option 1
echo 3. Login: admin / admin123
echo.
echo ## Important Files:
echo - run_app.bat: Main launcher
echo - USER_GUIDE.md: Complete user guide
echo - main.py: Main application file
echo.
echo ## Features:
echo - Student Management
echo - Attendance Tracking
echo - Grade Management
echo - Report Generation
echo - Arabic Interface
echo.
echo ## Developer:
echo Created specifically for Mr. Ahmed Adel
echo Geography and History Teacher
echo.
echo Enjoy using the Student Management System!
) > "%TEMP_DIR%\README_FIRST.txt"

echo   [OK] README_FIRST.txt

echo.
echo Compressing package...

REM Try to create ZIP using PowerShell
powershell -Command "try { Compress-Archive -Path '%TEMP_DIR%\*' -DestinationPath '%ZIP_NAME%' -Force; Write-Host 'ZIP created successfully' } catch { Write-Host 'PowerShell compression failed' }" 2>nul

REM Check if ZIP was created
if exist "%ZIP_NAME%" (
    echo.
    echo ========================================
    echo           SUCCESS!
    echo ========================================
    echo.
    echo ZIP file created: %ZIP_NAME%
    
    REM Get file size
    for %%A in ("%ZIP_NAME%") do set "file_size=%%~zA"
    set /a "file_size_mb=%file_size% / 1048576"
    echo File size: %file_size_mb% MB
    echo Location: %CD%
    echo.
    echo The Student Management System is ready for download!
    echo.
    echo Instructions:
    echo 1. Copy the ZIP file to target computer
    echo 2. Extract all files
    echo 3. Double-click run_app.bat
    echo 4. Login with admin/admin123
    echo.
    
    REM Open file location
    explorer /select,"%ZIP_NAME%"
    
) else (
    echo.
    echo ========================================
    echo      Manual Compression Required
    echo ========================================
    echo.
    echo PowerShell compression failed.
    echo Please manually compress the folder: %TEMP_DIR%
    echo.
    echo Steps:
    echo 1. Right-click on %TEMP_DIR% folder
    echo 2. Select "Send to" ^> "Compressed folder"
    echo 3. Rename to: %ZIP_NAME%
    echo.
    
    REM Open the temp directory
    explorer "%TEMP_DIR%"
)

echo.
echo Package directory: %TEMP_DIR%
echo.
pause
