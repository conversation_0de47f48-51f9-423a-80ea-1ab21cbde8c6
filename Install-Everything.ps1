# PowerShell script to download and install everything needed for Student Management System

Write-Host "=" * 60 -ForegroundColor Cyan
Write-Host "Student Management System - Complete Installation" -ForegroundColor Green
Write-Host "Mr. <PERSON> - Geography and History Teacher" -ForegroundColor Yellow
Write-Host "=" * 60 -ForegroundColor Cyan
Write-Host ""

# Check if running as administrator
$isAdmin = ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")

if ($isAdmin) {
    Write-Host "✓ Running as Administrator" -ForegroundColor Green
} else {
    Write-Host "⚠ Not running as Administrator - some features may not work" -ForegroundColor Yellow
}

Write-Host ""

# Function to download file
function Download-File {
    param($url, $output)
    try {
        Write-Host "Downloading $output..." -ForegroundColor Cyan
        Invoke-WebRequest -Uri $url -OutFile $output -UseBasicParsing
        Write-Host "✓ Download completed: $output" -ForegroundColor Green
        return $true
    } catch {
        Write-Host "✗ Download failed: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# Step 1: Check Python
Write-Host "Step 1: Checking Python installation..." -ForegroundColor Magenta
Write-Host "-" * 40

try {
    $pythonVersion = python --version 2>&1
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✓ Python found: $pythonVersion" -ForegroundColor Green
    } else {
        throw "Python not found"
    }
} catch {
    Write-Host "✗ Python not found. Installing..." -ForegroundColor Yellow
    
    # Download Python
    $pythonUrl = "https://www.python.org/ftp/python/3.11.0/python-3.11.0-amd64.exe"
    $pythonInstaller = "python-installer.exe"
    
    if (Download-File $pythonUrl $pythonInstaller) {
        Write-Host "Installing Python..." -ForegroundColor Cyan
        Write-Host "Please wait, this may take a few minutes..." -ForegroundColor Yellow
        
        # Install Python silently
        Start-Process -FilePath $pythonInstaller -ArgumentList "/quiet", "InstallAllUsers=1", "PrependPath=1", "Include_test=0" -Wait
        
        # Clean up
        Remove-Item $pythonInstaller -ErrorAction SilentlyContinue
        
        # Refresh environment variables
        $env:Path = [System.Environment]::GetEnvironmentVariable("Path","Machine") + ";" + [System.Environment]::GetEnvironmentVariable("Path","User")
        
        Write-Host "✓ Python installation completed!" -ForegroundColor Green
        
        # Test Python again
        try {
            $pythonVersion = python --version 2>&1
            Write-Host "✓ Python verified: $pythonVersion" -ForegroundColor Green
        } catch {
            Write-Host "✗ Python installation may have failed. Please restart and try again." -ForegroundColor Red
            exit 1
        }
    } else {
        Write-Host "✗ Failed to download Python installer" -ForegroundColor Red
        Write-Host "Please manually install Python from: https://python.org" -ForegroundColor Yellow
        exit 1
    }
}

Write-Host ""

# Step 2: Install packages
Write-Host "Step 2: Installing required packages..." -ForegroundColor Magenta
Write-Host "-" * 40

$packages = @("PyQt5", "reportlab", "openpyxl", "Pillow")

foreach ($package in $packages) {
    Write-Host "Installing $package..." -ForegroundColor Cyan
    try {
        & python -m pip install $package --quiet --upgrade
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✓ $package installed successfully" -ForegroundColor Green
        } else {
            Write-Host "⚠ $package installation had issues, trying user install..." -ForegroundColor Yellow
            & python -m pip install $package --user --quiet
        }
    } catch {
        Write-Host "✗ Failed to install $package" -ForegroundColor Red
    }
}

Write-Host ""

# Step 3: Prepare application
Write-Host "Step 3: Preparing application..." -ForegroundColor Magenta
Write-Host "-" * 40

# Create directories
$directories = @("data", "logs", "backups", "exports", "assets")
foreach ($dir in $directories) {
    if (!(Test-Path $dir)) {
        New-Item -ItemType Directory -Path $dir -Force | Out-Null
        Write-Host "✓ Created directory: $dir" -ForegroundColor Green
    } else {
        Write-Host "✓ Directory exists: $dir" -ForegroundColor Green
    }
}

# Create launcher
$launcherContent = @"
@echo off
title Student Management System - Mr. Ahmed Adel
echo.
echo ========================================
echo   Student Management System
echo   Mr. Ahmed Adel
echo ========================================
echo.
echo Starting application...
echo.
python main.py
echo.
echo Login credentials:
echo Username: admin
echo Password: admin123
echo.
pause
"@

$launcherContent | Out-File -FilePath "LAUNCH_APP.bat" -Encoding ASCII
Write-Host "✓ Created launcher: LAUNCH_APP.bat" -ForegroundColor Green

Write-Host ""

# Step 4: Test installation
Write-Host "Step 4: Testing installation..." -ForegroundColor Magenta
Write-Host "-" * 40

# Test Python
try {
    $pythonTest = python --version 2>&1
    Write-Host "✓ Python test: $pythonTest" -ForegroundColor Green
} catch {
    Write-Host "✗ Python test failed" -ForegroundColor Red
}

# Test PyQt5
try {
    $pyqtTest = python -c "import PyQt5.QtWidgets; print('PyQt5 OK')" 2>&1
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✓ PyQt5 test: OK" -ForegroundColor Green
    } else {
        Write-Host "✗ PyQt5 test failed" -ForegroundColor Red
    }
} catch {
    Write-Host "✗ PyQt5 test failed" -ForegroundColor Red
}

# Test application files
if (Test-Path "main.py") {
    Write-Host "✓ main.py found" -ForegroundColor Green
} else {
    Write-Host "✗ main.py not found" -ForegroundColor Red
}

if (Test-Path "src") {
    Write-Host "✓ src directory found" -ForegroundColor Green
} else {
    Write-Host "✗ src directory not found" -ForegroundColor Red
}

Write-Host ""

# Final message
Write-Host "=" * 60 -ForegroundColor Cyan
Write-Host "INSTALLATION COMPLETE!" -ForegroundColor Green -BackgroundColor Black
Write-Host "=" * 60 -ForegroundColor Cyan
Write-Host ""

Write-Host "The Student Management System is ready to use!" -ForegroundColor Green
Write-Host ""
Write-Host "Login credentials:" -ForegroundColor Yellow
Write-Host "Username: admin" -ForegroundColor White
Write-Host "Password: admin123" -ForegroundColor White
Write-Host ""

Write-Host "To start the application:" -ForegroundColor Yellow
Write-Host "1. Double-click: LAUNCH_APP.bat" -ForegroundColor White
Write-Host "2. Or run: python main.py" -ForegroundColor White
Write-Host ""

$choice = Read-Host "Do you want to start the application now? (y/n)"
if ($choice -eq "y" -or $choice -eq "yes" -or $choice -eq "Y") {
    Write-Host ""
    Write-Host "Starting Student Management System..." -ForegroundColor Green
    Write-Host ""
    
    try {
        & python main.py
    } catch {
        Write-Host "Failed to start application. Please try running LAUNCH_APP.bat" -ForegroundColor Red
    }
}

Write-Host ""
Write-Host "For help and documentation, see:" -ForegroundColor Yellow
Write-Host "- USER_GUIDE.md" -ForegroundColor White
Write-Host "- READ_ME_FIRST.txt" -ForegroundColor White
Write-Host ""
Write-Host "Press any key to exit..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
