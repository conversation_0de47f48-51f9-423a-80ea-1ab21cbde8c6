@echo off
title Student Management System - Simple Installation

echo.
echo ========================================
echo   Student Management System
echo   Simple Installation
echo ========================================
echo.

echo Checking Python...
python --version >nul 2>&1
if errorlevel 1 (
    echo Python not found!
    echo.
    echo SOLUTION: Install Python manually
    echo.
    echo 1. Go to: https://python.org/downloads
    echo 2. Download Python 3.8 or newer
    echo 3. During installation, CHECK "Add Python to PATH"
    echo 4. Restart computer after installation
    echo 5. Run this script again
    echo.
    echo Opening Python download page...
    start https://python.org/downloads
    echo.
    pause
    exit /b 1
) else (
    echo Python found!
    python --version
)

echo.
echo Installing packages...
pip install PyQt5 reportlab openpyxl Pillow --quiet --upgrade

echo.
echo Creating directories...
if not exist "data" mkdir data
if not exist "logs" mkdir logs
if not exist "backups" mkdir backups
if not exist "exports" mkdir exports

echo.
echo Creating launcher...
(
echo @echo off
echo title Student Management System
echo echo Starting Student Management System...
echo echo.
echo python main.py
echo echo.
echo echo Login: admin / admin123
echo pause
) > RUN_APP.bat

echo.
echo Testing installation...
python -c "import PyQt5.QtWidgets; print('PyQt5 OK')" 2>nul
if errorlevel 1 (
    echo PyQt5 test failed!
    echo Trying alternative installation...
    pip install PyQt5 --user
)

echo.
echo ========================================
echo   Installation Complete!
echo ========================================
echo.
echo To run the application:
echo 1. Double-click: RUN_APP.bat
echo 2. Or run: python main.py
echo.
echo Login credentials:
echo Username: admin
echo Password: admin123
echo.

set /p choice="Start application now? (y/n): "
if /i "%choice%"=="y" (
    echo.
    echo Starting application...
    python main.py
)

echo.
pause
