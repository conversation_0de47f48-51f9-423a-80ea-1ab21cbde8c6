@echo off
title Compress Student Management System

echo Creating ZIP file for Student Management System...
echo.

REM Get current date and time for filename
for /f "tokens=2 delims==" %%a in ('wmic OS Get localdatetime /value') do set "dt=%%a"
set "YYYY=%dt:~0,4%" & set "MM=%dt:~4,2%" & set "DD=%dt:~6,2%"
set "HH=%dt:~8,2%" & set "Min=%dt:~10,2%"
set "ZIP_NAME=StudentManagementSystem_%YYYY%%MM%%DD%_%HH%%Min%.zip"

echo ZIP filename: %ZIP_NAME%
echo.

REM Create temporary directory
set "TEMP_DIR=temp_zip"
if exist "%TEMP_DIR%" rmdir /s /q "%TEMP_DIR%"
mkdir "%TEMP_DIR%"

echo Copying files...

REM Copy main files
copy main.py "%TEMP_DIR%\" 2>nul
copy run.py "%TEMP_DIR%\" 2>nul
copy launch.py "%TEMP_DIR%\" 2>nul
copy config.py "%TEMP_DIR%\" 2>nul
copy requirements.txt "%TEMP_DIR%\" 2>nul
copy run_app.bat "%TEMP_DIR%\" 2>nul
copy start.bat "%TEMP_DIR%\" 2>nul
copy build.bat "%TEMP_DIR%\" 2>nul
copy fix_imports.py "%TEMP_DIR%\" 2>nul
copy test_app.py "%TEMP_DIR%\" 2>nul
copy build_exe.py "%TEMP_DIR%\" 2>nul
copy compress_project.py "%TEMP_DIR%\" 2>nul
copy README.md "%TEMP_DIR%\" 2>nul
copy README_FINAL.md "%TEMP_DIR%\" 2>nul
copy USER_GUIDE.md "%TEMP_DIR%\" 2>nul
copy INSTALLATION.md "%TEMP_DIR%\" 2>nul
copy PROJECT_SUMMARY.md "%TEMP_DIR%\" 2>nul

REM Copy directories
if exist "src" xcopy "src" "%TEMP_DIR%\src\" /E /I /Q 2>nul
if exist "data" xcopy "data" "%TEMP_DIR%\data\" /E /I /Q 2>nul
if exist "assets" xcopy "assets" "%TEMP_DIR%\assets\" /E /I /Q 2>nul

REM Create empty directories
mkdir "%TEMP_DIR%\logs" 2>nul
mkdir "%TEMP_DIR%\backups" 2>nul
mkdir "%TEMP_DIR%\exports" 2>nul
mkdir "%TEMP_DIR%\temp" 2>nul

echo. > "%TEMP_DIR%\logs\.gitkeep"
echo. > "%TEMP_DIR%\backups\.gitkeep"
echo. > "%TEMP_DIR%\exports\.gitkeep"
echo. > "%TEMP_DIR%\temp\.gitkeep"

REM Create project info file
echo # Student Management System > "%TEMP_DIR%\PROJECT_INFO.txt"
echo. >> "%TEMP_DIR%\PROJECT_INFO.txt"
echo Created: %date% %time% >> "%TEMP_DIR%\PROJECT_INFO.txt"
echo Version: 1.0.0 >> "%TEMP_DIR%\PROJECT_INFO.txt"
echo. >> "%TEMP_DIR%\PROJECT_INFO.txt"
echo To run: Double-click run_app.bat >> "%TEMP_DIR%\PROJECT_INFO.txt"
echo Login: admin / admin123 >> "%TEMP_DIR%\PROJECT_INFO.txt"

echo Compressing files...
powershell -Command "Compress-Archive -Path '%TEMP_DIR%\*' -DestinationPath '%ZIP_NAME%' -Force"

REM Clean up
rmdir /s /q "%TEMP_DIR%"

if exist "%ZIP_NAME%" (
    echo.
    echo SUCCESS: Project compressed successfully!
    echo File: %ZIP_NAME%
    echo Location: %CD%
    echo.
    echo The ZIP file contains the complete Student Management System.
    echo Extract and run run_app.bat to start the application.
    echo.
    explorer /select,"%ZIP_NAME%"
) else (
    echo.
    echo ERROR: Failed to create ZIP file.
    echo Please try manual compression.
)

pause
