# -*- coding: utf-8 -*-
"""
نافذة الإعدادات
Settings Window
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                            QLineEdit, QPushButton, QComboBox, QMessageBox, 
                            QFrame, QGroupBox, QFormLayout, QTabWidget,
                            QCheckBox, QSpinBox, QTextEdit, QFileDialog)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont

from ..database.database_manager import DatabaseManager
from ..utils.auth import AuthManager

class SettingsWindow(QWidget):
    """نافذة الإعدادات"""
    
    def __init__(self, db_manager: DatabaseManager, auth_manager: AuthManager):
        super().__init__()
        self.db_manager = db_manager
        self.auth_manager = auth_manager
        
        self.init_ui()
        self.setup_styles()
        self.load_settings()
    
    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        self.setWindowTitle("إعدادات التطبيق")
        self.setGeometry(100, 100, 800, 600)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout()
        
        # التبويبات
        tabs = QTabWidget()
        
        # تبويب الإعدادات العامة
        general_tab = self.create_general_tab()
        tabs.addTab(general_tab, "الإعدادات العامة")
        
        # تبويب إعدادات المعلم
        teacher_tab = self.create_teacher_tab()
        tabs.addTab(teacher_tab, "إعدادات المعلم")
        
        # تبويب إعدادات قاعدة البيانات
        database_tab = self.create_database_tab()
        tabs.addTab(database_tab, "قاعدة البيانات")
        
        # تبويب إعدادات الأمان
        security_tab = self.create_security_tab()
        tabs.addTab(security_tab, "الأمان")
        
        main_layout.addWidget(tabs)
        
        # أزرار الحفظ والإلغاء
        buttons_layout = QHBoxLayout()
        
        self.save_button = QPushButton("حفظ الإعدادات")
        self.save_button.clicked.connect(self.save_settings)
        
        self.cancel_button = QPushButton("إلغاء")
        self.cancel_button.clicked.connect(self.close)
        
        self.reset_button = QPushButton("استعادة الافتراضي")
        self.reset_button.clicked.connect(self.reset_to_defaults)
        
        buttons_layout.addWidget(self.save_button)
        buttons_layout.addWidget(self.cancel_button)
        buttons_layout.addStretch()
        buttons_layout.addWidget(self.reset_button)
        
        main_layout.addLayout(buttons_layout)
        
        self.setLayout(main_layout)
    
    def create_general_tab(self):
        """إنشاء تبويب الإعدادات العامة"""
        widget = QWidget()
        layout = QVBoxLayout()
        
        # إعدادات المدرسة
        school_group = QGroupBox("معلومات المدرسة")
        school_layout = QFormLayout()
        
        self.school_name_input = QLineEdit()
        self.school_name_input.setPlaceholderText("أدخل اسم المدرسة")
        school_layout.addRow("اسم المدرسة:", self.school_name_input)
        
        self.current_year_input = QLineEdit()
        self.current_year_input.setPlaceholderText("مثال: 2024-2025")
        school_layout.addRow("العام الدراسي:", self.current_year_input)
        
        school_group.setLayout(school_layout)
        layout.addWidget(school_group)
        
        # إعدادات التطبيق
        app_group = QGroupBox("إعدادات التطبيق")
        app_layout = QFormLayout()
        
        self.language_combo = QComboBox()
        self.language_combo.addItems(["العربية", "English"])
        app_layout.addRow("اللغة:", self.language_combo)
        
        self.theme_combo = QComboBox()
        self.theme_combo.addItems(["فاتح", "داكن", "تلقائي"])
        app_layout.addRow("المظهر:", self.theme_combo)
        
        self.auto_backup_checkbox = QCheckBox("النسخ الاحتياطي التلقائي")
        app_layout.addRow("", self.auto_backup_checkbox)
        
        self.backup_interval_spinbox = QSpinBox()
        self.backup_interval_spinbox.setRange(1, 30)
        self.backup_interval_spinbox.setValue(7)
        self.backup_interval_spinbox.setSuffix(" أيام")
        app_layout.addRow("فترة النسخ الاحتياطي:", self.backup_interval_spinbox)
        
        app_group.setLayout(app_layout)
        layout.addWidget(app_group)
        
        layout.addStretch()
        widget.setLayout(layout)
        return widget
    
    def create_teacher_tab(self):
        """إنشاء تبويب إعدادات المعلم"""
        widget = QWidget()
        layout = QVBoxLayout()
        
        # معلومات المعلم
        teacher_group = QGroupBox("معلومات المعلم")
        teacher_layout = QFormLayout()
        
        self.teacher_name_input = QLineEdit()
        self.teacher_name_input.setPlaceholderText("أدخل اسم المعلم")
        teacher_layout.addRow("اسم المعلم:", self.teacher_name_input)
        
        self.teacher_email_input = QLineEdit()
        self.teacher_email_input.setPlaceholderText("البريد الإلكتروني (اختياري)")
        teacher_layout.addRow("البريد الإلكتروني:", self.teacher_email_input)
        
        self.teacher_phone_input = QLineEdit()
        self.teacher_phone_input.setPlaceholderText("رقم الهاتف (اختياري)")
        teacher_layout.addRow("رقم الهاتف:", self.teacher_phone_input)
        
        teacher_group.setLayout(teacher_layout)
        layout.addWidget(teacher_group)
        
        # إعدادات المواد
        subjects_group = QGroupBox("إعدادات المواد")
        subjects_layout = QFormLayout()
        
        self.geography_enabled_checkbox = QCheckBox("تفعيل مادة الجغرافيا")
        self.geography_enabled_checkbox.setChecked(True)
        subjects_layout.addRow("", self.geography_enabled_checkbox)
        
        self.history_enabled_checkbox = QCheckBox("تفعيل مادة التاريخ")
        self.history_enabled_checkbox.setChecked(True)
        subjects_layout.addRow("", self.history_enabled_checkbox)
        
        self.passing_grade_spinbox = QSpinBox()
        self.passing_grade_spinbox.setRange(30, 70)
        self.passing_grade_spinbox.setValue(50)
        self.passing_grade_spinbox.setSuffix("%")
        subjects_layout.addRow("درجة النجاح:", self.passing_grade_spinbox)
        
        subjects_group.setLayout(subjects_layout)
        layout.addWidget(subjects_group)
        
        layout.addStretch()
        widget.setLayout(layout)
        return widget
    
    def create_database_tab(self):
        """إنشاء تبويب إعدادات قاعدة البيانات"""
        widget = QWidget()
        layout = QVBoxLayout()
        
        # معلومات قاعدة البيانات
        db_info_group = QGroupBox("معلومات قاعدة البيانات")
        db_info_layout = QFormLayout()
        
        self.db_path_label = QLabel("جاري التحميل...")
        db_info_layout.addRow("مسار قاعدة البيانات:", self.db_path_label)
        
        self.db_size_label = QLabel("جاري الحساب...")
        db_info_layout.addRow("حجم قاعدة البيانات:", self.db_size_label)
        
        self.total_students_label = QLabel("0")
        db_info_layout.addRow("إجمالي الطلاب:", self.total_students_label)
        
        self.total_records_label = QLabel("0")
        db_info_layout.addRow("إجمالي السجلات:", self.total_records_label)
        
        db_info_group.setLayout(db_info_layout)
        layout.addWidget(db_info_group)
        
        # عمليات قاعدة البيانات
        db_operations_group = QGroupBox("عمليات قاعدة البيانات")
        db_operations_layout = QVBoxLayout()
        
        # النسخ الاحتياطي
        backup_layout = QHBoxLayout()
        self.backup_button = QPushButton("إنشاء نسخة احتياطية")
        self.backup_button.clicked.connect(self.create_backup)
        
        self.restore_button = QPushButton("استعادة من نسخة احتياطية")
        self.restore_button.clicked.connect(self.restore_backup)
        
        backup_layout.addWidget(self.backup_button)
        backup_layout.addWidget(self.restore_button)
        db_operations_layout.addLayout(backup_layout)
        
        # تنظيف قاعدة البيانات
        cleanup_layout = QHBoxLayout()
        self.optimize_button = QPushButton("تحسين قاعدة البيانات")
        self.optimize_button.clicked.connect(self.optimize_database)
        
        self.vacuum_button = QPushButton("ضغط قاعدة البيانات")
        self.vacuum_button.clicked.connect(self.vacuum_database)
        
        cleanup_layout.addWidget(self.optimize_button)
        cleanup_layout.addWidget(self.vacuum_button)
        db_operations_layout.addLayout(cleanup_layout)
        
        db_operations_group.setLayout(db_operations_layout)
        layout.addWidget(db_operations_group)
        
        layout.addStretch()
        widget.setLayout(layout)
        return widget
    
    def create_security_tab(self):
        """إنشاء تبويب إعدادات الأمان"""
        widget = QWidget()
        layout = QVBoxLayout()
        
        # تغيير كلمة المرور
        password_group = QGroupBox("تغيير كلمة المرور")
        password_layout = QFormLayout()
        
        self.current_password_input = QLineEdit()
        self.current_password_input.setEchoMode(QLineEdit.Password)
        password_layout.addRow("كلمة المرور الحالية:", self.current_password_input)
        
        self.new_password_input = QLineEdit()
        self.new_password_input.setEchoMode(QLineEdit.Password)
        password_layout.addRow("كلمة المرور الجديدة:", self.new_password_input)
        
        self.confirm_password_input = QLineEdit()
        self.confirm_password_input.setEchoMode(QLineEdit.Password)
        password_layout.addRow("تأكيد كلمة المرور:", self.confirm_password_input)
        
        self.change_password_button = QPushButton("تغيير كلمة المرور")
        self.change_password_button.clicked.connect(self.change_password)
        password_layout.addRow("", self.change_password_button)
        
        password_group.setLayout(password_layout)
        layout.addWidget(password_group)
        
        # إعدادات الأمان
        security_group = QGroupBox("إعدادات الأمان")
        security_layout = QFormLayout()
        
        self.auto_logout_checkbox = QCheckBox("تسجيل خروج تلقائي")
        security_layout.addRow("", self.auto_logout_checkbox)
        
        self.logout_timeout_spinbox = QSpinBox()
        self.logout_timeout_spinbox.setRange(5, 120)
        self.logout_timeout_spinbox.setValue(30)
        self.logout_timeout_spinbox.setSuffix(" دقيقة")
        security_layout.addRow("مهلة تسجيل الخروج:", self.logout_timeout_spinbox)
        
        self.remember_login_checkbox = QCheckBox("تذكر تسجيل الدخول")
        security_layout.addRow("", self.remember_login_checkbox)
        
        security_group.setLayout(security_layout)
        layout.addWidget(security_group)
        
        layout.addStretch()
        widget.setLayout(layout)
        return widget

    def load_settings(self):
        """تحميل الإعدادات من قاعدة البيانات"""
        try:
            # تحميل الإعدادات العامة
            school_name = self.db_manager.get_setting('school_name') or "مدرسة النجاح"
            self.school_name_input.setText(school_name)

            current_year = self.db_manager.get_setting('current_year') or "2024-2025"
            self.current_year_input.setText(current_year)

            # تحميل إعدادات المعلم
            teacher_name = self.db_manager.get_setting('teacher_name') or "مستر أحمد عادل"
            self.teacher_name_input.setText(teacher_name)

            teacher_email = self.db_manager.get_setting('teacher_email') or ""
            self.teacher_email_input.setText(teacher_email)

            teacher_phone = self.db_manager.get_setting('teacher_phone') or ""
            self.teacher_phone_input.setText(teacher_phone)

            # تحميل معلومات قاعدة البيانات
            self.load_database_info()

        except Exception as e:
            QMessageBox.warning(self, "تحذير", f"فشل في تحميل بعض الإعدادات: {str(e)}")

    def load_database_info(self):
        """تحميل معلومات قاعدة البيانات"""
        try:
            import os
            from ..models.student import Student

            # مسار قاعدة البيانات
            self.db_path_label.setText(self.db_manager.db_path)

            # حجم قاعدة البيانات
            if os.path.exists(self.db_manager.db_path):
                size_bytes = os.path.getsize(self.db_manager.db_path)
                size_mb = size_bytes / (1024 * 1024)
                self.db_size_label.setText(f"{size_mb:.2f} ميجابايت")
            else:
                self.db_size_label.setText("غير موجود")

            # إحصائيات
            student_model = Student(self.db_manager)
            stats = student_model.get_student_statistics()

            self.total_students_label.setText(str(stats.get('total_students', 0)))

            # حساب إجمالي السجلات
            total_records = self.db_manager.fetch_one("""
                SELECT
                    (SELECT COUNT(*) FROM students) +
                    (SELECT COUNT(*) FROM attendance) +
                    (SELECT COUNT(*) FROM grades) as total
            """)

            self.total_records_label.setText(str(total_records.get('total', 0) if total_records else 0))

        except Exception as e:
            print(f"خطأ في تحميل معلومات قاعدة البيانات: {e}")

    def save_settings(self):
        """حفظ الإعدادات"""
        try:
            # حفظ الإعدادات العامة
            self.db_manager.execute_query("""
                INSERT OR REPLACE INTO settings (setting_key, setting_value, description)
                VALUES (?, ?, ?)
            """, ('school_name', self.school_name_input.text(), 'اسم المدرسة'))

            self.db_manager.execute_query("""
                INSERT OR REPLACE INTO settings (setting_key, setting_value, description)
                VALUES (?, ?, ?)
            """, ('current_year', self.current_year_input.text(), 'العام الدراسي الحالي'))

            # حفظ إعدادات المعلم
            self.db_manager.execute_query("""
                INSERT OR REPLACE INTO settings (setting_key, setting_value, description)
                VALUES (?, ?, ?)
            """, ('teacher_name', self.teacher_name_input.text(), 'اسم المعلم'))

            self.db_manager.execute_query("""
                INSERT OR REPLACE INTO settings (setting_key, setting_value, description)
                VALUES (?, ?, ?)
            """, ('teacher_email', self.teacher_email_input.text(), 'بريد المعلم الإلكتروني'))

            self.db_manager.execute_query("""
                INSERT OR REPLACE INTO settings (setting_key, setting_value, description)
                VALUES (?, ?, ?)
            """, ('teacher_phone', self.teacher_phone_input.text(), 'هاتف المعلم'))

            # حفظ إعدادات المواد
            self.db_manager.execute_query("""
                INSERT OR REPLACE INTO settings (setting_key, setting_value, description)
                VALUES (?, ?, ?)
            """, ('geography_enabled', str(self.geography_enabled_checkbox.isChecked()), 'تفعيل مادة الجغرافيا'))

            self.db_manager.execute_query("""
                INSERT OR REPLACE INTO settings (setting_key, setting_value, description)
                VALUES (?, ?, ?)
            """, ('history_enabled', str(self.history_enabled_checkbox.isChecked()), 'تفعيل مادة التاريخ'))

            self.db_manager.execute_query("""
                INSERT OR REPLACE INTO settings (setting_key, setting_value, description)
                VALUES (?, ?, ?)
            """, ('passing_grade', str(self.passing_grade_spinbox.value()), 'درجة النجاح'))

            QMessageBox.information(self, "تم", "تم حفظ الإعدادات بنجاح")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في حفظ الإعدادات: {str(e)}")

    def reset_to_defaults(self):
        """استعادة الإعدادات الافتراضية"""
        reply = QMessageBox.question(
            self, 'تأكيد',
            'هل تريد استعادة جميع الإعدادات إلى القيم الافتراضية؟',
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            # استعادة القيم الافتراضية
            self.school_name_input.setText("مدرسة النجاح")
            self.current_year_input.setText("2024-2025")
            self.teacher_name_input.setText("مستر أحمد عادل")
            self.teacher_email_input.clear()
            self.teacher_phone_input.clear()
            self.geography_enabled_checkbox.setChecked(True)
            self.history_enabled_checkbox.setChecked(True)
            self.passing_grade_spinbox.setValue(50)
            self.language_combo.setCurrentIndex(0)
            self.theme_combo.setCurrentIndex(0)
            self.auto_backup_checkbox.setChecked(False)
            self.backup_interval_spinbox.setValue(7)

    def change_password(self):
        """تغيير كلمة المرور"""
        current_password = self.current_password_input.text()
        new_password = self.new_password_input.text()
        confirm_password = self.confirm_password_input.text()

        # التحقق من البيانات
        if not current_password:
            QMessageBox.warning(self, "خطأ", "يرجى إدخال كلمة المرور الحالية")
            return

        if not new_password:
            QMessageBox.warning(self, "خطأ", "يرجى إدخال كلمة المرور الجديدة")
            return

        if new_password != confirm_password:
            QMessageBox.warning(self, "خطأ", "كلمة المرور الجديدة وتأكيدها غير متطابقين")
            return

        if len(new_password) < 6:
            QMessageBox.warning(self, "خطأ", "كلمة المرور يجب أن تكون 6 أحرف على الأقل")
            return

        # تغيير كلمة المرور
        if self.auth_manager.change_password(current_password, new_password):
            QMessageBox.information(self, "تم", "تم تغيير كلمة المرور بنجاح")

            # مسح الحقول
            self.current_password_input.clear()
            self.new_password_input.clear()
            self.confirm_password_input.clear()
        else:
            QMessageBox.critical(self, "خطأ", "كلمة المرور الحالية غير صحيحة")

    def create_backup(self):
        """إنشاء نسخة احتياطية"""
        try:
            import shutil
            from datetime import datetime

            # اختيار مكان الحفظ
            backup_name = f"backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.db"
            file_path, _ = QFileDialog.getSaveFileName(
                self, "حفظ النسخة الاحتياطية",
                backup_name,
                "Database Files (*.db)"
            )

            if file_path:
                # نسخ قاعدة البيانات
                shutil.copy2(self.db_manager.db_path, file_path)
                QMessageBox.information(self, "تم", f"تم إنشاء النسخة الاحتياطية في:\n{file_path}")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في إنشاء النسخة الاحتياطية: {str(e)}")

    def restore_backup(self):
        """استعادة من نسخة احتياطية"""
        reply = QMessageBox.warning(
            self, 'تحذير',
            'استعادة النسخة الاحتياطية ستحذف جميع البيانات الحالية!\nهل تريد المتابعة؟',
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            try:
                import shutil

                # اختيار ملف النسخة الاحتياطية
                file_path, _ = QFileDialog.getOpenFileName(
                    self, "اختيار النسخة الاحتياطية",
                    "",
                    "Database Files (*.db)"
                )

                if file_path:
                    # إغلاق الاتصال الحالي
                    self.db_manager.disconnect()

                    # استعادة النسخة الاحتياطية
                    shutil.copy2(file_path, self.db_manager.db_path)

                    # إعادة الاتصال
                    self.db_manager.connect()

                    QMessageBox.information(self, "تم", "تم استعادة النسخة الاحتياطية بنجاح")

                    # تحديث المعلومات
                    self.load_database_info()

            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"فشل في استعادة النسخة الاحتياطية: {str(e)}")

    def optimize_database(self):
        """تحسين قاعدة البيانات"""
        try:
            # تحليل قاعدة البيانات
            self.db_manager.execute_query("ANALYZE")
            QMessageBox.information(self, "تم", "تم تحسين قاعدة البيانات بنجاح")

            # تحديث المعلومات
            self.load_database_info()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحسين قاعدة البيانات: {str(e)}")

    def vacuum_database(self):
        """ضغط قاعدة البيانات"""
        try:
            # ضغط قاعدة البيانات
            self.db_manager.execute_query("VACUUM")
            QMessageBox.information(self, "تم", "تم ضغط قاعدة البيانات بنجاح")

            # تحديث المعلومات
            self.load_database_info()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في ضغط قاعدة البيانات: {str(e)}")

    def setup_styles(self):
        """تطبيق الأنماط"""
        style = """
        QGroupBox {
            font-weight: bold;
            border: 2px solid #cccccc;
            border-radius: 5px;
            margin-top: 1ex;
            padding-top: 10px;
        }

        QGroupBox::title {
            subcontrol-origin: margin;
            left: 10px;
            padding: 0 5px 0 5px;
        }

        QPushButton {
            background-color: #3498db;
            color: white;
            border: none;
            padding: 8px;
            border-radius: 4px;
            font-weight: bold;
        }

        QPushButton:hover {
            background-color: #2980b9;
        }

        QPushButton:pressed {
            background-color: #21618c;
        }

        QLineEdit, QComboBox, QSpinBox {
            padding: 5px;
            border: 1px solid #bdc3c7;
            border-radius: 3px;
        }

        QLineEdit:focus, QComboBox:focus, QSpinBox:focus {
            border-color: #3498db;
        }

        QTabWidget::pane {
            border: 1px solid #bdc3c7;
            background-color: white;
        }

        QTabBar::tab {
            background-color: #ecf0f1;
            padding: 8px 16px;
            margin-right: 2px;
            border-top-left-radius: 4px;
            border-top-right-radius: 4px;
        }

        QTabBar::tab:selected {
            background-color: white;
            border-bottom: 2px solid #3498db;
        }

        QTabBar::tab:hover {
            background-color: #d5dbdb;
        }
        """

        self.setStyleSheet(style)
