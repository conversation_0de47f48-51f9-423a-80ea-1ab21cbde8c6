# 📱 دليل إعداد تليجرام للنظام

## مطور خصيصاً لمستر أحمد عادل - معلم الجغرافيا والتاريخ

---

## 🎯 **نظرة عامة:**

تم إضافة ميزة الربط مع تليجرام لرفع الفيديوهات التعليمية مباشرة من الهاتف أو الكمبيوتر إلى النظام مع تشفير وحماية تلقائية.

---

## 🤖 **الخطوة الأولى: إنشاء بوت تليجرام**

### **1. البحث عن BotFather:**
- افتح تليجرام
- ابحث عن: `@BotFather`
- اضغط على النتيجة الأولى (مع العلامة الزرقاء)

### **2. إنشاء البوت:**
- أرسل: `/newbot`
- أدخل اسم البوت (مثال: `مستر أحمد - الفيديوهات التعليمية`)
- أدخل اسم المستخدم للبوت (مثال: `ahmed_geography_bot`)
- **احفظ التوكن المُعطى** (مثال: `1234567890:ABCdefGHIjklMNOpqrsTUVwxyz`)

### **3. إعدادات البوت:**
- أرسل: `/setdescription` ← وصف البوت
- أرسل: `/setabouttext` ← معلومات البوت
- أرسل: `/setuserpic` ← صورة البوت

---

## 📺 **الخطوة الثانية: إنشاء قناة أو مجموعة**

### **1. إنشاء قناة:**
- اضغط على "قناة جديدة"
- أدخل اسم القناة (مثال: `فيديوهات مستر أحمد التعليمية`)
- اختر "قناة عامة" أو "قناة خاصة"
- احفظ رابط القناة (مثال: `@ahmed_geography`)

### **2. إضافة البوت للقناة:**
- اذهب لإعدادات القناة
- اضغط "المشرفون"
- اضغط "إضافة مشرف"
- ابحث عن اسم البوت وأضفه
- **امنحه صلاحيات النشر والحذف**

---

## 🔗 **الخطوة الثالثة: ربط النظام بتليجرام**

### **1. فتح النظام:**
- شغل `RUN_WITH_TELEGRAM.bat`
- سجل دخول بـ `admin` / `admin123`
- اذهب لـ **"إدارة الفيديوهات"**
- اضغط تبويب **"الربط بتليجرام"**

### **2. إدخال البيانات:**
- **توكن البوت:** الصق التوكن من BotFather
- **معرف القناة:** أدخل `@ahmed_geography` (أو المعرف الخاص بك)
- **اسم البوت:** اختياري

### **3. اختبار الاتصال:**
- اضغط **"اختبار الاتصال"**
- يجب أن تظهر رسالة "🟢 متصل بنجاح"
- اضغط **"حفظ الإعدادات"**

---

## 📤 **الخطوة الرابعة: رفع الفيديوهات**

### **1. تنسيق الوصف المطلوب:**
عند رفع فيديو في تليجرام، أضف وصف بهذا التنسيق:

```
العنوان: الجغرافيا - الدرس الأول
المادة: جغرافيا
الصف: الثاني الثانوي
الترم: الترم الأول
```

### **2. خطوات الرفع:**
1. **أرسل الفيديو** للقناة/البوت
2. **أضف الوصف** بالتنسيق المطلوب
3. في النظام: اذهب لـ **"الربط بتليجرام"**
4. اضغط **"تحديث الفيديوهات من تليجرام"**
5. **سيتم تشفير الفيديوهات تلقائياً**

### **3. المزامنة التلقائية:**
- فعّل **"مزامنة تلقائية كل 5 دقائق"**
- سيتم جلب الفيديوهات الجديدة تلقائياً
- تشفير وحماية تلقائية

---

## 🛡️ **الحماية والأمان:**

### **الفيديوهات المجلبة من تليجرام:**
- ✅ **تشفير تلقائي** بـ AES-256
- ✅ **حد أقصى 3 مشاهدات** لكل طالب
- ✅ **منع تسجيل الشاشة** والنسخ
- ✅ **امتحانات تلقائية** بعد المشاهدة
- ✅ **تتبع مشاهدات الطلاب**

### **حماية البوت:**
- 🔒 **لا تشارك التوكن** مع أحد
- 🔒 **احتفظ بالتوكن** في مكان آمن
- 🔒 **غيّر التوكن** إذا تم تسريبه
- 🔒 **راقب نشاط البوت** من السجل

---

## 📊 **المميزات المتقدمة:**

### **1. سجل الأنشطة:**
- تتبع جميع العمليات
- رسائل الأخطاء والنجاح
- تاريخ ووقت كل عملية

### **2. الإحصائيات:**
- عدد الفيديوهات المجلبة
- إجمالي المشاهدات
- الطلاب النشطين
- الامتحانات المكتملة

### **3. إدارة الفيديوهات:**
- معاينة الفيديوهات المجلبة
- تعديل معلومات الفيديو
- حذف الفيديوهات
- تصدير التقارير

---

## 🔧 **حل المشاكل الشائعة:**

### **مشكلة: "فشل الاتصال"**
- تأكد من صحة التوكن
- تأكد من الاتصال بالإنترنت
- تأكد من أن البوت لم يتم حذفه

### **مشكلة: "لا يمكن جلب الفيديوهات"**
- تأكد من إضافة البوت كمشرف للقناة
- تأكد من صحة معرف القناة
- تأكد من وجود فيديوهات في القناة

### **مشكلة: "تنسيق الوصف خاطئ"**
- اتبع التنسيق بدقة
- تأكد من وجود النقطتين (:)
- تأكد من أسماء المواد الصحيحة

---

## 📱 **نصائح للاستخدام الأمثل:**

### **للمعلم:**
- استخدم أسماء واضحة للفيديوهات
- اتبع تنسيق الوصف بدقة
- راجع السجل بانتظام
- احتفظ بنسخة احتياطية من الإعدادات

### **للطلاب:**
- الفيديوهات محمية ولا يمكن نسخها
- 3 مشاهدات فقط لكل فيديو
- امتحان إجباري بعد كل فيديو
- أي محاولة تسجيل = إغلاق فوري

---

## 🎓 **الدعم الفني:**

**تم التطوير بواسطة:** م/ حسام أسامة  
**مهندس برمجيات - مطور تطبيقات**  
**مصمم خصيصاً لمستر أحمد عادل**

---

## 🏆 **النتيجة:**

**أول نظام إدارة طلاب عربي مع ربط تليجرام! 🚀**

- 📱 رفع سهل من الهاتف
- 🔐 حماية متقدمة
- 🤖 بوت ذكي
- 🔄 مزامنة تلقائية
- 📊 إحصائيات مفصلة

**النظام جاهز للاستخدام! 🎉**
